<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RombelEkstrakurikuler extends Model
{
    use HasFactory;

    // Tentukan nama tabel yang benar
    protected $table = 'rombel_ekstrakurikulers';

    protected $fillable = [
        'nama_rombel',
        'ekstrakurikuler',
        'tahun_ajaran',
        'pembina',
    ];

    // Relasi ke anggota
    public function anggota()
    {
        return $this->hasMany(AnggotaEkstrakurikuler::class, 'rombel_ekstrakurikuler_id');
    }
}


