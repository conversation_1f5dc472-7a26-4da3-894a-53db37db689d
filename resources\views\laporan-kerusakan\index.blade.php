@extends('adminlte::page')

@section('title', 'Laporan Kerusakan')

@section('content_header')
    <h1><PERSON><PERSON><PERSON></h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-12">
            @if(auth()->user()->hasPermissionTo('buat-laporan-kerusakan'))
            <a href="{{ route('laporan-kerusakan.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Buat Laporan Baru
            </a>
            @endif
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Daftar Laporan Kerusakan</h3>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif
                    
                    @if(session('error'))
                        <div class="alert alert-danger">{{ session('error') }}</div>
                    @endif
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Judul</th>
                                    <th>Lokasi</th>
                                    <th>Status</th>
                                    <th>Pelapor</th>
                                    <th>Tanggal Lapor</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($laporanKerusakan ?? [] as $index => $laporan)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>{{ $laporan->judul }}</td>
                                    <td>{{ $laporan->lokasi }}</td>
                                    <td>
                                        @if($laporan->status == 'dilaporkan')
                                            <span class="badge badge-warning">Dilaporkan</span>
                                        @elseif($laporan->status == 'diproses')
                                            <span class="badge badge-info">Diproses</span>
                                        @elseif($laporan->status == 'selesai')
                                            <span class="badge badge-success">Selesai</span>
                                        @endif
                                    </td>
                                    <td>{{ $laporan->pelapor->name ?? 'N/A' }}</td>
                                    <td>{{ $laporan->tanggal_lapor ? $laporan->tanggal_lapor->format('d/m/Y H:i') : 'N/A' }}</td>
                                    <td>
                                        <a href="{{ route('laporan-kerusakan.show', $laporan) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center">Tidak ada data laporan kerusakan</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
<style>
    /* Custom CSS jika diperlukan */
</style>
@stop

@section('js')
<script>
    $(document).ready(function() {
        // DataTable initialization jika diperlukan
        $('table').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
        });
    });
</script>
@stop
