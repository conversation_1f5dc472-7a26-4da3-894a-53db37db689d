

@extends('adminlte::page')

@section('title', '<PERSON>na dan <PERSON>')

@section('content_header')
    <h1><PERSON><PERSON> dan <PERSON></h1>
@stop

@section('content')
    <div class="row">
        <!-- Statistik Sarana -->
        <div class="col-12">
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ $statistik['total'] }}</h3>
                            <p>Total Sarana</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-building"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ $statistik['baik'] }}</h3>
                            <p><PERSON><PERSON><PERSON></p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ $statistik['rusak_ringan'] }}</h3>
                            <p>Rusak Ringan</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>{{ $statistik['rusak_berat'] }}</h3>
                            <p>Rusak Berat</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabel Sarana -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Daftar Sarana dan Prasarana</h3>
                    <div class="card-tools">
                        {{-- Tombol Lihat Lokasi - Semua pengguna dengan permission view-sarpras dapat melihat --}}
                        <a href="{{ route('sarana.lokasi') }}" class="btn btn-info mr-2">
                            <i class="fas fa-map-marker-alt"></i> Lihat Lokasi
                        </a>
                        
                        {{-- Tombol Tambah Sarana - Hanya muncul jika pengguna memiliki permission create-sarpras --}}
                        @can('create-sarpras')
                        <a href="{{ route('sarana.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Tambah Sarana
                        </a>
                        @endcan
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <form action="{{ route('sarana.index') }}" method="GET" class="form-inline">
                                <div class="form-group mr-2">
                                    <select name="unit_id" class="form-control">
                                        <option value="">-- Semua Unit --</option>
                                        @foreach($units as $unit)
                                            <option value="{{ $unit->id }}" {{ request('unit_id') == $unit->id ? 'selected' : '' }}>
                                                {{ $unit->nama_unit }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group mr-2">
                                    <select name="gedung_id" class="form-control">
                                        <option value="">-- Semua Lokasi --</option>
                                        @foreach($gedungs as $gedung)
                                            <option value="{{ $gedung->id }}" {{ request('gedung_id') == $gedung->id ? 'selected' : '' }}>
                                                {{ $gedung->nama_gedung }} ({{ $gedung->unit->nama_unit }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group mr-2">
                                    <select name="kondisi" class="form-control">
                                        <option value="">-- Semua Kondisi --</option>
                                        <option value="Baik" {{ request('kondisi') == 'Baik' ? 'selected' : '' }}>Baik</option>
                                        <option value="Rusak Ringan" {{ request('kondisi') == 'Rusak Ringan' ? 'selected' : '' }}>Rusak Ringan</option>
                                        <option value="Rusak Berat" {{ request('kondisi') == 'Rusak Berat' ? 'selected' : '' }}>Rusak Berat</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary mr-2">Filter</button>
                                <a href="{{ route('sarana.index') }}" class="btn btn-secondary">Reset</a>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Statistik -->
                     <!-- 
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3>{{ $statistik['total'] }}</h3>
                                    <p>Total Sarana</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-boxes"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="small-box bg-success">
                                <div class="inner">
                                    <h3>{{ $statistik['baik'] }}</h3>
                                    <p>Kondisi Baik</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-check"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="small-box bg-warning">
                                <div class="inner">
                                    <h3>{{ $statistik['rusak_ringan'] }}</h3>
                                    <p>Rusak Ringan</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-exclamation"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="small-box bg-danger">
                                <div class="inner">
                                    <h3>{{ $statistik['rusak_berat'] }}</h3>
                                    <p>Rusak Berat</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-times"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    -->
                    
                    <!-- Tabel sarana -->
                    <div class="table-responsive">
                        @if(session('success'))
                            <div class="alert alert-success alert-dismissible">
                                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                                <h5><i class="icon fas fa-check"></i> Sukses!</h5>
                                {{ session('success') }}
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="alert alert-danger alert-dismissible">
                                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                                <h5><i class="icon fas fa-ban"></i> Error!</h5>
                                {{ session('error') }}
                            </div>
                        @endif

                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th style="width: 10px">#</th>
                                    <th>Nama Sarana</th>
                                    <th>No. Barang</th>
                                    <th>Jenis</th>
                                    <th>Jumlah</th>
                                    <th>Kondisi</th>
                                    <th>Unit</th>
                                    <th>Lokasi</th>
                                    <th>Tahun Pengadaan</th>
                                    <th style="width: 150px">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($sarana as $index => $item)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $item->nama_sarana }}</td>
                                        <td>{{ $item->no_barang ?? '-' }}</td>
                                        <td>{{ $item->jenis }}</td>
                                        <td>{{ $item->jumlah }}</td>
                                        <td>
                                            @if($item->kondisi == 'Baik')
                                                <span class="badge bg-success">{{ $item->kondisi }}</span>
                                            @elseif($item->kondisi == 'Rusak Ringan')
                                                <span class="badge bg-warning">{{ $item->kondisi }}</span>
                                            @else
                                                <span class="badge bg-danger">{{ $item->kondisi }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $item->unit->nama_unit ?? '-' }}</td>
                                        <td>{{ $item->gedung->nama_gedung ?? '-' }}</td>
                                        <td>{{ $item->tahun_pengadaan ?? '-' }}</td>
                                        <td>
                                            {{-- Tombol Lihat Detail - Semua pengguna dengan permission view-sarpras dapat melihat --}}
                                            <a href="{{ route('sarana.show', $item->id) }}" class="btn btn-info btn-sm" title="Lihat Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            {{-- Tombol Edit - Hanya muncul jika pengguna memiliki permission edit-sarpras --}}
                                            @can('edit-sarpras')
                                            <a href="{{ route('sarana.edit', $item->id) }}" class="btn btn-warning btn-sm" title="Edit Data">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @endcan
                                            
                                            {{-- Tombol Hapus - Hanya muncul jika pengguna memiliki permission delete-sarpras --}}
                                            @can('delete-sarpras')
                                            <form action="{{ route('sarana.destroy', $item->id) }}" method="POST" style="display: inline-block;">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')" title="Hapus Data">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            @endcan
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="10" class="text-center">Tidak ada data sarana</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
    <link rel="stylesheet" href="{{ asset('css/admin_custom.css') }}">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            // Inisialisasi DataTables
            $('table').DataTable({
                "paging": true,
                "lengthChange": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                }
            });
        });
    </script>
@stop





