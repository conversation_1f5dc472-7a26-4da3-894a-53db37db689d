<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class VerifyUserPermissions extends Command
{
    protected $signature = 'users:verify-permissions {user_id?}';
    protected $description = 'Verify user permissions and roles';

    public function handle()
    {
        $userId = $this->argument('user_id');
        
        if ($userId) {
            $users = User::where('id', $userId)->get();
        } else {
            $users = User::all();
        }

        foreach ($users as $user) {
            $this->info("User: {$user->name}");
            $this->info("Direct Permissions: " . $user->getDirectPermissions()->pluck('name')->join(', '));
            $this->info("Permissions via Roles: " . $user->getPermissionsViaRoles()->pluck('name')->join(', '));
            $this->info("All Permissions: " . $user->getAllPermissions()->pluck('name')->join(', '));
            $this->info("Roles: " . $user->getRoleNames()->join(', '));
            $this->info("------------------------");
        }
    }
}
