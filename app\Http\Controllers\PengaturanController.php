<?php

namespace App\Http\Controllers;

use App\Models\Jenjang;
use App\Models\Kelas;
use App\Models\MataPelajaran;
use App\Models\User;
use App\Models\Gedung;
use App\Models\Unit;
use App\Models\TahunAjaran;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * Controller untuk mengelola pengaturan aplikasi
 * Termasuk jenjang, kelas, mata pelajaran, gedung, dll
 */
class PengaturanController extends Controller
{
    /**
     * Menampilkan halaman daftar jenjang pendidikan
     * 
     * @return \Illuminate\View\View
     */
    public function jenjang()
    {
        $jenjangs = Jenjang::all();
        return view('pengaturan.jenjang', compact('jenjangs'));
    }

    /**
     * Menyimpan data jenjang baru ke database
     * 
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function jenjangStore(Request $request)
    {
        $request->validate([
            'jenjang' => 'required|in:PG,SD,SMP,SMA',
            'tingkat' => 'required|string'
        ]);

        try {
            DB::beginTransaction();
            Jenjang::create($request->all());
            DB::commit();

            return redirect()->route('pengaturan.jenjang')
                ->with('success', 'Data jenjang berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('pengaturan.jenjang')
                ->with('error', 'Terjadi kesalahan saat menambahkan data jenjang');
        }
    }

    /**
     * Memperbarui data jenjang yang sudah ada
     * 
     * @param Request $request
     * @param Jenjang $jenjang
     * @return \Illuminate\Http\RedirectResponse
     */
    public function jenjangUpdate(Request $request, Jenjang $jenjang)
    {
        $request->validate([
            'jenjang' => 'required|in:PG,SD,SMP,SMA',
            'tingkat' => 'required|string'
        ]);

        try {
            DB::beginTransaction();
            $jenjang->update($request->all());
            DB::commit();

            return redirect()->route('pengaturan.jenjang')
                ->with('success', 'Data jenjang berhasil diperbarui');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('pengaturan.jenjang')
                ->with('error', 'Terjadi kesalahan saat memperbarui data jenjang');
        }
    }

    /**
     * Menghapus data jenjang dari database
     * 
     * @param Jenjang $jenjang
     * @return \Illuminate\Http\RedirectResponse
     */
    public function jenjangDestroy(Jenjang $jenjang)
    {
        try {
            DB::beginTransaction();
            $jenjang->delete();
            DB::commit();

            return redirect()->route('pengaturan.jenjang')
                ->with('success', 'Data jenjang berhasil dihapus');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('pengaturan.jenjang')
                ->with('error', 'Terjadi kesalahan saat menghapus data jenjang');
        }
    }

    /**
     * Menampilkan halaman daftar kelas
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function kelas(Request $request)
    {
        // Ambil semua unit
        $units = Unit::all();
        
        // Ambil semua jenjang
        $jenjangs = Jenjang::all();
        
        // Ambil semua gedung
        $gedungs = Gedung::all();
        
        // Ambil semua guru
        $guru = User::where('role', 'Guru')->get();
        
        // Ambil tahun ajaran dari database atau gunakan fallback
        $tahunAjaranList = TahunAjaran::pluck('nama')->toArray();
        if (empty($tahunAjaranList)) {
            $tahunAjaranList = $this->getTahunAjaranList();
        }
        
        // Tentukan tahun ajaran yang dipilih
        $tahunAjaranTerpilih = $request->tahun_ajaran ?? TahunAjaran::where('aktif', true)->value('nama');
        if (!$tahunAjaranTerpilih && !empty($tahunAjaranList)) {
            $tahunAjaranTerpilih = $tahunAjaranList[0];
        }
        
        // Query dasar
        $query = Kelas::with(['jenjang', 'gedung', 'unit'])
            ->where('tahun_ajaran', $tahunAjaranTerpilih);
        
        // Filter berdasarkan unit jika ada
        if ($request->has('unit') && $request->unit) {
            $query->where('unit_id', $request->unit);
        }
        
        // Tambahkan orderBy untuk mengurutkan data
        $kelas = $query->orderBy('jenjang_id')
            ->orderBy('nama')
            ->get();
        
        return view('pengaturan.kelas', compact(
            'units',
            'jenjangs', 
            'kelas', 
            'tahunAjaranList',
            'tahunAjaranTerpilih',
            'gedungs',
            'guru'
        ));
    }

    /**
     * Menyimpan data kelas baru ke database
     * 
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function kelasStore(Request $request)
    {
        $validated = $request->validate([
            'unit_id' => 'required|exists:units,id',
            'jenjang_id' => 'required|exists:jenjangs,id',
            'gedung_id' => 'required|exists:gedungs,id',
            'nama' => 'required|string|max:20',
            'tahun_ajaran' => 'required|string'
        ], [
            'unit_id.required' => 'Unit harus dipilih',
            'jenjang_id.required' => 'Jenjang harus dipilih',
            'gedung_id.required' => 'Ruang harus dipilih',
            'nama.required' => 'Nama kelas harus diisi',
            'tahun_ajaran.required' => 'Tahun ajaran harus dipilih'
        ]);

        try {
            DB::beginTransaction();
            
            // Jika tingkat tidak diisi, ambil dari jenjang
            $jenjang = Jenjang::find($validated['jenjang_id']);
            if ($jenjang) {
                $validated['tingkat'] = $jenjang->tingkat;
            }
            
            // Debugging
            \Log::info('Data yang akan disimpan:', $validated);
            
            Kelas::create($validated);
            
            DB::commit();
            
            return redirect()->route('pengaturan.kelas')
                ->with('success', 'Data kelas berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('Error saat menyimpan kelas: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            return redirect()->route('pengaturan.kelas')
                ->with('error', 'Terjadi kesalahan saat menambahkan data kelas: ' . $e->getMessage());
        }
    }

    /**
     * Memperbarui data kelas yang sudah ada
     * 
     * @param Request $request
     * @param Kelas $kelas
     * @return \Illuminate\Http\RedirectResponse
     */
    public function kelasUpdate(Request $request, Kelas $kelas)
    {
        $request->validate([
            'unit_id' => 'required|exists:units,id',
            'jenjang_id' => 'required|exists:jenjangs,id',
            'gedung_id' => 'required|exists:gedungs,id',
            'nama' => 'required|string|max:20',
            'tahun_ajaran' => 'required|string'
        ]);

        try {
            DB::beginTransaction();
            $kelas->update($request->all());
            DB::commit();

            return redirect()->route('pengaturan.kelas')
                ->with('success', 'Data kelas berhasil diperbarui');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('pengaturan.kelas')
                ->with('error', 'Terjadi kesalahan saat memperbarui data kelas');
        }
    }

    /**
     * Menghapus data kelas dari database
     * 
     * @param Kelas $kelas
     * @return \Illuminate\Http\RedirectResponse
     */
    public function kelasDestroy(Kelas $kelas)
    {
        try {
            DB::beginTransaction();
            $kelas->delete();
            DB::commit();

            return redirect()->route('pengaturan.kelas')
                ->with('success', 'Data kelas berhasil dihapus');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('pengaturan.kelas')
                ->with('error', 'Terjadi kesalahan saat menghapus data kelas');
        }
    }

    /**
     * Mendapatkan tahun ajaran saat ini
     * 
     * @return array
     */
    private function getTahunAjaran()
    {
        $tahunSekarang = Carbon::now()->year;
        $bulanSekarang = Carbon::now()->month;
        $tahunMulai = $bulanSekarang >= 7 ? $tahunSekarang : $tahunSekarang - 1;
        return [$tahunMulai . '/' . ($tahunMulai + 1)];
    }

    /**
     * Mendapatkan daftar tahun ajaran sebagai fallback
     * jika tidak ada data di database
     * 
     * @return array
     */
    private function getTahunAjaranList()
    {
        // Metode ini bisa dipertahankan sebagai fallback jika tidak ada data di database
        $tahunSekarang = Carbon::now()->year;
        $tahunAjaranList = [];
        
        for ($i = 0; $i < 5; $i++) {
            $tahun = $tahunSekarang - $i;
            $tahunAjaranList[] = $tahun . '/' . ($tahun + 1);
        }
        
        return $tahunAjaranList;
    }

    /**
     * Menampilkan halaman daftar mata pelajaran
     * 
     * @return \Illuminate\View\View
     */
    public function mapel()
    {
        $units = Unit::all();
        $pengajars = User::where('role', 'Guru')->get();
        $mapels = MataPelajaran::with(['unit', 'pengajar'])->get();
        
        return view('pengaturan.mapel', compact('units', 'pengajars', 'mapels'));
    }

    /**
     * Menyimpan data mata pelajaran baru ke database
     * 
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function mapelStore(Request $request)
    {
        $validated = $request->validate([
            'unit_id' => 'required',
            'nama_mapel' => 'required',
            'pengajar_id' => 'required'
        ]);

        try {
            DB::beginTransaction();
            // Hapus baris ini karena kolom created_by tidak ada
            // $validated['created_by'] = auth()->id();
            
            $mapel = MataPelajaran::create($validated);
            DB::commit();

            return redirect()->route('pengaturan.mapel')
                ->with('success', 'Data mata pelajaran berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollback();
            
            // Tambahkan logging untuk debugging
            \Log::error('Error creating mapel: ' . $e->getMessage());
            
            return redirect()->route('pengaturan.mapel')
                ->with('error', 'Terjadi kesalahan saat menambahkan data mata pelajaran: ' . $e->getMessage());
        }
    }

    /**
     * Memperbarui data mata pelajaran yang sudah ada
     * 
     * @param Request $request
     * @param MataPelajaran $mapel
     * @return \Illuminate\Http\RedirectResponse
     */
    public function mapelUpdate(Request $request, MataPelajaran $mapel)
    {
        $validated = $request->validate([
            'unit_id' => 'required',
            'nama_mapel' => 'required',
            'pengajar_id' => 'required'
        ]);

        try {
            DB::beginTransaction();
            $mapel->update($validated);
            DB::commit();

            return redirect()->route('pengaturan.mapel')
                ->with('success', 'Data mata pelajaran berhasil diupdate');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('pengaturan.mapel')
                ->with('error', 'Terjadi kesalahan saat memperbarui data mata pelajaran');
        }
    }

    /**
     * Menghapus data mata pelajaran dari database
     * 
     * @param MataPelajaran $mapel
     * @return \Illuminate\Http\RedirectResponse
     */
    public function mapelDestroy(MataPelajaran $mapel)
    {
        try {
            DB::beginTransaction();
            $mapel->delete();
            DB::commit();

            return redirect()->route('pengaturan.mapel')
                ->with('success', 'Data mata pelajaran berhasil dihapus');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('pengaturan.mapel')
                ->with('error', 'Terjadi kesalahan saat menghapus data mata pelajaran');
        }
    }

    /**
     * Mengambil data pengajar berdasarkan jenjang
     * 
     * @param int $jenjang ID jenjang
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPengajarByJenjang($jenjang)
    {
        $pengajars = User::where('role', 'Guru')
                        ->whereHas('unitRelation', function($query) use ($jenjang) {
                            $query->where('jenjang_id', $jenjang);
                        })
                        ->get(['id', 'name']);
                    
        return response()->json($pengajars);
    }

    /**
     * Menampilkan halaman daftar gedung
     * 
     * @return \Illuminate\View\View
     */
    public function gedung()
    {
        $gedungs = Gedung::with('unit')->get();
        $units = Unit::all();
        return view('pengaturan.gedung', compact('gedungs', 'units'));
    }

    /**
     * Menyimpan data gedung baru ke database
     * 
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function gedungStore(Request $request)
    {
        DB::beginTransaction();
        try {
            $validated = $request->validate([
                'unit_id' => 'required|exists:units,id',
                'nama_gedung' => 'required|string|max:255',
                'keterangan' => 'nullable|string'
            ], [
                'unit_id.required' => 'Unit harus dipilih',
                'unit_id.exists' => 'Unit tidak valid',
                'nama_gedung.required' => 'Nama ruang harus diisi'
            ]);

            Gedung::create($validated);
            DB::commit();

            return redirect()->route('pengaturan.gedung')
                ->with('success', 'Data ruang berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('pengaturan.gedung')
                ->with('error', 'Terjadi kesalahan saat menambah data ruang');
        }
    }

    /**
     * Memperbarui data gedung yang sudah ada
     * 
     * @param Request $request
     * @param Gedung $gedung
     * @return \Illuminate\Http\RedirectResponse
     */
    public function gedungUpdate(Request $request, Gedung $gedung)
    {
        try {
            $validated = $request->validate([
                'unit_id' => 'required|exists:units,id',
                'nama_gedung' => 'required|string|max:255',
                'keterangan' => 'nullable|string'
            ], [
                'unit_id.required' => 'Unit harus dipilih',
                'unit_id.exists' => 'Unit tidak valid',
                'nama_gedung.required' => 'Nama ruang harus diisi'
            ]);

            $gedung->update($validated);

            return redirect()->route('pengaturan.gedung')
                ->with('success', 'Data ruang berhasil diperbarui');
        } catch (\Exception $e) {
            return redirect()->route('pengaturan.gedung')
                ->with('error', 'Terjadi kesalahan saat memperbarui data ruang');
        }
    }

    /**
     * Menghapus data gedung dari database
     * 
     * @param Gedung $gedung
     * @return \Illuminate\Http\RedirectResponse
     */
    public function gedungDestroy(Gedung $gedung)
    {
        try {
            $gedung->delete();
            return redirect()->route('pengaturan.gedung')
                ->with('success', 'Data gedung berhasil dihapus');
        } catch (\Exception $e) {
            return redirect()->route('pengaturan.gedung')
                ->with('error', 'Terjadi kesalahan saat menghapus data gedung');
        }
    }

    /**
     * Mendapatkan jenjang berdasarkan unit
     * 
     * @param Unit $unit
     * @return \Illuminate\Http\JsonResponse
     */
    public function getJenjangsByUnit(Unit $unit)
    {
        try {
            // Log untuk debugging
            \Log::info('Mencari jenjang untuk unit: ' . $unit->id . ' - ' . $unit->nama);
            
            // Cek apakah unit memiliki jenjang_id
            if ($unit->jenjang_id) {
                // Jika jenjang_id adalah ID numerik
                if (is_numeric($unit->jenjang_id)) {
                    $jenjangs = Jenjang::where('id', $unit->jenjang_id)->get();
                } 
                // Jika jenjang_id adalah kode jenjang (PG, SD, SMP, SMA)
                else {
                    $jenjangs = Jenjang::where('jenjang', $unit->jenjang_id)->get();
                }
            } else {
                // Jika unit tidak memiliki jenjang_id, ambil semua jenjang
                $jenjangs = Jenjang::all();
            }
            
            // Tambahkan nama_jenjang jika belum ada
            $jenjangs->each(function($jenjang) {
                if (!isset($jenjang->nama_jenjang)) {
                    $jenjang->nama_jenjang = $jenjang->jenjang . ' ' . $jenjang->tingkat;
                }
            });
            
            \Log::info('Jenjang ditemukan: ' . $jenjangs->count(), $jenjangs->toArray());
            
            return response()->json($jenjangs);
        } catch (\Exception $e) {
            \Log::error('Error saat mengambil jenjang: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Mendapatkan gedung berdasarkan unit
     */
    public function getGedungsByUnit(Unit $unit)
    {
        try {
            // Log untuk debugging
            \Log::info('Mencari gedung untuk unit: ' . $unit->id . ' - ' . $unit->nama);
            
            $gedungs = Gedung::where('unit_id', $unit->id)->get();
            
            // Log untuk debugging
            \Log::info('Gedung ditemukan: ' . $gedungs->count(), $gedungs->toArray());
            
            // Jika tidak ada gedung, kembalikan array kosong dengan pesan
            if ($gedungs->isEmpty()) {
                return response()->json([
                    ['id' => '', 'nama_gedung' => 'Tidak ada ruang tersedia untuk unit ini']
                ]);
            }
            
            return response()->json($gedungs);
        } catch (\Exception $e) {
            \Log::error('Error saat mengambil gedung: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}







