<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>

    
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    
    <?php echo $__env->yieldContent('meta_tags'); ?>

    
    <title>
        <?php echo $__env->yieldContent('title_prefix', config('adminlte.title_prefix', '')); ?>
        <?php echo $__env->yieldContent('title', config('adminlte.title', 'AdminLTE 3')); ?>
        <?php echo $__env->yieldContent('title_postfix', config('adminlte.title_postfix', '')); ?>
    </title>

    
    <?php echo $__env->yieldContent('adminlte_css_pre'); ?>

    
    <?php if(!config('adminlte.enabled_laravel_mix')): ?>
        <link rel="stylesheet" href="<?php echo e(asset('vendor/fontawesome-free/css/all.min.css')); ?>">
        <link rel="stylesheet" href="<?php echo e(asset('vendor/overlayScrollbars/css/OverlayScrollbars.min.css')); ?>">
        <link rel="stylesheet" href="<?php echo e(asset('vendor/adminlte/dist/css/adminlte.min.css')); ?>">

        <?php if(config('adminlte.google_fonts.allowed', true)): ?>
            <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700,300italic,400italic,600italic">
        <?php endif; ?>
    <?php else: ?>
        <link rel="stylesheet" href="<?php echo e(mix(config('adminlte.laravel_mix_css_path', 'css/app.css'))); ?>">
    <?php endif; ?>

    
    <?php echo $__env->make('adminlte::plugins', ['type' => 'css'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    
    <?php if(config('adminlte.livewire')): ?>
        <?php if(app()->version() >= 7): ?>
            @livewireStyles
        <?php else: ?>
            <livewire:styles />
        <?php endif; ?>
    <?php endif; ?>

    
    <?php echo $__env->yieldContent('adminlte_css'); ?>

    
    <?php if(config('adminlte.use_ico_only')): ?>
        <link rel="shortcut icon" href="<?php echo e(asset('favicons/favicon.ico')); ?>" />
    <?php elseif(config('adminlte.use_full_favicon')): ?>
        <link rel="shortcut icon" href="<?php echo e(asset('favicons/favicon.ico')); ?>" />
        <link rel="apple-touch-icon" sizes="57x57" href="<?php echo e(asset('favicons/apple-icon-57x57.png')); ?>">
        <link rel="apple-touch-icon" sizes="60x60" href="<?php echo e(asset('favicons/apple-icon-60x60.png')); ?>">
        <link rel="apple-touch-icon" sizes="72x72" href="<?php echo e(asset('favicons/apple-icon-72x72.png')); ?>">
        <link rel="apple-touch-icon" sizes="76x76" href="<?php echo e(asset('favicons/apple-icon-76x76.png')); ?>">
        <link rel="apple-touch-icon" sizes="114x114" href="<?php echo e(asset('favicons/apple-icon-114x114.png')); ?>">
        <link rel="apple-touch-icon" sizes="120x120" href="<?php echo e(asset('favicons/apple-icon-120x120.png')); ?>">
        <link rel="apple-touch-icon" sizes="144x144" href="<?php echo e(asset('favicons/apple-icon-144x144.png')); ?>">
        <link rel="apple-touch-icon" sizes="152x152" href="<?php echo e(asset('favicons/apple-icon-152x152.png')); ?>">
        <link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(asset('favicons/apple-icon-180x180.png')); ?>">
        <link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(asset('favicons/favicon-16x16.png')); ?>">
        <link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(asset('favicons/favicon-32x32.png')); ?>">
        <link rel="icon" type="image/png" sizes="96x96" href="<?php echo e(asset('favicons/favicon-96x96.png')); ?>">
        <link rel="icon" type="image/png" sizes="192x192"  href="<?php echo e(asset('favicons/android-icon-192x192.png')); ?>">
        <link rel="manifest" crossorigin="use-credentials" href="<?php echo e(asset('favicons/manifest.json')); ?>">
        <meta name="msapplication-TileColor" content="#ffffff">
        <meta name="msapplication-TileImage" content="<?php echo e(asset('favicon/ms-icon-144x144.png')); ?>">
    <?php endif; ?>

</head>

<body class="<?php echo $__env->yieldContent('classes_body'); ?>" <?php echo $__env->yieldContent('body_data'); ?>>

    
    <?php echo $__env->yieldContent('body'); ?>

    
    <?php if(!config('adminlte.enabled_laravel_mix')): ?>
        <script src="<?php echo e(asset('vendor/jquery/jquery.min.js')); ?>"></script>
        <script src="<?php echo e(asset('vendor/bootstrap/js/bootstrap.bundle.min.js')); ?>"></script>
        <script src="<?php echo e(asset('vendor/overlayScrollbars/js/jquery.overlayScrollbars.min.js')); ?>"></script>
        <script src="<?php echo e(asset('vendor/adminlte/dist/js/adminlte.min.js')); ?>"></script>
    <?php else: ?>
        <script src="<?php echo e(mix(config('adminlte.laravel_mix_js_path', 'js/app.js'))); ?>"></script>
    <?php endif; ?>

    
    <?php echo $__env->make('adminlte::plugins', ['type' => 'js'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    
    <?php if(config('adminlte.livewire')): ?>
        <?php if(app()->version() >= 7): ?>
            @livewireScripts
        <?php else: ?>
            <livewire:scripts />
        <?php endif; ?>
    <?php endif; ?>

    
    <?php echo $__env->yieldContent('adminlte_js'); ?>

    <!-- Notifikasi Modal -->
    <?php if(auth()->guard()->check()): ?>
    <div class="modal fade" id="notifikasiModal" tabindex="-1" role="dialog" aria-labelledby="notifikasiModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="notifikasiModalLabel">Notifikasi Baru</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="notifikasi-list-popup" class="list-group">
                        <!-- Notifikasi akan dimuat di sini -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <a href="<?php echo e(route('notifikasi.mark-all-as-read')); ?>" class="btn btn-primary">Tandai Semua Dibaca</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Fungsi untuk memeriksa notifikasi yang belum dibaca
            function checkUnreadNotifications() {
                fetch('<?php echo e(route("notifikasi.count")); ?>')
                    .then(response => response.json())
                    .then(data => {
                        // Update badge di navbar
                        const badge = document.getElementById('notification-badge');
                        if (badge) {
                            badge.textContent = data.count;
                            badge.style.display = data.count > 0 ? 'inline-block' : 'none';
                        }
                        
                        // Jika ada notifikasi yang belum dibaca, tampilkan pop-up
                        if (data.count > 0) {
                            loadNotifications();
                        }
                    })
                    .catch(error => console.error('Error checking notifications:', error));
            }

            // Fungsi untuk memuat notifikasi ke dalam pop-up
            function loadNotifications() {
                fetch('<?php echo e(route("notifikasi.get-unread")); ?>')
                    .then(response => response.json())
                    .then(data => {
                        const notifikasiList = document.getElementById('notifikasi-list-popup');
                        if (!notifikasiList) return;
                        
                        notifikasiList.innerHTML = '';

                        if (data.notifikasi.length > 0) {
                            data.notifikasi.forEach(item => {
                                let icon = '';
                                if (item.jenis === 'warning') {
                                    icon = '<i class="fas fa-exclamation-triangle text-warning"></i>';
                                } else if (item.jenis === 'success') {
                                    icon = '<i class="fas fa-check-circle text-success"></i>';
                                } else if (item.jenis === 'danger') {
                                    icon = '<i class="fas fa-times-circle text-danger"></i>';
                                } else {
                                    icon = '<i class="fas fa-info-circle text-info"></i>';
                                }

                                const notifikasiItem = document.createElement('div');
                                notifikasiItem.className = 'list-group-item list-group-item-action';
                                notifikasiItem.innerHTML = `
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1">${icon} ${item.judul}</h5>
                                        <small>${item.created_at_formatted}</small>
                                    </div>
                                    <p class="mb-1">${item.pesan}</p>
                                    <div class="d-flex justify-content-end mt-2">
                                        <a href="${item.mark_as_read_url}" class="btn btn-sm btn-primary mr-2">
                                            <i class="fas fa-eye"></i> Lihat
                                        </a>
                                        <a href="${item.mark_as_read_url}" class="btn btn-sm btn-success">
                                            <i class="fas fa-check"></i> Tandai Dibaca
                                        </a>
                                    </div>
                                `;
                                notifikasiList.appendChild(notifikasiItem);
                            });

                            // Tampilkan modal
                            $('#notifikasiModal').modal('show');
                        }
                    })
                    .catch(error => console.error('Error loading notifications:', error));
            }

            // Periksa notifikasi saat halaman dimuat
            checkUnreadNotifications();

            // Periksa notifikasi setiap 5 menit (300000 ms)
            setInterval(checkUnreadNotifications, 300000);
        });
    </script>
    <?php endif; ?>

</body>

</html>

<?php /**PATH C:\xampp\htdocs\webplp\resources\views/vendor/adminlte/master.blade.php ENDPATH**/ ?>