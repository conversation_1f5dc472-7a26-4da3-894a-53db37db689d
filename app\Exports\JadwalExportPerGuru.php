<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use App\Models\User;
use App\Models\DetailJadwal;
use App\Exports\Sheets\JadwalPerGuruSheet;
use Illuminate\Support\Collection;

class JadwalExportPerGuru implements WithMultipleSheets
{
    private $jadwalList;

    public function __construct($jadwalList)
    {
        $this->jadwalList = $jadwalList;
    }

    public function sheets(): array
    {
        $sheets = [];
        
        // Dapatkan semua detail jadwal
        $allDetailJadwal = collect();
        foreach ($this->jadwalList as $jadwal) {
            $allDetailJadwal = $allDetailJadwal->concat($jadwal->detailJadwal);
        }

        // Kelompokkan berdasarkan guru
        $jadwalPerGuru = $allDetailJadwal
            ->filter(function ($detail) {
                return !$detail->is_istirahat && !$detail->keterangan && $detail->mataPelajaran;
            })
            ->groupBy(function ($detail) {
                return $detail->mataPelajaran->pengajar->id;
            });

        // Buat sheet untuk setiap guru
        foreach ($jadwalPerGuru as $guruId => $jadwalGuru) {
            $namaGuru = $jadwalGuru->first()->mataPelajaran->pengajar->name;
            $sheets[] = new JadwalPerGuruSheet($jadwalGuru, $namaGuru);
        }

        if (empty($sheets)) {
            $sheets[] = new JadwalPerGuruSheet(new Collection(), 'Tidak Ada Data');
        }

        return $sheets;
    }
}
