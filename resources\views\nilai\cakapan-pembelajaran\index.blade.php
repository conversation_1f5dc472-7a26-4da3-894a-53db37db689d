@extends('adminlte::page')

@section('title', 'Daftar Cakapan Pembelajaran')

@section('content_header')
    <h1>Daftar Cakapan Pembelajaran</h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Cakapan Pembelajaran</h3>
            <div class="card-tools">
                <a href="{{ route('penilaian.cakapan-pembelajaran.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Tambah Cakapan Pembelajaran
                </a>
            </div>
        </div>
        <div class="card-body">
            <!-- Pesan Sukses -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h5><i class="icon fas fa-check"></i> Sukses!</h5>
                    {{ session('success') }}
                </div>
            @endif

            <!-- Tabel Cakapan Pembelajaran -->
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="cakapanPembelajaranTable">
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th width="30%">Kompetensi Dasar</th>
                            <th>Deskripsi</th>
                            <th width="15%">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($cakapanPembelajarans as $index => $cp)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $cp->kompetensiDasar->deskripsi }}</td>
                                <td>{{ $cp->deskripsi }}</td>
                                <td>
                                    <a href="{{ route('penilaian.cakapan-pembelajaran.edit', $cp->id) }}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('penilaian.cakapan-pembelajaran.destroy', $cp->id) }}" method="POST" style="display: inline-block;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4" class="text-center">Tidak ada data cakapan pembelajaran</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            $('#cakapanPembelajaranTable').DataTable({
                "paging": true,
                "lengthChange": false,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
            });
        });
    </script>
@stop