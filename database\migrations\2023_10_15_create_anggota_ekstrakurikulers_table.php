<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Jalankan migrasi.
     */
    public function up(): void
    {
        Schema::create('anggota_ekstrakurikuler', function (Blueprint $table) { // Ubah nama tabel tanpa 's'
            $table->id(); // Kolom ID sebagai primary key
            $table->foreignId('rombel_ekstrakurikuler_id')
                  ->constrained('rombel_ekstrakurikuler') // Ubah referensi tabel tanpa 's'
                  ->onDelete('cascade'); // Relasi ke tabel rombel_ekstrakurikuler
            $table->foreignId('siswa_id')
                  ->constrained('peserta_didik')
                  ->onDelete('cascade'); // Relasi ke tabel peserta_didik
            $table->foreignId('kelas_id')
                  ->nullable()
                  ->constrained('kelas')
                  ->nullOnDelete(); // <PERSON><PERSON>i ke tabel kelas, bisa null jika kelas dihapus
            $table->timestamps(); // created_at dan updated_at
        });
    }

    /**
     * Batalkan migrasi.
     */
    public function down(): void
    {
        Schema::dropIfExists('anggota_ekstrakurikuler'); // Ubah nama tabel tanpa 's'
    }
};
