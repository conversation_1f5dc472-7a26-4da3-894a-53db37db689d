@extends('adminlte::page')

@section('title', '<PERSON>')

@section('content_header')
    <h1>Edit Laporan Kerja</h1>
@stop

@section('content')
<div class="container">
    <div class="card">
        <div class="card-body">
            <form action="{{ route('laporan-kerja.update', $laporanKerja->id) }}" method="POST">
                @csrf
                @method('PUT')
                
                <div class="form-group">
                    <label for="uraian_kerja">Uraian <PERSON> <span class="text-danger">*</span></label>
                    <textarea name="uraian_kerja" id="uraian_kerja" class="form-control @error('uraian_kerja') is-invalid @enderror" rows="3" required>{{ old('uraian_kerja', $laporanKerja->uraian_kerja) }}</textarea>
                    @error('uraian_kerja')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="waktu_mulai">Waktu Mulai <span class="text-danger">*</span></label>
                            <input type="text" name="waktu_mulai" id="waktu_mulai" class="form-control @error('waktu_mulai') is-invalid @enderror" value="{{ old('waktu_mulai', \Carbon\Carbon::parse($laporanKerja->waktu_mulai)->format('Y-m-d H:i')) }}" placeholder="Format: YYYY-MM-DD HH:MM" required>
                            @error('waktu_mulai')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="waktu_selesai">Waktu Selesai</label>
                            <input type="text" name="waktu_selesai" id="waktu_selesai" class="form-control @error('waktu_selesai') is-invalid @enderror" value="{{ old('waktu_selesai', $laporanKerja->waktu_selesai ? \Carbon\Carbon::parse($laporanKerja->waktu_selesai)->format('Y-m-d H:i') : '') }}" placeholder="Format: YYYY-MM-DD HH:MM">
                            @error('waktu_selesai')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="status">Status <span class="text-danger">*</span></label>
                    <select name="status" id="status" class="form-control @error('status') is-invalid @enderror" required>
                        <option value="">-- Pilih Status --</option>
                        <option value="selesai" {{ old('status', $laporanKerja->status) == 'selesai' ? 'selected' : '' }}>Selesai</option>
                        <option value="proses" {{ old('status', $laporanKerja->status) == 'proses' ? 'selected' : '' }}>Proses</option>
                        <option value="ter



