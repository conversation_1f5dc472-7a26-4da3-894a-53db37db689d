@extends('adminlte::page')

@section('title', 'Edit Data Mutasi Keluar')

@section('content_header')
    <h1>Edit Data Mutasi Keluar</h1>
@stop

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Edit Data Mutasi Keluar</h3>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('peserta-didik.update-mutasi', $siswa->id) }}" method="POST">
                            @csrf
                            @method('PUT')
                            
                            <div class="form-group">
                                <label for="nis">NIS</label>
                                <input type="text" class="form-control" id="nis" name="nis" value="{{ $siswa->nis }}" readonly>
                            </div>
                            
                            <div class="form-group">
                                <label for="nama">Nama</label>
                                <input type="text" class="form-control" id="nama" name="nama" value="{{ $siswa->nama }}" readonly>
                            </div>
                            
                            <div class="form-group">
                                <label for="kelas_terakhir">Kelas Terakhir</label>
                                <select class="form-control" id="kelas_terakhir" name="kelas_terakhir">
                                    @foreach($kelas as $k)
                                        <option value="{{ $k->id }}" {{ $siswa->riwayatKelas->first()->kelas_lama_id == $k->id ? 'selected' : '' }}>
                                            {{ $k->nama }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="tanggal_mutasi">Tanggal Mutasi</label>
                                <input type="date" class="form-control" id="tanggal_mutasi" name="tanggal_mutasi" 
                                       value="{{ $siswa->riwayatKelas->first()->tanggal_pindah ? date('Y-m-d', strtotime($siswa->riwayatKelas->first()->tanggal_pindah)) : '' }}">
                            </div>
                            
                            <div class="form-group">
                                <label for="sekolah_tujuan">Sekolah Tujuan</label>
                                <input type="text" class="form-control" id="sekolah_tujuan" name="sekolah_tujuan" 
                                       value="{{ $siswa->riwayatKelas->first()->sekolah_tujuan ?? '' }}">
                            </div>
                            
                            <div class="form-group">
                                <label for="alasan">Alasan Mutasi</label>
                                <textarea class="form-control" id="alasan" name="alasan" rows="3">{{ $siswa->riwayatKelas->first()->alasan ?? '' }}</textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">Simpan</button>
                            <a href="{{ route('peserta-didik.mutasi-keluar') }}" class="btn btn-secondary">Kembali</a>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        console.log('Hi!');
    </script>
@stop



