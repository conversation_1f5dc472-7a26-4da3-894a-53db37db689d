@extends('adminlte::page')

@section('title', 'Tambah Penilaian Sumatif')

@section('content_header')
    <h1>Tambah Penilaian Sumatif</h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Form Tambah Penilaian Sumatif</h3>
        </div>
        <div class="card-body">
            <form action="{{ route('penilaian.sumatif.store') }}" method="POST">
                @csrf
                
                <div class="row">
                    <!-- Siswa -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="siswa_id">Siswa <span class="text-danger">*</span></label>
                            <select name="siswa_id" id="siswa_id" class="form-control select2 @error('siswa_id') is-invalid @enderror" required>
                                <option value="">Pilih <PERSON></option>
                                @foreach($siswa as $s)
                                    <option value="{{ $s->id }}" {{ old('siswa_id') == $s->id ? 'selected' : '' }}>
                                        {{ $s->nama }} ({{ $s->nisn }})
                                    </option>
                                @endforeach
                            </select>
                            @error('siswa_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Kelas -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="kelas_id">Kelas <span class="text-danger">*</span></label>
                            <select name="kelas_id" id="kelas_id" class="form-control select2 @error('kelas_id') is-invalid @enderror" required>
                                <option value="">Pilih Kelas</option>
                                @foreach($kelas as $k)
                                    <option value="{{ $k->id }}" {{ old('kelas_id') == $k->id ? 'selected' : '' }}>
                                        {{ $k->nama }}
                                    </option>
                                @endforeach
                            </select>
                            @error('kelas_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Mata Pelajaran -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="mata_pelajaran_id">Mata Pelajaran <span class="text-danger">*</span></label>
                            <select name="mata_pelajaran_id" id="mata_pelajaran_id" class="form-control select2 @error('mata_pelajaran_id') is-invalid @enderror" required>
                                <option value="">Pilih Mata Pelajaran</option>
                                @foreach($mataPelajaran as $mp)
                                    <option value="{{ $mp->id }}" {{ old('mata_pelajaran_id') == $mp->id ? 'selected' : '' }}>
                                        {{ $mp->nama }}
                                    </option>
                                @endforeach
                            </select>
                            @error('mata_pelajaran_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Kompetensi Dasar -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="kompetensi_dasar_id">Kompetensi Dasar <span class="text-danger">*</span></label>
                            <select name="kompetensi_dasar_id" id="kompetensi_dasar_id" class="form-control select2 @error('kompetensi_dasar_id') is-invalid @enderror" required>
                                <option value="">Pilih Kompetensi Dasar</option>
                                @foreach($kompetensiDasar as $kd)
                                    <option value="{{ $kd->id }}" {{ old('kompetensi_dasar_id') == $kd->id ? 'selected' : '' }}>
                                        {{ $kd->kode }} - {{ $kd->deskripsi }}
                                    </option>
                                @endforeach
                            </select>
                            @error('kompetensi_dasar_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Tanggal -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="tanggal">Tanggal Penilaian <span class="text-danger">*</span></label>
                            <input type="date" name="tanggal" id="tanggal" class="form-control @error('tanggal') is-invalid @enderror" value="{{ old('tanggal', date('Y-m-d')) }}" required>
                            @error('tanggal')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Jenis Penilaian -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="jenis_penilaian">Jenis Penilaian <span class="text-danger">*</span></label>
                            <select name="jenis_penilaian" id="jenis_penilaian" class="form-control @error('jenis_penilaian') is-invalid @enderror" required>
                                <option value="">Pilih Jenis Penilaian</option>
                                <option value="Sumatif Tengah Semester" {{ old('jenis_penilaian') == 'Sumatif Tengah Semester' ? 'selected' : '' }}>Sumatif Tengah Semester</option>
                                <option value="Sumatif Akhir Semester" {{ old('jenis_penilaian') == 'Sumatif Akhir Semester' ? 'selected' : '' }}>Sumatif Akhir Semester</option>
                                <option value="Sumatif Akhir Tahun" {{ old('jenis_penilaian') == 'Sumatif Akhir Tahun' ? 'selected' : '' }}>Sumatif Akhir Tahun</option>
                            </select>
                            @error('jenis_penilaian')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Dimensi -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="dimensi">Dimensi <span class="text-danger">*</span></label>
                            <select name="dimensi" id="dimensi" class="form-control @error('dimensi') is-invalid @enderror" required>
                                <option value="">Pilih Dimensi</option>
                                <option value="Pengetahuan" {{ old('dimensi') == 'Pengetahuan' ? 'selected' : '' }}>Pengetahuan</option>
                                <option value="Keterampilan" {{ old('dimensi') == 'Keterampilan' ? 'selected' : '' }}>Keterampilan</option>
                                <option value="Sikap" {{ old('dimensi') == 'Sikap' ? 'selected' : '' }}>Sikap</option>
                            </select>
                            @error('dimensi')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Nilai Angka -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="nilai_angka">Nilai Angka <span class="text-danger">*</span></label>
                            <input type="number" name="nilai_angka" id="nilai_angka" class="form-control @error('nilai_angka') is-invalid @enderror" value="{{ old('nilai_angka') }}" min="0" max="100" step="0.01" required>
                            @error('nilai_angka')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <!-- Nilai Huruf -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="nilai_huruf">Nilai Huruf</label>
                            <input type="text" name="nilai_huruf" id="nilai_huruf" class="form-control @error('nilai_huruf') is-invalid @enderror" value="{{ old('nilai_huruf') }}" maxlength="2">
                            @error('nilai_huruf')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <!-- Deskripsi -->
                <div class="form-group">
                    <label for="deskripsi">Deskripsi</label>
                    <textarea name="deskripsi" id="deskripsi" class="form-control @error('deskripsi') is-invalid @enderror" rows="3">{{ old('deskripsi') }}</textarea>
                    @error('deskripsi')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Simpan</button>
                    <a href="{{ route('penilaian.sumatif.index') }}" class="btn btn-secondary">Kembali</a>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css">
@stop

@section('js')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Inisialisasi Select2
            $('.select2').select2({
                theme: 'bootstrap4',
                width: '100%'
            });
            
            // Auto-generate nilai huruf berdasarkan nilai angka
            $('#nilai_angka').on('change', function() {
                let nilai = parseFloat($(this).val());
                let huruf = '';
                
                if (nilai >= 90) {
                    huruf = 'A';
                } else if (nilai >= 80) {
                    huruf = 'B';
                } else if (nilai >= 70) {
                    huruf = 'C';
                } else if (nilai >= 60) {
                    huruf = 'D';
                } else {
                    huruf = 'E';
                }
                
                $('#nilai_huruf').val(huruf);
            });
            
            // Filter kompetensi dasar berdasarkan mata pelajaran
            $('#mata_pelajaran_id').on('change', function() {
                let mapelId = $(this).val();
                if (mapelId) {
                    // Anda bisa menambahkan AJAX request di sini untuk memfilter KD berdasarkan mapel
                    // Contoh sederhana:
                    /*
                    $.ajax({
                        url: '/api/kompetensi-dasar/by-mapel/' + mapelId,
                        type: 'GET',
                        dataType: 'json',
                        success: function(data) {
                            $('#kompetensi_dasar_id').empty();
                            $('#kompetensi_dasar_id').append('<option value="">Pilih Kompetensi Dasar</option>');
                            $.each(data, function(key, value) {
                                $('#kompetensi_dasar_id').append('<option value="' + value.id + '">' + value.kode + ' - ' + value.deskripsi + '</option>');
                            });
                        }
                    });
                    */
                }
            });
        });
    </script>
@stop