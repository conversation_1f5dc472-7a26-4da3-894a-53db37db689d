@extends('adminlte::page')

@section('title', 'Manajemen Role')

@section('content_header')
    <h1>Manajemen Role</h1>
@stop

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar Role</h3>
        <div class="card-tools">
            <a href="{{ route('admin.roles.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah Role
            </a>
        </div>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>No</th>
                    <th><PERSON>a Role</th>
                    <th>Permissions</th>
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
                @foreach($roles as $index => $role)
                    <tr>
                        <td>{{ $index + 1 }}</td>
                        <td>{{ $role->name }}</td>
                        <td>
                            @if($role->permissions->count() > 0)
                                <span class="badge badge-info">{{ $role->permissions->count() }} permissions</span>
                                <button type="button" class="btn btn-xs btn-outline-info" data-toggle="modal" data-target="#permissionsModal{{ $role->id }}">
                                    Lihat Detail
                                </button>
                                
                                <!-- Modal untuk menampilkan permissions -->
                                <div class="modal fade" id="permissionsModal{{ $role->id }}" tabindex="-1" role="dialog" aria-labelledby="permissionsModalLabel{{ $role->id }}" aria-hidden="true">
                                    <div class="modal-dialog modal-lg" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="permissionsModalLabel{{ $role->id }}">Permissions untuk Role: {{ $role->name }}</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row">
                                                    @foreach($role->permissions as $permission)
                                                        <div class="col-md-4 mb-2">
                                                            <span class="badge badge-success">{{ $permission->name }}</span>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <span class="badge badge-warning">Tidak ada permissions</span>
                            @endif
                        </td>
                        <td>
                            <a href="{{ route('admin.roles.edit', $role->id) }}" class="btn btn-sm btn-info">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>
@stop

@section('css')
<style>
    .modal-body {
        max-height: 400px;
        overflow-y: auto;
    }
</style>
@stop

@section('js')
<script>
    $(document).ready(function() {
        // Auto hide alert after 3 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 3000);
    });
</script>
@stop