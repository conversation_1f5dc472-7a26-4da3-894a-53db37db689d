@extends('adminlte::page')

@section('title', 'Tambah Laporan Kerja')

@section('content_header')
    <h1>Tambah Laporan Kerja</h1>
@stop

@section('content')
<div class="container">
    <div class="card">
        <div class="card-body">
            <form action="{{ route('laporan-kerja.store') }}" method="POST">
                @csrf
                
                <div class="form-group">
                    <label for="uraian_kerja">Uraian Kerja <span class="text-danger">*</span></label>
                    <textarea name="uraian_kerja" id="uraian_kerja" class="form-control @error('uraian_kerja') is-invalid @enderror" rows="3" required>{{ old('uraian_kerja') }}</textarea>
                    @error('uraian_kerja')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="waktu_mulai">W<PERSON><PERSON> <span class="text-danger">*</span></label>
                            <input type="text" name="waktu_mulai" id="waktu_mulai" class="form-control @error('waktu_mulai') is-invalid @enderror" value="{{ old('waktu_mulai') }}" placeholder="Format: YYYY-MM-DD HH:MM" required>
                            @error('waktu_mulai')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="waktu_selesai">Waktu Selesai</label>
                            <input type="datetime-local" class="form-control @error('waktu_selesai') is-invalid @enderror" 
                                   id="waktu_selesai" name="waktu_selesai" value="{{ old('waktu_selesai') }}">
                            @error('waktu_selesai')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="status">Status <span class="text-danger">*</span></label>
                    <select name="status" id="status" class="form-control @error('status') is-invalid @enderror" required>
                        <option value="">-- Pilih Status --</option>
                        <option value="selesai" {{ old('status') == 'selesai' ? 'selected' : '' }}>Selesai</option>
                        <option value="proses" {{ old('status') == 'proses' ? 'selected' : '' }}>Proses</option>
                        <option value="tertunda" {{ old('status') == 'tertunda' ? 'selected' : '' }}>Tertunda</option>
                    </select>
                    @error('status')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group">
                    <label for="keterangan">Keterangan Tambahan</label>
                    <textarea name="keterangan" id="keterangan" class="form-control @error('keterangan') is-invalid @enderror" rows="3">{{ old('keterangan') }}</textarea>
                    @error('keterangan')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Simpan</button>
                    <a href="{{ route('laporan-kerja.index') }}" class="btn btn-secondary">Batal</a>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('js')
<script>
    $(document).ready(function() {
        // Fungsi untuk mengatur visibilitas field waktu selesai
        function toggleWaktuSelesai() {
            const status = $('#status').val();
            if (status === 'selesai') {
                $('#waktu_selesai').prop('required', true);
                $('#waktu_selesai').closest('.form-group').show();
                $('#waktu_selesai').closest('.form-group').find('label').html('Waktu Selesai <span class="text-danger">*</span>');
            } else {
                // Jika status bukan selesai, kosongkan field dan sembunyikan
                $('#waktu_selesai').val('');
                $('#waktu_selesai').prop('required', false);
                $('#waktu_selesai').closest('.form-group').hide();
            }
        }
        
        // Jalankan saat halaman dimuat
        toggleWaktuSelesai();
        
        // Jalankan saat status berubah
        $('#status').change(toggleWaktuSelesai);
        
        // Sebelum submit form, pastikan waktu_selesai kosong jika status bukan selesai
        $('form').submit(function() {
            if ($('#status').val() !== 'selesai') {
                $('#waktu_selesai').val('');
            }
            return true;
        });
    });
</script>
@stop



