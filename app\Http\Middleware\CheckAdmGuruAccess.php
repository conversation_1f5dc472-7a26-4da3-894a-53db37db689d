<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CheckAdmGuruAccess
{
    public function handle(Request $request, Closure $next)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        
        // Cek apakah user memiliki permission yang sesuai
        if ($request->is('*/approve') || $request->is('*/reject')) {
            if (!$user->hasRole(['Kepala Sekolah', 'Waka Kurikulum'])) {
                return redirect()->route('dashboard')
                    ->with('error', 'Anda tidak memiliki izin untuk menyetujui/menolak ADM.');
            }
        } else {
            if (!$user->hasAnyPermission(['view-adm-guru', 'upload-adm-guru', 'manage-adm-guru'])) {
                return redirect()->route('dashboard')
                    ->with('error', 'Anda tidak memiliki izin untuk mengakses ADM.');
            }
        }

        return $next($request);
    }
}
