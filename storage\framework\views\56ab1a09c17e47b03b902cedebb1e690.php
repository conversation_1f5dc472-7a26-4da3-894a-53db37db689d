<?php $__env->startSection('title', 'Event ' . ucfirst($jenjang)); ?>

<?php $__env->startSection('css'); ?>
<style>
    /* Styling untuk judul halaman */
.page-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}
    /* Memperbaiki ukuran video YouTube agar sama dengan gambar */
    .card-img-top {
        height: 250px;
        object-fit: cover;
        width: 100%;
        border-radius: 15px 15px 0 0;
    }

    /* Container media untuk memastikan ukuran yang konsisten */
    .media-container {
        width: 100%;
        height: 250px;
        overflow: hidden;
        position: relative;
        border-radius: 15px 15px 0 0;
        background-color: #f8f9fa;
    }

    /* Styling untuk embed YouTube */
    .embed-responsive {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        padding: 0 !important;
        margin: 0 !important;
        overflow: hidden;
        border-radius: 15px 15px 0 0;
    }

    .embed-responsive-16by9::before {
        display: none !important;
        padding-top: 0 !important;
    }

    .embed-responsive-16by9 {
        padding-bottom: 0 !important;
        height: 100% !important;
    }

    .embed-responsive-item {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        border: 0 !important;
        border-radius: 15px 15px 0 0 !important;
        object-fit: cover !important;
    }

    /* Memastikan iframe YouTube memenuhi container */
    .media-container iframe,
    .media-container .embed-responsive iframe,
    iframe.embed-responsive-item {
        width: 100% !important;
        height: 100% !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        border: none !important;
        border-radius: 15px 15px 0 0 !important;
        object-fit: cover !important;
        transform: scale(1.2) !important;
        transform-origin: center center !important;
    }

    /* Memastikan card memiliki ukuran yang sama */
    .card {
        height: 100%;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: none;
        border-radius: 15px;
        overflow: hidden;
        background: #fff;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    /* Override untuk memastikan tidak ada padding atau margin yang mengganggu */
    .embed-responsive-16by9 {
        padding-bottom: 0 !important;
        height: 100% !important;
    }

    /* Memastikan gambar dan video memiliki ukuran yang sama persis */
    .media-container img,
    .media-container .embed-responsive,
    .media-container iframe {
        width: 100%;
        height: 250px;
        object-fit: cover;
    }

    /* CSS khusus untuk mengatasi masalah ukuran YouTube */
    .media-container .embed-responsive {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100% !important;
        height: 100% !important;
    }

    /* Memaksa iframe YouTube untuk memenuhi container */
    .media-container iframe[src*="youtube.com"],
    .media-container iframe[src*="youtu.be"] {
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        width: 120% !important;
        height: 120% !important;
        transform: translate(-50%, -50%) !important;
        border: none !important;
        border-radius: 15px 15px 0 0 !important;
    }

    /* CSS tambahan untuk memastikan YouTube memenuhi container */
    .media-container {
        position: relative !important;
        overflow: hidden !important;
    }

    .media-container .embed-responsive,
    .media-container .embed-responsive-16by9 {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* Solusi alternatif untuk YouTube yang stubborn */
    .media-container iframe {
        min-width: 100% !important;
        min-height: 100% !important;
        width: 100% !important;
        height: 100% !important;
        object-fit: cover !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }

    /* Memastikan container media memiliki ukuran yang sama */
    .card .embed-responsive,
    .card img.card-img-top {
        width: 100%;
        height: 250px;
        object-position: center;
    }

    .card:hover .card-img-top {
        transform: scale(1.05);
    }

    .card-body {
        padding: 1.5rem;
    }

    .card-title {
        color: #2c3e50;
        font-weight: 700;
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }

    .text-muted {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .text-muted i {
        margin-right: 8px;
        color: #e74c3c;
    }

    .card-text {
        color: #666;
        font-size: 0.95rem;
        line-height: 1.5;
    }
</style>
<?php $__env->stopSection(); ?>

<?php
/**
 * Mengekstrak ID video dari URL YouTube
 *
 * @param string $url URL YouTube
 * @return string|null ID video YouTube atau null jika tidak valid
 */
function getYoutubeVideoId($url) {
    if (empty($url)) return null;

    $pattern =
        '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i';

    if (preg_match($pattern, $url, $match)) {
        return $match[1];
    }

    return null;
}
?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    <div class="row">
        <!-- Sidebar Menu -->
        <div class="col-md-3">
            <?php echo $__env->make('website.partials._sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <h1 class="mb-4">Event <?php echo e(ucfirst($jenjang)); ?></h1>
            
            <div class="row">
                <?php $__currentLoopData = $events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-4 mb-4">
                    <div class="card h-100">
                        <div class="media-container">
                            <?php if($event->gambar): ?>
                                <img src="<?php echo e(asset('storage/events/' . $event->gambar)); ?>"
                                     class="card-img-top"
                                     alt="<?php echo e($event->judul); ?>">
                            <?php elseif($event->youtube_url): ?>
                                <div class="embed-responsive embed-responsive-16by9">
                                    <iframe class="embed-responsive-item"
                                            src="https://www.youtube.com/embed/<?php echo e(getYoutubeVideoId($event->youtube_url)); ?>"
                                            allowfullscreen></iframe>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title"><?php echo e($event->judul); ?></h5>
                            <p class="text-muted">
                                <i class="fas fa-calendar"></i> 
                                <?php echo e(\Carbon\Carbon::parse($event->tanggal)->format('d M Y')); ?>

                            </p>
                            <p class="text-muted">
                                <i class="fas fa-map-marker-alt"></i> 
                                <?php echo e($event->lokasi); ?>

                            </p>
                            <p class="card-text"><?php echo e(Str::limit($event->deskripsi, 100)); ?></p>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="d-flex justify-content-center mt-4">
                <?php echo e($events->links()); ?>

            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('layouts.website', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/website/jenjang/event.blade.php ENDPATH**/ ?>