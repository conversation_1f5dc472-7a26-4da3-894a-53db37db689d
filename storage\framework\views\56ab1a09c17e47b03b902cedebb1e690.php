<?php $__env->startSection('title', 'Eventjj ' . ucfirst($jenjang)); ?>

<?php $__env->startSection('css'); ?>
<style>
/* ===== STYLING UNTUK HALAMAN EVENT ===== */

/* Container utama dengan background gradient */
.event-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

/* Styling untuk judul halaman */
.page-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* Styling untuk card event */
.event-card {
    background: white;
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    overflow: hidden;
    position: relative;
    height: 100%;
}

.event-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.event-card:hover::before {
    transform: scaleX(1);
}

.event-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Container media untuk gambar dan video */
.media-container {
    width: 100%;
    height: 220px;
    overflow: hidden;
    position: relative;
    border-radius: 20px 20px 0 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Styling untuk gambar */
.event-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: transform 0.4s ease;
    border-radius: 0;
}

.event-card:hover .event-image {
    transform: scale(1.05);
}

/* Styling untuk embed YouTube */
.embed-responsive {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden;
    border-radius: 20px 20px 0 0;
}

.embed-responsive-16by9::before {
    display: none !important;
    padding-top: 0 !important;
}

.embed-responsive-16by9 {
    padding-bottom: 0 !important;
    height: 100% !important;
}

.embed-responsive-item {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    border: 0 !important;
    border-radius: 20px 20px 0 0 !important;
    object-fit: cover !important;
}

/* Memastikan iframe YouTube memenuhi container */
.media-container iframe,
.media-container .embed-responsive iframe,
iframe.embed-responsive-item {
    width: 100% !important;
    height: 100% !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    border: none !important;
    border-radius: 20px 20px 0 0 !important;
    object-fit: cover !important;
    transform: scale(1.2) !important;
    transform-origin: center center !important;
}

/* Memaksa iframe YouTube untuk memenuhi container */
.media-container iframe[src*="youtube.com"],
.media-container iframe[src*="youtu.be"] {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    width: 120% !important;
    height: 120% !important;
    transform: translate(-50%, -50%) !important;
    border: none !important;
    border-radius: 20px 20px 0 0 !important;
}

/* Styling untuk body card */
.event-card-body {
    padding: 1.5rem;
    background: white;
}

.event-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.25rem;
    margin-bottom: 1rem;
    transition: color 0.3s ease;
    line-height: 1.4;
}

.event-card:hover .event-title {
    color: #667eea;
}

/* Styling untuk informasi event */
.event-info {
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    color: #6c757d;
    display: flex;
    align-items: center;
}

.event-info i {
    margin-right: 10px;
    width: 16px;
    text-align: center;
    color: #667eea;
}

.event-description {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Styling untuk placeholder jika tidak ada media */
.no-media-placeholder {
    height: 220px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 3rem;
    border-radius: 20px 20px 0 0;
}

/* Styling untuk empty state */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 1rem;
}

/* Styling untuk pagination */
.pagination-container {
    margin-top: 3rem;
}

.pagination .page-link {
    border: none;
    color: #667eea;
    padding: 0.75rem 1rem;
    margin: 0 0.25rem;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

/* Responsive design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .event-container {
        padding: 1rem 0;
    }

    .media-container,
    .event-image,
    .no-media-placeholder {
        height: 180px;
    }

    .event-card-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .page-title {
        font-size: 1.75rem;
    }

    .media-container,
    .event-image,
    .no-media-placeholder {
        height: 160px;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php
/**
 * Mengekstrak ID video dari URL YouTube
 *
 * @param string $url URL YouTube
 * @return string|null ID video YouTube atau null jika tidak valid
 */
function getYoutubeVideoId($url) {
    if (empty($url)) return null;

    $pattern =
        '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i';

    if (preg_match($pattern, $url, $match)) {
        return $match[1];
    }

    return null;
}
?>

<?php $__env->startSection('content'); ?>
<div class="event-container">
    <div class="container">
        <div class="row">
            <!-- Sidebar Menu -->
            <div class="col-md-3 mb-4">
                <?php echo $__env->make('website.partials._sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <h1 class="page-title">Event <?php echo e(ucfirst($jenjang)); ?></h1>

                <?php if($events->count() > 0): ?>
                    <div class="row">
                        <?php $__currentLoopData = $events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card event-card">
                                <div class="media-container">
                                    <?php if($event->gambar): ?>
                                        <img src="<?php echo e(asset('storage/events/' . $event->gambar)); ?>"
                                             class="event-image"
                                             alt="<?php echo e($event->judul); ?>">
                                    <?php elseif($event->youtube_url): ?>
                                        <div class="embed-responsive embed-responsive-16by9">
                                            <iframe class="embed-responsive-item"
                                                    src="https://www.youtube.com/embed/<?php echo e(getYoutubeVideoId($event->youtube_url)); ?>"
                                                    allowfullscreen></iframe>
                                        </div>
                                    <?php else: ?>
                                        <div class="no-media-placeholder">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="card-body event-card-body">
                                    <h5 class="card-title event-title"><?php echo e($event->judul); ?></h5>
                                    <div class="event-info">
                                        <i class="fas fa-calendar"></i>
                                        <?php echo e(\Carbon\Carbon::parse($event->tanggal)->format('d M Y')); ?>

                                    </div>
                                    <div class="event-info">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <?php echo e($event->lokasi); ?>

                                    </div>
                                    <p class="card-text event-description"><?php echo e(Str::limit($event->deskripsi, 100)); ?></p>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-calendar-alt"></i>
                        <h3>Belum Ada Event</h3>
                        <p>Event untuk jenjang <?php echo e(ucfirst($jenjang)); ?> belum tersedia.</p>
                    </div>
                <?php endif; ?>

                <?php if($events->hasPages()): ?>
                    <div class="pagination-container d-flex justify-content-center">
                        <?php echo e($events->links()); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('layouts.website', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/website/jenjang/event.blade.php ENDPATH**/ ?>