@extends('adminlte::page')

@section('title', 'Edit Prestasi')

@section('content_header')
    <h1>Edit Prestasi</h1>
@stop

@section('content')
<div class="card">
    <div class="card-body">
        <form action="{{ route('admin.website.prestasi.update', $prestasi->id) }}" method="POST">
            @csrf
            @method('PUT')
            <div class="form-group">
                <label for="judul">Judul Prestasi</label>
                <input type="text" class="form-control @error('judul') is-invalid @enderror" 
                       id="judul" name="judul" value="{{ old('judul', $prestasi->judul) }}" required>
                @error('judul')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="tanggal">Tanggal</label>
                <input type="date" class="form-control @error('tanggal') is-invalid @enderror" 
                       id="tanggal" name="tanggal" 
                       value="{{ old('tanggal', ($prestasi->tanggal ? $prestasi->tanggal->format('Y-m-d') : '')) }}" 
                       required>
                @error('tanggal')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="tingkat">Tingkat</label>
                <select class="form-control @error('tingkat') is-invalid @enderror" 
                        id="tingkat" name="tingkat" required>
                    <option value="">Pilih Tingkat</option>
                    @foreach(['Sekolah', 'Kecamatan', 'Kabupaten', 'Provinsi', 'Nasional', 'Internasional'] as $tingkat)
                        <option value="{{ $tingkat }}" {{ old('tingkat', $prestasi->tingkat) == $tingkat ? 'selected' : '' }}>
                            {{ $tingkat }}
                        </option>
                    @endforeach
                </select>
                @error('tingkat')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="unit_id">Jenjang</label>
                <select class="form-control @error('unit_id') is-invalid @enderror" 
                        id="unit_id" name="unit_id" required>
                    <option value="">Pilih Jenjang</option>
                    @foreach($units as $unit)
                        <option value="{{ $unit->id }}" {{ old('unit_id', $prestasi->unit_id) == $unit->id ? 'selected' : '' }}>
                            {{ $unit->nama_unit }}
                        </option>
                    @endforeach
                </select>
                @error('unit_id')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="peserta">Peserta/Tim</label>
                <input type="text" class="form-control @error('peserta') is-invalid @enderror" 
                       id="peserta" name="peserta" value="{{ old('peserta', $prestasi->peserta) }}" required>
                @error('peserta')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="pembina">Pembina</label>
                <input type="text" class="form-control @error('pembina') is-invalid @enderror" 
                       id="pembina" name="pembina" value="{{ old('pembina', $prestasi->pembina) }}">
                @error('pembina')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="deskripsi">Deskripsi</label>
                <textarea class="form-control @error('deskripsi') is-invalid @enderror" 
                          id="deskripsi" name="deskripsi" rows="4">{{ old('deskripsi', $prestasi->deskripsi) }}</textarea>
                @error('deskripsi')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            @if($prestasi->image)
                <div class="mb-2">
                    <img src="{{ Storage::url($prestasi->image) }}" alt="Current Image" style="max-height: 200px">
                </div>
            @endif

            <button type="submit" class="btn btn-primary">Update</button>
            <a href="{{ route('admin.website.prestasi.index') }}" class="btn btn-secondary">Kembali</a>
        </form>
    </div>
</div>
@stop


