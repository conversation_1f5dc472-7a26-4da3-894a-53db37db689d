<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use Illuminate\Session\TokenMismatchException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Illuminate\Support\Facades\Log;
use App\Models\Jenjang;

class Handler extends ExceptionHandler
{
    public function register()
    {
        $this->renderable(function (TokenMismatchException $e) {
            Log::debug('TokenMismatchException triggered', [
                'exception' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return redirect()
                ->route('login')
                ->with('error', 'Your session has expired. Please try again.');
        });

        $this->renderable(function (UnauthorizedException $e) {
            Log::debug('UnauthorizedException triggered', [
                'exception' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'user' => auth()->user() ? [
                    'id' => auth()->id(),
                    'name' => auth()->user()->name,
                    'role' => auth()->user()->role,
                    'permissions' => auth()->user()->getAllPermissions()->pluck('name')
                ] : 'Not authenticated'
            ]);
            return response()->view('errors.403', [
                'debug_info' => config('app.debug') ? [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'message' => $e->getMessage()
                ] : null
            ], 403);
        });

        // Tambahkan handler untuk ModelNotFoundException
        $this->renderable(function (ModelNotFoundException $e) {
            Log::debug('ModelNotFoundException triggered', [
                'exception' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return response()->view('errors.404', [
                'message' => 'Data yang Anda cari tidak ditemukan.',
                'back_url' => url()->previous(),
                'debug_info' => config('app.debug') ? [
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ] : null
            ], 404);
        });

        // Tambahkan handler untuk NotFoundHttpException
        $this->renderable(function (NotFoundHttpException $e) {
            Log::debug('NotFoundHttpException triggered', [
                'exception' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            // Cek apakah request URL mengandung 'sarana'
            $isSaranaRoute = str_contains(request()->path(), 'sarana');
            
            // Default jenjang jika tidak ada
            $jenjang = 'sd'; // Nilai default
            
            // Coba ambil jenjang dari database jika tersedia
            try {
                $jenjangModel = \App\Models\Jenjang::first();
                if ($jenjangModel) {
                    $jenjang = strtolower($jenjangModel->jenjang);
                }
            } catch (\Exception $ex) {
                // Jika gagal, gunakan nilai default
            }
            
            return response()->view('errors.404', [
                'message' => 'Halaman yang Anda cari tidak ditemukan.',
                'back_url' => url()->previous(),
                'jenjang' => $jenjang, // Tambahkan variabel $jenjang
                'debug_info' => config('app.debug') ? [
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ] : null
            ], 404);
        });
    }
}




