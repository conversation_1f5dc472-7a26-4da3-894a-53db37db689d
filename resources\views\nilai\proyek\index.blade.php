@extends('adminlte::page')

@section('title', 'Daftar Penilaian Proyek')

@section('content_header')
    <h1>Daftar Penilaian Proyek</h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Penilaian Proyek</h3>
            <div class="card-tools">
                <a href="{{ route('penilaian.proyek.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Tambah Penilaian Proyek
                </a>
            </div>
        </div>
        <div class="card-body">
            <!-- <PERSON><PERSON> Sukses -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h5><i class="icon fas fa-check"></i> Sukses!</h5>
                    {{ session('success') }}
                </div>
            @endif

            <!-- <PERSON><PERSON> Error -->
            @if(session('error'))
                <div class="alert alert-danger alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h5><i class="icon fas fa-ban"></i> Error!</h5>
                    {{ session('error') }}
                </div>
            @endif

            <!-- Tabel Penilaian Proyek -->
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="proyekTable">
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th width="20%">Nama Proyek</th>
                            <th width="15%">Kelas</th>
                            <th width="15%">Tanggal Mulai</th>
                            <th width="15%">Tanggal Selesai</th>
                            <th width="15%">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($penilaianProyek as $index => $proyek)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $proyek->nama_proyek }}</td>
                                <td>{{ $proyek->kelas->nama }}</td>
                                <td>{{ $proyek->tanggal_mulai->format('d/m/Y') }}</td>
                                <td>{{ $proyek->tanggal_selesai->format('d/m/Y') }}</td>
                                <td>
                                    <a href="{{ route('penilaian.proyek.show', $proyek->id) }}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('penilaian.proyek.edit', $proyek->id) }}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ route('penilaian.proyek.nilaiSiswa', $proyek->id) }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-users"></i> Nilai Siswa
                                    </a>
                                    <form action="{{ route('penilaian.proyek.destroy', $proyek->id) }}" method="POST" style="display: inline-block;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center">Tidak ada data penilaian proyek</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-3">
                {{ $penilaianProyek->links() }}
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            $('#proyekTable').DataTable({
                "paging": false,
                "lengthChange": false,
                "searching": true,
                "ordering": true,
                "info": false,
                "autoWidth": false,
                "responsive": true,
            });
        });
    </script>
@stop