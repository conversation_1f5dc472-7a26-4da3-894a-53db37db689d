@extends('adminlte::page')

@section('title', 'ADM Waka')

@section('content_header')
    <h1>ADM Waka</h1>
@stop

@section('content')
<div class="container">
    @if(auth()->user()->hasPermissionTo('upload-adm-waka'))
        <button type="button" class="btn btn-primary mb-3" data-toggle="modal" data-target="#uploadModal">
            <i class="fas fa-upload"></i> Upload ADM
        </button>
    @endif

    <div class="card">
        <div class="card-body">
            <table class="table table-bordered" id="admTable">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Judul</th>
                        <th style="width: 300px; vertical-align: top;">Keterangan</th>
                        <th>Waka</th>
                        <th>Status</th>
                        <th>Tanggal</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @php $index = 1; @endphp
                    @foreach($admList as $adm)
                        <tr>
                            <td>{{ $index++ }}</td>
                            <td>{{ $adm->judul }}</td>
                            <td style="vertical-align: top !important; padding-top: 0 !important;">
                                <div class="keterangan-cell">
                                    @if($adm->keterangan)
                                        {{ $adm->keterangan }}
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </div>
                            </td>
                            <td>{{ $adm->user->name }}</td>
                            <td>
                                @if($adm->status == 'pending')
                                    <span class="badge badge-warning">Pending</span>
                                @elseif($adm->status == 'approved')
                                    <span class="badge badge-success">Disetujui</span>
                                @else
                                    <span class="badge badge-danger" 
                                          data-toggle="tooltip" 
                                          data-html="true"
                                          title="<strong>Ditangguhkan oleh:</strong> {{ $adm->rejector->name ?? 'N/A' }}<br>
                                                     <strong>Alasan:</strong> {{ $adm->alasan_penolakan ?? 'N/A' }}">
                                        Ditangguhkan
                                    </span>
                                @endif
                            </td>
                            <td>{{ $adm->created_at->format('d/m/Y H:i') }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ route('adm.waka.view-page', basename($adm->file_path)) }}" 
                                       class="btn btn-sm btn-info" 
                                       target="_blank">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    @if($adm->status == 'pending' && auth()->user()->hasRole('Kepala Sekolah'))
                                        <button type="button" 
                                                class="btn btn-sm btn-success" 
                                                onclick="approveAdm('{{ route('adm.waka.approve', $adm->id) }}')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        
                                        <button type="button" 
                                                class="btn btn-sm btn-danger" 
                                                onclick="showRejectModal('{{ route('adm.waka.reject', $adm->id) }}')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

@include('adm.waka.modals.upload')
@include('adm.waka.modals.reject')
@stop

@section('css')
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<style>
    .keterangan-cell {
        max-width: 300px; /* Tetapkan lebar maksimum */
        min-width: 200px; /* Tetapkan lebar minimum */
        white-space: pre-wrap; /* Memungkinkan text wrap */
        overflow-y: auto; /* Scroll vertikal jika perlu */
        max-height: 75px; /* Tinggi maksimum sebelum scroll */
        padding: 10px; /* Padding untuk kenyamanan membaca */
        vertical-align: top !important; /* Tambahkan ini */
        text-align: justify; /* Rata kanan kiri */
        display: block; /* Memastikan div mengisi seluruh td */
        margin-top: 0 !important; /* Memastikan tidak ada margin di atas */
    }

    /* Style untuk parent td dari keterangan-cell */
    td .keterangan-cell {
        margin-top: 0 !important; /* Memastikan tidak ada gap di atas */
    }

    /* Memastikan td yang berisi keterangan menggunakan vertical-align: top */
    table.table td:nth-child(3) {
        vertical-align: top !important;
        padding-top: 0 !important;
    }
    
    /* Tambahan untuk memastikan konten berada di atas */
    #admTable tbody tr td:nth-child(3) {
        vertical-align: top !important;
    }
    
    /* Memastikan tidak ada padding tambahan di dalam td */
    #admTable tbody tr td:nth-child(3) {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
</style>
@stop

@section('js')
<script>
$(document).ready(function() {
    $('#admTable').DataTable();
    
    $('[data-toggle="tooltip"]').tooltip({
        html: true,
        container: 'body'
    });
});

function approveAdm(url) {
    if (confirm('Apakah Anda yakin ingin menyetujui ADM ini?')) {
        const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        fetch(url, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': token,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Terjadi kesalahan: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat memproses permintaan');
        });
    }
}

function showRejectModal(url) {
    $('#rejectForm').attr('action', url);
    $('#rejectModal').modal('show');
}
</script>
@stop






