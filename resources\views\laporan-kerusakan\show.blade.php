@extends('adminlte::page')

@section('title', 'Detail Laporan Kerusakan')

@section('content_header')
    <h1>Detail Laporan Kerusakan</h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Informasi Laporan</h3>
                    <div class="card-tools">
                        <a href="{{ route('laporan-kerusakan.index') }}" class="btn btn-sm btn-default">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 200px">Judul</th>
                                    <td>{{ $laporanKerusakan->judul }}</td>
                                </tr>
                                <tr>
                                    <th>Lokasi</th>
                                    <td>{{ $laporanKerusakan->lokasi }}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        @if($laporanKerusakan->status == 'dilaporkan')
                                            <span class="badge badge-warning">Dilaporkan</span>
                                        @elseif($laporanKerusakan->status == 'diproses')
                                            <span class="badge badge-info">Diproses</span>
                                        @elseif($laporanKerusakan->status == 'selesai')
                                            <span class="badge badge-success">Selesai</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Pelapor</th>
                                    <td>{{ $laporanKerusakan->pelapor->name }}</td>
                                </tr>
                                <tr>
                                    <th>Tanggal Lapor</th>
                                    <td>{{ $laporanKerusakan->tanggal_lapor->format('d/m/Y H:i') }}</td>
                                </tr>
                                @if($laporanKerusakan->status == 'diproses' || $laporanKerusakan->status == 'selesai')
                                <tr>
                                    <th>Ditangani Oleh</th>
                                    <td>{{ $laporanKerusakan->penindak->name ?? '-' }}</td>
                                </tr>
                                @endif
                                @if($laporanKerusakan->status == 'selesai')
                                <tr>
                                    <th>Tanggal Selesai</th>
                                    <td>{{ $laporanKerusakan->tanggal_selesai->format('d/m/Y H:i') }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="card-title">Deskripsi Kerusakan</h5>
                                </div>
                                <div class="card-body">
                                    <p>{{ $laporanKerusakan->deskripsi }}</p>
                                </div>
                            </div>
                            
                            @if($laporanKerusakan->status == 'selesai')
                            <div class="card mt-3">
                                <div class="card-header bg-success">
                                    <h5 class="card-title text-white">Tindakan yang Dilakukan</h5>
                                </div>
                                <div class="card-body">
                                    <p>{{ $laporanKerusakan->tindakan }}</p>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="timeline">
                                <div>
                                    <i class="fas fa-flag bg-warning"></i>
                                    <div class="timeline-item">
                                        <span class="time"><i class="fas fa-clock"></i> {{ $laporanKerusakan->tanggal_lapor->format('d/m/Y H:i') }}</span>
                                        <h3 class="timeline-header"><strong>Laporan Dibuat</strong></h3>
                                        <div class="timeline-body">
                                            Laporan kerusakan dibuat oleh {{ $laporanKerusakan->pelapor->name }}
                                        </div>
                                    </div>
                                </div>
                                
                                @if($laporanKerusakan->status == 'diproses' || $laporanKerusakan->status == 'selesai')
                                <div>
                                    <i class="fas fa-tools bg-info"></i>
                                    <div class="timeline-item">
                                        <span class="time"><i class="fas fa-clock"></i> {{ $laporanKerusakan->tanggal_proses->format('d/m/Y H:i') }}</span>
                                        <h3 class="timeline-header"><strong>Laporan Diproses</strong></h3>
                                        <div class="timeline-body">
                                            Laporan kerusakan sedang diproses oleh {{ $laporanKerusakan->penindak->name }}
                                        </div>
                                    </div>
                                </div>
                                @endif
                                
                                @if($laporanKerusakan->status == 'selesai')
                                <div>
                                    <i class="fas fa-check bg-success"></i>
                                    <div class="timeline-item">
                                        <span class="time"><i class="fas fa-clock"></i> {{ $laporanKerusakan->tanggal_selesai->format('d/m/Y H:i') }}</span>
                                        <h3 class="timeline-header"><strong>Laporan Selesai</strong></h3>
                                        <div class="timeline-body">
                                            Laporan kerusakan telah diselesaikan oleh {{ $laporanKerusakan->penindak->name }}
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="btn-group">
                                @if(auth()->user()->hasPermissionTo('kelola-laporan-kerusakan'))
                                    @if($laporanKerusakan->status == 'dilaporkan')
                                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#prosesModal">
                                            <i class="fas fa-tools"></i> Proses Laporan
                                        </button>
                                    @elseif($laporanKerusakan->status == 'diproses')
                                        <button type="button" class="btn btn-success" data-toggle="modal" data-target="#selesaiModal">
                                            <i class="fas fa-check"></i> Selesaikan Laporan
                                        </button>
                                    @endif
                                    <!--
                                    <a href="{{ route('laporan-kerusakan.edit', $laporanKerusakan) }}" class="btn btn-warning">
                                        <i class="fas fa-edit"></i> Edit Laporan
                                    </a> -->
                                    
                                    <form action="{{ route('laporan-kerusakan.destroy', $laporanKerusakan) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <!--<button type="submit" class="btn btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus laporan ini?')">
                                            <i class="fas fa-trash"></i> Hapus Laporan
                                        </button> -->
                                    </form>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Proses -->
@if($laporanKerusakan->status == 'dilaporkan')
<div class="modal fade" id="prosesModal" tabindex="-1" role="dialog" aria-labelledby="prosesModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="prosesModalLabel">Proses Laporan Kerusakan</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('laporan-kerusakan.proses', $laporanKerusakan) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin memproses laporan kerusakan ini?</p>
                    <p><strong>Judul:</strong> {{ $laporanKerusakan->judul }}</p>
                    <p><strong>Lokasi:</strong> {{ $laporanKerusakan->lokasi }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Proses Laporan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif

<!-- Modal Selesai -->
@if($laporanKerusakan->status == 'diproses')
<div class="modal fade" id="selesaiModal" tabindex="-1" role="dialog" aria-labelledby="selesaiModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="selesaiModalLabel">Selesaikan Laporan Kerusakan</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('laporan-kerusakan.selesai', $laporanKerusakan) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="tindakan">Tindakan yang Dilakukan <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="tindakan" name="tindakan" rows="4" required></textarea>
                        <small class="form-text text-muted">Jelaskan tindakan yang telah dilakukan untuk memperbaiki kerusakan</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">Selesaikan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@stop

@section('css')
<style>
    .timeline {
        position: relative;
        margin: 0 0 30px 0;
        padding: 0;
        list-style: none;
    }
    
    .timeline:before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 4px;
        background: #ddd;
        left: 31px;
        margin: 0;
        border-radius: 2px;
    }
    
    .timeline > div {
        position: relative;
        margin-right: 10px;
        margin-bottom: 15px;
    }
    
    .timeline > div > i {
        width: 30px;
        height: 30px;
        font-size: 15px;
        line-height: 30px;
        position: absolute;
        color: #fff;
        background: #d2d6de;
        border-radius: 50%;
        text-align: center;
        left: 18px;
        top: 0;
    }
    
    .timeline-item {
        box-shadow: 0 1px 1px rgba(0,0,0,0.1);
        border-radius: 3px;
        margin-top: 0;
        background: #fff;
        color: #444;
        margin-left: 60px;
        margin-right: 15px;
        padding: 0;
        position: relative;
    }
    
    .timeline-item .time {
        color: #999;
        float: right;
        padding: 10px;
        font-size: 12px;
    }
    
    .timeline-item .timeline-header {
        margin: 0;
        color: #555;
        border-bottom: 1px solid #f4f4f4;
        padding: 10px;
        font-size: 16px;
        line-height: 1.1;
    }
    
    .timeline-item .timeline-body {
        padding: 10px;
    }
</style>
@stop

@section('js')
<script>
    $(document).ready(function() {
        // Script tambahan jika diperlukan
    });
</script>
@stop
