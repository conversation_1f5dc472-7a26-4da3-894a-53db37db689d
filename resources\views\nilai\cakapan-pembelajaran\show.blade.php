@extends('adminlte::page')

@section('title', 'Detail Cakapan Pembelajaran')

@section('content_header')
    <h1>Detail Cakapan Pembelajaran</h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Informasi Cakapan Pembelajaran</h3>
                    <div class="card-tools">
                        <a href="{{ route('penilaian.cakapan-pembelajaran.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th width="200">Kompetensi Dasar</th>
                            <td>{{ $cakapanPembelajaran->kompetensiDasar->kode }} - {{ $cakapanPembelajaran->kompetensiDasar->deskripsi }}</td>
                        </tr>
                        <tr>
                            <th>Deskripsi</th>
                            <td>{{ $cakapanPembelajaran->deskripsi }}</td>
                        </tr>
                        <tr>
                            <th>Dibuat Pada</th>
                            <td>{{ $cakapanPembelajaran->created_at->format('d-m-Y H:i:s') }}</td>
                        </tr>
                        <tr>
                            <th>Diperbarui Pada</th>
                            <td>{{ $cakapanPembelajaran->updated_at->format('d-m-Y H:i:s') }}</td>
                        </tr>
                    </table>
                    
                    <div class="mt-3">
                        <a href="{{ route('penilaian.cakapan-pembelajaran.edit', $cakapanPembelajaran->id) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <form action="{{ route('penilaian.cakapan-pembelajaran.destroy', $cakapanPembelajaran->id) }}" method="POST" style="display: inline-block;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')">
                                <i class="fas fa-trash"></i> Hapus
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop