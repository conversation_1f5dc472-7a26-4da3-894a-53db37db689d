<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Spp;
use App\Models\SppUpload;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\SppImport;

class SppController extends Controller
{
    /**
     * Menampilkan halaman admin SPP
     */
    public function index()
    {
        // Ambil data SPP terbaru
        $spps = Spp::latest()->paginate(10);
        
        // Ambil data riwayat upload
        $uploads = SppUpload::with('uploader')->latest()->get();
        
        return view('spp.admin.index', compact('spps', 'uploads'));
    }

    /**
     * Menampilkan form cek SPP untuk siswa/orang tua
     */
    public function check()
    {
        return view('spp.public.check');
    }

    /**
     * Memproses hasil cek SPP
     */
    public function checkResult(Request $request)
    {
        $request->validate([
            'va_number' => 'required',
            'tanggal_lahir' => 'required|date',
        ]);

        $spp = Spp::where('va_number', $request->va_number)
                  ->whereDate('tanggal_lahir', $request->tanggal_lahir)
                  ->first();

        if (!$spp) {
            return back()->with('error', 'Data SPP tidak ditemukan. Pastikan VA Number dan Tanggal Lahir benar.');
        }

        return view('spp.public.result', compact('spp'));
    }

    /**
     * Upload data SPP dari file Excel
     */
    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:2048',
            'keterangan' => 'nullable|string|max:255',
        ]);

        try {
            $file = $request->file('file');
            $originalName = $file->getClientOriginalName();
            $fileName = time() . '_' . $originalName;
            
            // Simpan file ke storage
            $path = $file->storeAs('spp_uploads', $fileName, 'public');
            
            // Simpan informasi upload ke database
            SppUpload::create([
                'file_name' => $fileName,
                'original_name' => $originalName,
                'keterangan' => $request->keterangan,
                'uploaded_by' => auth()->id(),
            ]);
            
            // Import data dari Excel dengan upsert
            Excel::import(new SppImport, $file);
            
            return back()->with('success', 'Data SPP berhasil diupload dan diperbarui.');
        } catch (\Exception $e) {
            return back()->with('error', 'Gagal mengupload data: ' . $e->getMessage());
        }
    }
}








