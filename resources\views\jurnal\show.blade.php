@extends('adminlte::page')

@section('title', 'Detail Jurnal Kegiatan')

@section('content_header')
    <h1>Detail Jurnal Kegiatan</h1>
@stop

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Detail Jurnal Kegiatan</h3>
                <div class="card-tools">
                    <a href="{{ route('jurnal.index') }}" class="btn btn-sm btn-default">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">Tanggal</dt>
                    <dd class="col-sm-9">{{ \Carbon\Carbon::parse($jurnal->tanggal)->format('d/m/Y') }}</dd>
                    
                    <dt class="col-sm-3">Unit</dt>
                    <dd class="col-sm-9">{{ $jurnal->unit->nama_unit ?? '-' }}</dd>
                    
                    <dt class="col-sm-3">Kegiatan</dt>
                    <dd class="col-sm-9">{{ $jurnal->kegiatan }}</dd>
                    
                    <dt class="col-sm-3">Ada</dt>
                    <dd class="col-sm-9">{{ $jurnal->ada ? 'Ya' : 'Tidak' }}</dd>
                    
                    <dt class="col-sm-3">Tidak</dt>
                    <dd class="col-sm-9">{{ $jurnal->tidak ? 'Ya' : 'Tidak' }}</dd>
                    
                    <dt class="col-sm-3">Keterangan</dt>
                    <dd class="col-sm-9">{{ $jurnal->keterangan ?? '-' }}</dd>
                    
                    <dt class="col-sm-3">Status</dt>
                    <dd class="col-sm-9">
                        @if($jurnal->status == 'draft')
                            <span class="badge badge-secondary">Draft</span>
                        @elseif($jurnal->status == 'submitted')
                            <span class="badge badge-info">Diajukan</span>
                        @elseif($jurnal->status == 'approved')
                            <span class="badge badge-success">Disetujui</span>
                        @elseif($jurnal->status == 'rejected')
                            <span class="badge badge-danger" 
                                  data-toggle="tooltip" 
                                  data-html="true"
                                  title="<strong>Ditolak oleh:</strong> {{ $jurnal->rejector->name ?? 'N/A' }}<br>
                                         <strong>Alasan:</strong> {{ $jurnal->alasan_penolakan ?? 'N/A' }}<br>
                                         <strong>Pada:</strong> {{ $jurnal->rejected_at ? \Carbon\Carbon::parse($jurnal->rejected_at)->format('d/m/Y H:i') : 'N/A' }}">
                                Ditolak
                            </span>
                        @endif
                    </dd>
                </dl>

                <!-- Tambahan untuk menampilkan data ekstrakurikuler -->
                @if($jurnal->eskulItems && $jurnal->eskulItems->count() > 0)
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">Data Ekstrakurikuler</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="5%">No</th>
                                        <th>Nama Eskul</th>
                                        <th>Kelas</th>
                                        <th>Jumlah Siswa</th>
                                        <th>Kegiatan</th>
                                        <th>Jumlah Jam</th>
                                        <th>Keterangan</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($jurnal->eskulItems as $index => $eskul)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $eskul->nama_eskul }}</td>
                                        <td>{{ $eskul->kelas }}</td>
                                        <td>{{ $eskul->jumlah_siswa }}</td>
                                        <td>{{ $eskul->kegiatan }}</td>
                                        <td>{{ $eskul->jumlah_jam }}</td>
                                        <td>{{ $eskul->keterangan ?? '-' }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                                <tfoot>
                                    <tr class="bg-light font-weight-bold">
                                        <td colspan="5" class="text-right">Total Jam Eskul:</td>
                                        <td>{{ $jurnal->eskulItems->sum('jumlah_jam') }}</td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Tambahan untuk menampilkan data jam pengganti -->
                @if($jurnal->penggantiItems && $jurnal->penggantiItems->count() > 0)
                <div class="card mt-4">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0">Data Jam Pengganti</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="5%">No</th>
                                        <th>Kelas</th>
                                        <th>Jam Ke</th>
                                        <th>Guru Diganti</th>
                                        <th>Jumlah Jam</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($jurnal->penggantiItems as $index => $pengganti)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $pengganti->kelas }}</td>
                                        <td>{{ $pengganti->jam_ke }}</td>
                                        <td>{{ $pengganti->guru_diganti }}</td>
                                        <td>{{ $pengganti->jumlah_jam }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                                <tfoot>
                                    <tr class="bg-light font-weight-bold">
                                        <td colspan="4" class="text-right">Total Jam Pengganti:</td>
                                        <td>{{ $jurnal->penggantiItems->sum('jumlah_jam') }}</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                @endif
            </div>
            <div class="card-footer">
                @if($jurnal->status == 'draft' || $jurnal->status == 'rejected')
                    @can('manage-jurnal')
                    <a href="{{ route('jurnal.edit', $jurnal->id) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    
                    <form action="{{ route('jurnal.destroy', $jurnal->id) }}" method="POST" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger" onclick="return confirm('Yakin ingin menghapus?')">
                            <i class="fas fa-trash"></i> Hapus
                        </button>
                    </form>
                    @endcan
                    
                    <form action="{{ route('jurnal.submitIndividual', $jurnal->id) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-primary" onclick="return confirm('Yakin ingin mengirim jurnal ini untuk persetujuan?')">
                            <i class="fas fa-paper-plane"></i> Submit
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            // Initialize tooltips with HTML support
            $('[data-toggle="tooltip"]').tooltip({
                html: true,
                container: 'body'
            });
            
            // Other existing JavaScript...
        });
    </script>
@stop







