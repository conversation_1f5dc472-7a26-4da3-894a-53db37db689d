<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Halaman;
use App\Models\Unit;
use App\Models\Article;
use App\Models\Event;
use App\Models\Achievement;
use App\Models\Extracurricular;
use Illuminate\Support\Facades\Log;

class JenjangController extends Controller
{
    private function getUnit($jenjang)
    {
        $jenjangId = strtoupper($jenjang === 'paud' ? 'pg' : $jenjang);
        return Unit::where('jenjang_id', $jenjangId)->firstOrFail();
    }

    public function showProfil($jenjang)
    {
        try {
            $unit = $this->getUnit($jenjang);
            $profil = Halaman::where('unit_id', $unit->id)
                ->where('tipe', 'profil')
                ->where('is_active', true)
                ->firstOrFail();

            return view('website.jenjang.profil', compact('profil', 'unit', 'jenjang'));
        } catch (\Exception $e) {
            Log::error('Error showing profile: ' . $e->getMessage());
            return $this->showError($jenjang, 'profil');
        }
    }

    public function showVisiMisi($jenjang)
    {
        try {
            $unit = $this->getUnit($jenjang);
            $visiMisi = Halaman::where('unit_id', $unit->id)
                ->where('tipe', 'visi-misi')
                ->where('is_active', true)
                ->firstOrFail();

            return view('website.jenjang.visi-misi', compact('visiMisi', 'unit', 'jenjang'));
        } catch (\Exception $e) {
            return $this->showError($jenjang, 'visi & misi');
        }
    }

    public function showSejarah($jenjang)
    {
        try {
            $unit = $this->getUnit($jenjang);
            $sejarah = Halaman::where('unit_id', $unit->id)
                ->where('tipe', 'sejarah')
                ->where('is_active', true)
                ->firstOrFail();

            return view('website.jenjang.sejarah', compact('sejarah', 'unit', 'jenjang'));
        } catch (\Exception $e) {
            return $this->showError($jenjang, 'sejarah');
        }
    }

    public function showArtikel($jenjang)
    {
        try {
            $unit = $this->getUnit($jenjang);
            
            // Tambahkan debugging untuk melihat unit_id
            \Log::info('Unit found:', ['unit_id' => $unit->id, 'jenjang' => $jenjang]);
            
            $articles = Article::where('unit_id', $unit->id)
                ->where('status', 'published')
                ->latest();
                
            // Tambahkan debugging untuk melihat query
            \Log::info('Query articles:', [
                'sql' => $articles->toSql(),
                'bindings' => $articles->getBindings()
            ]);
            
            $articles = $articles->paginate(9);
            
            // Tambahkan debugging untuk melihat hasil
            \Log::info('Articles found:', ['count' => $articles->count()]);

            return view('website.jenjang.artikel', compact('articles', 'unit', 'jenjang'));
        } catch (\Exception $e) {
            \Log::error('Error in showArtikel: ' . $e->getMessage());
            return $this->showError($jenjang, 'artikel');
        }
    }

    public function showEvent($jenjang)
    {
        $unit = $this->getUnit($jenjang);
        $events = Event::where('unit_id', $unit->id)
            ->latest()
            ->paginate(9);

        return view('website.jenjang.event', compact('events', 'unit', 'jenjang'));
    }

    public function showPrestasi($jenjang)
    {
        $unit = $this->getUnit($jenjang);
        $achievements = Achievement::where('unit_id', $unit->id)
            ->latest()
            ->paginate(10);

        return view('website.jenjang.prestasi', compact('achievements', 'unit', 'jenjang'));
    }

    public function showEkstrakurikuler($jenjang)
    {
        $unit = $this->getUnit($jenjang);
        $ekstrakurikuler = Extracurricular::where('unit_id', $unit->id)
            ->get();

        return view('website.jenjang.ekstrakurikuler', compact('ekstrakurikuler', 'unit', 'jenjang'));
    }

    private function showError($jenjang, $page)
    {
        return response()->view('errors.404', [
            'message' => ucfirst($page) . ' untuk jenjang ' . strtoupper($jenjang) . ' belum tersedia.',
            'back_url' => url()->previous(),
            'jenjang' => $jenjang
        ], 404);
    }
}
