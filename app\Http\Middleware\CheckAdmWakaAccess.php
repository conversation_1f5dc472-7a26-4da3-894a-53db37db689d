<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CheckAdmWakaAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        
        // Cek apakah user memiliki role yang sesuai
        if (!$user->hasAnyRole(['Kepala Sekolah', 'Waka Kurikulum', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Administrator'])) {
            return redirect()->route('dashboard')
                ->with('error', 'Anda tidak memiliki izin untuk mengakses ADM Waka.');
        }
        
        // Cek khusus untuk approve/reject
        if ($request->is('*/approve') || $request->is('*/reject')) {
            if (!$user->hasRole('Kepala Sekolah')) {
                return redirect()->route('dashboard')
                    ->with('error', 'Anda tidak memiliki izin untuk menyetujui/menolak ADM Waka.');
            }
        }

        return $next($request);
    }
}