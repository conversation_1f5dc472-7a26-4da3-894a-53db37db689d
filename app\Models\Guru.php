<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Guru extends Model
{
    protected $table = 'guru';
    protected $fillable = [
        'unit_id', 'nama', 'nik', 'jenis_kelamin', 'tempat_lahir', 
        'tanggal_lahir', 'nama_ibu_kandung', 'alamat', 'kelurahan', 
        'kecamatan', 'kabupaten', 'provinsi', 'agama', 'npwp', 
        'nama_wajib_pajak', 'kewarganegaraan', 'status_kawin', 
        'nama_pasangan', 'pekerjaan_pasangan', 'status_pegawai', 
        'niy', 'nuptk', 'jenis_ptk', 'sk_pengangkatan', 
        'tmt_pengangkatan', 'lembaga_pengangkat', 'pangkat_golongan', 
        'sk_penugasan', 'tmt_penugasan', 'lembaga_penugasan', 
        'pangkat_golongan_penugasan', 'no_telp', 'email', 
        'mata_pelajaran', 'status'
    ];

    // Relasi dengan Unit
    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }
    
    /**
     * Scope untuk memfilter guru berdasarkan unit
     */
    public function scopeByUnit($query, $unitId)
    {
        return $query->where('unit_id', $unitId);
    }
    
    /**
     * Scope untuk memfilter guru yang aktif
     */
    public function scopeAktif($query)
    {
        return $query->where('status', 'Aktif');
    }
    
    /**
     * Scope untuk mencari guru berdasarkan nama
     */
    public function scopeSearch($query, $keyword)
    {
        return $query->where('nama', 'like', "%{$keyword}%")
                     ->orWhere('nip', 'like', "%{$keyword}%")
                     ->orWhere('niy', 'like', "%{$keyword}%")
                     ->orWhere('nuptk', 'like', "%{$keyword}%");
    }
}
