<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('spps', function (Blueprint $table) {
            $table->id();
            $table->string('va_number')->unique();
            $table->string('nama');
            $table->date('tanggal_lahir');
            $table->decimal('spp_bulan_ini', 12, 2)->default(0);
            $table->decimal('tunggakan', 12, 2)->default(0);
            $table->decimal('buku', 12, 2)->default(0);
            $table->decimal('uang_program', 12, 2)->default(0);
            $table->decimal('les', 12, 2)->default(0);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('spps');
    }
};