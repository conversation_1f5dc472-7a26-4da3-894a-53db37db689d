@extends('adminlte::page')

@section('title', 'Tambah Jurnal Kegiatan')

@section('content_header')
    <h1>Tambah Jurnal Kegiatan</h1>
@stop

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Tambah Jurnal Kegiatan</h3>
                <div class="card-tools">
                    <a href="{{ route('jurnal.index') }}" class="btn btn-default btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if(session('error'))
                    <div class="alert alert-danger">{{ session('error') }}</div>
                @endif
                
                <form action="{{ route('jurnal.store') }}" method="POST">
                    @csrf
                    
                    <!-- Tambahkan informasi unit -->
                    <div class="form-group">
                        <label>Unit</label>
                        <input type="text" class="form-control" value="{{ auth()->user()->unit->nama_unit ?? '-' }}" disabled>
                        <small class="form-text text-muted">Unit akan otomatis terisi berdasarkan unit Anda</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="tanggal">Tanggal</label>
                        <input type="date" class="form-control @error('tanggal') is-invalid @enderror" 
                               id="tanggal" 
                               name="tanggal" 
                               value="{{ old('tanggal', now()->format('Y-m-d')) }}" 
                               required>
                        @error('tanggal')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="kegiatan">Kegiatan</label>
                        <textarea class="form-control @error('kegiatan') is-invalid @enderror" id="kegiatan" name="kegiatan" rows="5" required>{{ old('kegiatan') }}</textarea>
                        @error('kegiatan')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label>Kehadiran</label>
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="ada" name="ada" {{ old('ada') ? 'checked' : '' }}>
                            <label class="custom-control-label" for="ada">Ada</label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="tidak" name="tidak" {{ old('tidak') ? 'checked' : '' }}>
                            <label class="custom-control-label" for="tidak">Tidak</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="keterangan">Keterangan</label>
                        <textarea class="form-control @error('keterangan') is-invalid @enderror" id="keterangan" name="keterangan" rows="3">{{ old('keterangan') }}</textarea>
                        @error('keterangan')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <!-- Tambahkan exkul -->
                     <!-- Jam Eskul -->
            <div class="card mb-3">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Jam Eskul</h5>
                    <button type="button" class="btn btn-sm btn-light" id="tambahJamEskul">
                        <i class="fas fa-plus"></i> Tambah Jam Eskul
                    </button>
                </div>
                <div class="card-body" id="jamEskulContainer">
                    <div class="alert alert-info" id="noJamEskulAlert">
                        Tidak ada jam eskul.
                    </div>
                    
                    <div class="table-responsive" id="jamEskulTableContainer" style="display:none">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Nama Eskul</th>
                                    <th>Kelas</th>
                                    <th>Jumlah Siswa</th>
                                    <th>Kegiatan</th>
                                    <th>Jumlah Jam</th>
                                    <th>Keterangan</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="jamEskulBody"></tbody>
                            <tfoot>
                                <tr class="bg-light font-weight-bold">
                                    <td colspan="4" class="text-right">Total Jam Eskul:</td>
                                    <td id="totalJamEskul">0</td>
                                    <td></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <input type="hidden" name="total_jam_eskul" value="0">
                </div>
            </div>

            <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Fungsi untuk menambah jam eskul
                const tambahJamEskulBtn = document.getElementById('tambahJamEskul');
                if (tambahJamEskulBtn) {
                    tambahJamEskulBtn.addEventListener('click', function() {
                        // Sembunyikan alert tidak ada jam eskul
                        const noJamEskulAlert = document.getElementById('noJamEskulAlert');
                        if (noJamEskulAlert) {
                            noJamEskulAlert.style.display = 'none';
                        }
                        
                        // Tampilkan tabel jika belum ditampilkan
                        const tableContainer = document.getElementById('jamEskulTableContainer');
                        if (tableContainer) {
                            tableContainer.style.display = '';
                        } else {
                            // Jika belum ada tabel, buat tabel baru
                            const container = document.getElementById('jamEskulContainer');
                            container.innerHTML = `
                                <div class="table-responsive" id="jamEskulTableContainer">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Nama Eskul</th>
                                                <th>Kelas</th>
                                                <th>Jumlah Siswa</th>
                                                <th>Kegiatan</th>
                                                <th>Jumlah Jam</th>
                                                <th>Keterangan</th>
                                                <th>Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody id="jamEskulBody"></tbody>
                                        <tfoot>
                                            <tr class="bg-light font-weight-bold">
                                                <td colspan="4" class="text-right">Total Jam Eskul:</td>
                                                <td id="totalJamEskul">0</td>
                                                <td></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                                <input type="hidden" name="total_jam_eskul" value="0">
                            `;
                        }
                        
                        const tbody = document.getElementById('jamEskulBody');
                        const index = tbody.querySelectorAll('tr').length;
                        
                        const newRow = document.createElement('tr');
                        newRow.innerHTML = `
                            <td>
                                <input type="text" name="jam_eskul[${index}][nama_eskul]" class="form-control" required>
                            </td>
                            <td>
                                <input type="text" name="jam_eskul[${index}][kelas]" class="form-control" required>
                            </td>
                            <td>
                                <input type="number" name="jam_eskul[${index}][jumlah_siswa]" class="form-control" min="1" value="1" required>
                            </td>
                            <td>
                                <input type="text" name="jam_eskul[${index}][kegiatan]" class="form-control" required>
                            </td>
                            <td>
                                <input type="number" name="jam_eskul[${index}][jumlah_jam]" class="form-control jam-eskul-durasi" min="0.5" step="0.5" value="1" required>
                            </td>
                            <td>
                                <input type="text" name="jam_eskul[${index}][keterangan]" class="form-control" placeholder="Keterangan jam pengganti">
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-danger hapus-jam-eskul">
                                    <i class="fas fa-trash"></i> Hapus
                                </button>
                            </td>
                        `;
                        
                        tbody.appendChild(newRow);
                        
                        // Event listener untuk hapus
                        newRow.querySelector('.hapus-jam-eskul').addEventListener('click', function() {
                            if (confirm('Apakah Anda yakin ingin menghapus baris ini?')) {
                                this.closest('tr').remove();
                                hitungTotalJamEskul();
                                
                                // Jika tidak ada baris lagi, tampilkan alert
                                if (tbody.querySelectorAll('tr').length === 0) {
                                    document.getElementById('noJamEskulAlert').style.display = '';
                                    document.getElementById('jamEskulTableContainer').style.display = 'none';
                                }
                            }
                        });
                        
                        // Event listener untuk durasi
                        newRow.querySelector('.jam-eskul-durasi').addEventListener('change', hitungTotalJamEskul);
                        
                        // Update total jam eskul
                        hitungTotalJamEskul();
                    });
                }
                
                // Fungsi untuk menghitung total jam eskul
                function hitungTotalJamEskul() {
                    let total = 0;
                    document.querySelectorAll('.jam-eskul-durasi').forEach(function(input) {
                        total += parseFloat(input.value || 0);
                    });
                    
                    const totalElement = document.getElementById('totalJamEskul');
                    if (totalElement) {
                        totalElement.textContent = total.toFixed(1);
                        document.querySelector('input[name="total_jam_eskul"]').value = total.toFixed(1);
                    }
                }
            });
            </script>
                    <!-- Tambahkan keterangan untuk jam pengganti -->
                    <div class="card mb-3">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Jam Pengganti</h5>
                            <button type="button" class="btn btn-sm btn-light" id="tambahJamPengganti">
                                <i class="fas fa-plus"></i> Tambah Jam Pengganti
                            </button>
                        </div>
                        <div class="card-body" id="jamPenggantiContainer">
                            <div class="alert alert-info" id="noJamPenggantiAlert">
                                Tidak ada jam pengganti.
                            </div>
                            
                            <div class="table-responsive" id="jamPenggantiTableContainer" style="display:none">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Kelas</th>
                                            <th>Jam Ke</th>
                                            <th>Guru Yang Diganti</th>
                                            <th>Jumlah Jam</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody id="jamPenggantiBody"></tbody>
                                    <tfoot>
                                        <tr class="bg-light font-weight-bold">
                                            <td colspan="3" class="text-right">Total Jam Pengganti:</td>
                                            <td id="totalJamPengganti">0</td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <input type="hidden" name="total_jam_pengganti" value="0">
                        </div>
                    </div>

                    <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Fungsi untuk menambah jam pengganti
                        const tambahJamPenggantiBtn = document.getElementById('tambahJamPengganti');
                        if (tambahJamPenggantiBtn) {
                            tambahJamPenggantiBtn.addEventListener('click', function() {
                                // Sembunyikan alert tidak ada jam pengganti
                                const noJamPenggantiAlert = document.getElementById('noJamPenggantiAlert');
                                if (noJamPenggantiAlert) {
                                    noJamPenggantiAlert.style.display = 'none';
                                }
                                
                                // Tampilkan tabel jika belum ditampilkan
                                const tableContainer = document.getElementById('jamPenggantiTableContainer');
                                if (tableContainer) {
                                    tableContainer.style.display = '';
                                }
                                
                                const tbody = document.getElementById('jamPenggantiBody');
                                const index = tbody.querySelectorAll('tr').length;
                                
                                const newRow = document.createElement('tr');
                                newRow.innerHTML = `
                                    <td>
                                        <input type="text" name="jam_pengganti[${index}][kelas]" class="form-control" required>
                                    </td>
                                    <td>
                                        <input type="text" name="jam_pengganti[${index}][jam_ke]" class="form-control" required>
                                    </td>
                                    <td>
                                        <input type="text" name="jam_pengganti[${index}][guru_diganti]" class="form-control" required>
                                    </td>
                                    <td>
                                        <input type="number" name="jam_pengganti[${index}][jumlah_jam]" class="form-control jam-pengganti-durasi" min="0.5" step="0.5" value="1" required>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-danger hapus-jam-pengganti">
                                            <i class="fas fa-trash"></i> Hapus
                                        </button>
                                    </td>
                                `;
                                
                                tbody.appendChild(newRow);
                                
                                // Event listener untuk hapus
                                newRow.querySelector('.hapus-jam-pengganti').addEventListener('click', function() {
                                    if (confirm('Apakah Anda yakin ingin menghapus baris ini?')) {
                                        this.closest('tr').remove();
                                        hitungTotalJamPengganti();
                                        
                                        // Jika tidak ada baris lagi, tampilkan alert
                                        if (tbody.querySelectorAll('tr').length === 0) {
                                            document.getElementById('noJamPenggantiAlert').style.display = '';
                                            document.getElementById('jamPenggantiTableContainer').style.display = 'none';
                                        }
                                    }
                                });
                                
                                // Event listener untuk durasi
                                newRow.querySelector('.jam-pengganti-durasi').addEventListener('change', hitungTotalJamPengganti);
                                
                                // Update total jam pengganti
                                hitungTotalJamPengganti();
                            });
                        }
                        
                        // Fungsi untuk menghitung total jam pengganti
                        function hitungTotalJamPengganti() {
                            let total = 0;
                            document.querySelectorAll('.jam-pengganti-durasi').forEach(function(input) {
                                total += parseFloat(input.value || 0);
                            });
                            
                            const totalElement = document.getElementById('totalJamPengganti');
                            if (totalElement) {
                                totalElement.textContent = total.toFixed(1);
                                document.querySelector('input[name="total_jam_pengganti"]').value = total.toFixed(1);
                            }
                        }
                    });
                    </script>
                    
                    <button type="submit" class="btn btn-primary">Simpan</button>
                    <a href="{{ route('jurnal.index') }}" class="btn btn-secondary">Batal</a>
                </form>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
<script>
$(document).ready(function() {
    // Pastikan hanya satu checkbox yang bisa dipilih
    $('#ada, #tidak').change(function() {
        if (this.checked) {
            const otherId = this.id === 'ada' ? '#tidak' : '#ada';
            $(otherId).prop('checked', false);
        }
    });
});
</script>
@stop
