@extends('adminlte::page')

@section('title', 'Daftar Tujuan Pembelajaran')

@section('content_header')
    <h1>Daftar Tujuan Pem<PERSON>ajaran</h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Tuju<PERSON></h3>
            <div class="card-tools">
                <a href="{{ route('penilaian.tujuan-pembelajaran.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Tambah Tujuan Pembelajaran
                </a>
            </div>
        </div>
        <div class="card-body">
            <!-- Pesan Sukses -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h5><i class="icon fas fa-check"></i> Sukses!</h5>
                    {{ session('success') }}
                </div>
            @endif

            <!-- <PERSON><PERSON> Error -->
            @if(session('error'))
                <div class="alert alert-danger alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h5><i class="icon fas fa-ban"></i> Error!</h5>
                    {{ session('error') }}
                </div>
            @endif

            <!-- Tabel Tujuan Pembelajaran -->
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="tujuanPembelajaranTable">
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th width="30%">Cakapan Pembelajaran</th>
                            <th>Deskripsi</th>
                            <th width="15%">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($tujuanPembelajarans as $index => $tp)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $tp->cakapanPembelajaran->deskripsi }}</td>
                                <td>{{ $tp->deskripsi }}</td>
                                <td>
                                    <a href="{{ route('penilaian.tujuan-pembelajaran.edit', $tp->id) }}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('penilaian.tujuan-pembelajaran.destroy', $tp->id) }}" method="POST" style="display: inline-block;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Apakah Anda yakin ingin menghapus tujuan pembelajaran ini?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4" class="text-center">Tidak ada data tujuan pembelajaran</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            // Inisialisasi DataTables
            $('#tujuanPembelajaranTable').DataTable({
                "paging": true,
                "lengthChange": false,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                }
            });
        });
    </script>
@stop