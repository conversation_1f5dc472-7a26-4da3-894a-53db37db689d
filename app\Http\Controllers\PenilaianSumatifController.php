<?php

namespace App\Http\Controllers;

use App\Models\PenilaianSumatif;
use App\Models\PesertaDidik;
use App\Models\Kelas;
use App\Models\MataPelajaran;
use App\Models\KompetensiDasar;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PenilaianSumatifController extends Controller
{
    /**
     * Menampilkan daftar penilaian sumatif
     */
    public function index()
    {
        // Ambil data penilaian sumatif dengan relasi
        $penilaianSumatif = PenilaianSumatif::with(['siswa', 'kelas', 'mataPelajaran'])
            ->latest()
            ->paginate(10);
            
        return view('nilai.sumatif.index', compact('penilaianSumatif'));
    }

    /**
     * Menampilkan form untuk membuat penilaian sumatif baru
     */
    public function create()
    {
        // Ambil data untuk dropdown
        $siswa = PesertaDidik::where('is_active', true)->get();
        $kelas = Kelas::all();
        $mataPelajaran = MataPelajaran::all();
        $kompetensiDasar = KompetensiDasar::all();
        
        return view('nilai.sumatif.create', compact(
            'siswa', 
            'kelas', 
            'mataPelajaran', 
            'kompetensiDasar'
        ));
    }

    /**
     * Menyimpan penilaian sumatif baru ke database
     */
    public function store(Request $request)
    {
        // Validasi input
        $validated = $request->validate([
            'siswa_id' => 'required|exists:peserta_didik,id',
            'kelas_id' => 'required|exists:kelas,id',
            'mata_pelajaran_id' => 'required|exists:mata_pelajaran,id',
            'kompetensi_dasar_id' => 'required|exists:kompetensi_dasar,id',
            'tanggal' => 'required|date',
            'jenis_penilaian' => 'required|string',
            'dimensi' => 'required|string',
            'nilai_angka' => 'required|numeric|min:0|max:100',
            'nilai_huruf' => 'nullable|string|max:2',
            'deskripsi' => 'nullable|string',
        ]);
        
        // Tambahkan user yang membuat
        $validated['created_by'] = Auth::id();
        
        // Simpan data
        PenilaianSumatif::create($validated);
        
        return redirect()->route('nilai.sumatif.index')
            ->with('success', 'Penilaian sumatif berhasil ditambahkan');
    }

    /**
     * Menampilkan detail penilaian sumatif
     */
    public function show(PenilaianSumatif $penilaianSumatif)
    {
        return view('nilai.sumatif.show', compact('penilaianSumatif'));
    }

    /**
     * Menampilkan form untuk mengedit penilaian sumatif
     */
    public function edit(PenilaianSumatif $penilaianSumatif)
    {
        // Ambil data untuk dropdown
        $siswa = PesertaDidik::where('is_active', true)->get();
        $kelas = Kelas::all();
        $mataPelajaran = MataPelajaran::all();
        $kompetensiDasar = KompetensiDasar::all();
        
        return view('nilai.sumatif.edit', compact(
            'penilaianSumatif',
            'siswa', 
            'kelas', 
            'mataPelajaran', 
            'kompetensiDasar'
        ));
    }

    /**
     * Memperbarui penilaian sumatif di database
     */
    public function update(Request $request, PenilaianSumatif $penilaianSumatif)
    {
        // Validasi input
        $validated = $request->validate([
            'siswa_id' => 'required|exists:peserta_didik,id',
            'kelas_id' => 'required|exists:kelas,id',
            'mata_pelajaran_id' => 'required|exists:mata_pelajaran,id',
            'kompetensi_dasar_id' => 'required|exists:kompetensi_dasar,id',
            'tanggal' => 'required|date',
            'jenis_penilaian' => 'required|string',
            'dimensi' => 'required|string',
            'nilai_angka' => 'required|numeric|min:0|max:100',
            'nilai_huruf' => 'nullable|string|max:2',
            'deskripsi' => 'nullable|string',
        ]);
        
        // Perbarui data
        $penilaianSumatif->update($validated);
        
        return redirect()->route('nilai.sumatif.index')
            ->with('success', 'Penilaian sumatif berhasil diperbarui');
    }

    /**
     * Menghapus penilaian sumatif dari database
     */
    public function destroy(PenilaianSumatif $penilaianSumatif)
    {
        $penilaianSumatif->delete();
        
        return redirect()->route('nilai.sumatif.index')
            ->with('success', 'Penilaian sumatif berhasil dihapus');
    }
}
