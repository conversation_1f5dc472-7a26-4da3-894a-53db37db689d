@extends('layouts.admin')

@section('title', 'Data Tahun Ajaran')

@section('plugins.Datatables', true)

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar Tahun Ajaran</h3>
        <div class="card-tools">
            <button class="btn btn-primary" data-toggle="modal" data-target="#tambahTahunAjaran">
                <i class="fas fa-plus"></i> Tambah Tahun Ajaran
            </button>
        </div>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-check"></i> Sukses!</h5>
                {{ session('success') }}
            </div>
        @endif
        
        @if(session('error'))
            <div class="alert alert-danger alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-ban"></i> Error!</h5>
                {{ session('error') }}
            </div>
        @endif
        
        <table class="table table-bordered table-striped" id="tahunAjaranTable">
            <thead>
                <tr>
                    <th width="5%">No</th>
                    <th>Nama</th>
                    <th>Tanggal Mulai</th>
                    <th>Tanggal Selesai</th>
                    <th>Status</th>
                    <th width="20%">Aksi</th>
                </tr>
            </thead>
            <tbody>
                @forelse($tahunAjarans as $ta)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $ta->nama }}</td>
                        <td>{{ $ta->tanggal_mulai->format('d-m-Y') }}</td>
                        <td>{{ $ta->tanggal_selesai->format('d-m-Y') }}</td>
                        <td>
                            @if($ta->aktif)
                                <span class="badge badge-success">Aktif</span>
                            @else
                                <span class="badge badge-secondary">Tidak Aktif</span>
                            @endif
                        </td>
                        <td>
                            @if(!$ta->aktif)
                                <form action="{{ route('tahunajaran.setActive', $ta->id) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit" class="btn btn-sm btn-success">
                                        <i class="fas fa-check"></i> Aktifkan
                                    </button>
                                </form>
                            @endif
                            
                            <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#editTahunAjaran{{ $ta->id }}">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            
                            <form action="{{ route('tahunajaran.destroy', $ta->id) }}" method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Yakin hapus data?')">
                                    <i class="fas fa-trash"></i> Hapus
                                </button>
                            </form>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6" class="text-center">Tidak ada data</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>

<!-- Modal Tambah Tahun Ajaran -->
<div class="modal fade" id="tambahTahunAjaran" tabindex="-1" role="dialog" aria-labelledby="tambahTahunAjaranLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form action="{{ route('tahunajaran.store') }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="tambahTahunAjaranLabel">Tambah Tahun Ajaran</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Nama Tahun Ajaran <span class="text-danger">*</span></label>
                        <input type="text" name="nama" class="form-control" placeholder="Contoh: 2024/2025" required>
                    </div>
                    <div class="form-group">
                        <label>Tanggal Mulai <span class="text-danger">*</span></label>
                        <input type="date" name="tanggal_mulai" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label>Tanggal Selesai <span class="text-danger">*</span></label>
                        <input type="date" name="tanggal_selesai" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="aktif" name="aktif" value="1">
                            <label class="custom-control-label" for="aktif">Aktifkan sebagai tahun ajaran saat ini</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit Tahun Ajaran -->
@foreach($tahunAjarans as $ta)
<div class="modal fade" id="editTahunAjaran{{ $ta->id }}" tabindex="-1" role="dialog" aria-labelledby="editTahunAjaranLabel{{ $ta->id }}" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form action="{{ route('tahunajaran.update', $ta->id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-header">
                    <h5 class="modal-title" id="editTahunAjaranLabel{{ $ta->id }}">Edit Tahun Ajaran</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Nama Tahun Ajaran <span class="text-danger">*</span></label>
                        <input type="text" name="nama" class="form-control" value="{{ $ta->nama }}" required>
                    </div>
                    <div class="form-group">
                        <label>Tanggal Mulai <span class="text-danger">*</span></label>
                        <input type="date" name="tanggal_mulai" class="form-control" value="{{ $ta->tanggal_mulai->format('Y-m-d') }}" required>
                    </div>
                    <div class="form-group">
                        <label>Tanggal Selesai <span class="text-danger">*</span></label>
                        <input type="date" name="tanggal_selesai" class="form-control" value="{{ $ta->tanggal_selesai->format('Y-m-d') }}" required>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="aktif{{ $ta->id }}" name="aktif" value="1" {{ $ta->aktif ? 'checked' : '' }}>
                            <label class="custom-control-label" for="aktif{{ $ta->id }}">Aktifkan sebagai tahun ajaran saat ini</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endforeach

@endsection

@section('js')
<script>
$(function () {
    $('#tahunAjaranTable').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.19/i18n/Indonesian.json"
        }
    });
});
</script>
@endsection