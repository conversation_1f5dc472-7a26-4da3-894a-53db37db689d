<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Illuminate\Support\Collection;

class JadwalExportPerKelas implements WithMultipleSheets
{
    protected $jadwalList;

    public function __construct(Collection $jadwalList)
    {
        $this->jadwalList = $jadwalList;
    }

    public function sheets(): array
    {
        $sheets = [];
        
        foreach ($this->jadwalList as $jadwal) {
            $sheets[] = new JadwalPerKelasSheet($jadwal);
        }

        return $sheets;
    }
}