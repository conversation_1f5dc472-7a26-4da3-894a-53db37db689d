@extends('layouts.admin')

@section('title', 'Edit Guru')
 
@section('page_title', 'Edit Data Guru')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Form Edit Guru</h3>
        <div class="card-tools">
            <a href="{{ route('gtk.guru.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
    <div class="card-body">
        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif
        <!-- Debuging di view -->
        <div class="d-none">
            Debug: Form action = {{ route('gtk.guru.update', $guru->id) }}
        </div>

        <form action="{{ route('gtk.guru.update', $guru->id) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            
            <!-- IDENTITAS SEKOLAH -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <h6 class="section-title font-weight-bold">IDENTITAS SEKOLAH</h6>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Nama Sekolah <span class="text-danger">*</span></label>
                        <select name="unit_id" id="unit_id" class="form-control @error('unit_id') is-invalid @enderror" required>
                            <option value="">Pilih Unit</option>
                            @foreach($units as $unit)
                                <option value="{{ $unit->id }}" {{ old('unit_id', $guru->unit_id) == $unit->id ? 'selected' : '' }}>{{ $unit->nama_unit }}</option>
                            @endforeach
                        </select>
                        @error('unit_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- IDENTITAS GURU -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <h6 class="section-title font-weight-bold">IDENTITAS GURU</h6>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Nama Lengkap <span class="text-danger">*</span></label>
                        <input type="text" name="nama" class="form-control @error('nama') is-invalid @enderror" value="{{ old('nama', $guru->nama) }}" required>
                        @error('nama')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>NIK <span class="text-danger">*</span></label>
                        <input type="text" name="nik" class="form-control @error('nik') is-invalid @enderror" value="{{ old('nik', $guru->nik) }}" required>
                        @error('nik')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Jenis Kelamin <span class="text-danger">*</span></label>
                        <select name="jenis_kelamin" class="form-control @error('jenis_kelamin') is-invalid @enderror" required>
                            <option value="">Pilih Jenis Kelamin</option>
                            <option value="L" {{ old('jenis_kelamin', $guru->jenis_kelamin) == 'L' ? 'selected' : '' }}>Laki-laki</option>
                            <option value="P" {{ old('jenis_kelamin', $guru->jenis_kelamin) == 'P' ? 'selected' : '' }}>Perempuan</option>
                        </select>
                        @error('jenis_kelamin')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Tempat Lahir <span class="text-danger">*</span></label>
                        <input type="text" name="tempat_lahir" class="form-control @error('tempat_lahir') is-invalid @enderror" value="{{ old('tempat_lahir', $guru->tempat_lahir) }}" required>
                        @error('tempat_lahir')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Tanggal Lahir <span class="text-danger">*</span></label>
                        <input type="date" name="tanggal_lahir" class="form-control @error('tanggal_lahir') is-invalid @enderror" value="{{ old('tanggal_lahir', $guru->tanggal_lahir) }}" required>
                        @error('tanggal_lahir')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Nama Ibu Kandung <span class="text-danger">*</span></label>
                        <input type="text" name="nama_ibu_kandung" class="form-control @error('nama_ibu_kandung') is-invalid @enderror" value="{{ old('nama_ibu_kandung', $guru->nama_ibu_kandung) }}" required>
                        @error('nama_ibu_kandung')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- ALAMAT -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <h6 class="section-title font-weight-bold">ALAMAT</h6>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <label>Alamat <span class="text-danger">*</span></label>
                        <textarea name="alamat" class="form-control @error('alamat') is-invalid @enderror" required>{{ old('alamat', $guru->alamat) }}</textarea>
                        @error('alamat')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Kelurahan <span class="text-danger">*</span></label>
                        <input type="text" name="kelurahan" class="form-control @error('kelurahan') is-invalid @enderror" value="{{ old('kelurahan', $guru->kelurahan) }}" required>
                        @error('kelurahan')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Kecamatan <span class="text-danger">*</span></label>
                        <input type="text" name="kecamatan" class="form-control @error('kecamatan') is-invalid @enderror" value="{{ old('kecamatan', $guru->kecamatan) }}" required>
                        @error('kecamatan')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Kabupaten <span class="text-danger">*</span></label>
                        <input type="text" name="kabupaten" class="form-control @error('kabupaten') is-invalid @enderror" value="{{ old('kabupaten', $guru->kabupaten) }}" required>
                        @error('kabupaten')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Provinsi <span class="text-danger">*</span></label>
                        <input type="text" name="provinsi" class="form-control @error('provinsi') is-invalid @enderror" value="{{ old('provinsi', $guru->provinsi) }}" required>
                        @error('provinsi')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- DATA PRIBADI -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <h6 class="section-title font-weight-bold">DATA PRIBADI</h6>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Agama <span class="text-danger">*</span></label>
                        <select name="agama" class="form-control @error('agama') is-invalid @enderror" required>
                            <option value="">Pilih Agama</option>
                            <option value="Islam" {{ old('agama', $guru->agama) == 'Islam' ? 'selected' : '' }}>Islam</option>
                            <option value="Kristen" {{ old('agama', $guru->agama) == 'Kristen' ? 'selected' : '' }}>Kristen</option>
                            <option value="Katolik" {{ old('agama', $guru->agama) == 'Katolik' ? 'selected' : '' }}>Katolik</option>
                            <option value="Hindu" {{ old('agama', $guru->agama) == 'Hindu' ? 'selected' : '' }}>Hindu</option>
                            <option value="Buddha" {{ old('agama', $guru->agama) == 'Buddha' ? 'selected' : '' }}>Buddha</option>
                            <option value="Konghucu" {{ old('agama', $guru->agama) == 'Konghucu' ? 'selected' : '' }}>Konghucu</option>
                        </select>
                        @error('agama')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>NPWP</label>
                        <input type="text" name="npwp" class="form-control @error('npwp') is-invalid @enderror" value="{{ old('npwp', $guru->npwp) }}">
                        @error('npwp')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Nama Wajib Pajak</label>
                        <input type="text" name="nama_wajib_pajak" class="form-control @error('nama_wajib_pajak') is-invalid @enderror" value="{{ old('nama_wajib_pajak', $guru->nama_wajib_pajak) }}">
                        @error('nama_wajib_pajak')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Kewarganegaraan <span class="text-danger">*</span></label>
                        <input type="text" name="kewarganegaraan" class="form-control @error('kewarganegaraan') is-invalid @enderror" value="{{ old('kewarganegaraan', $guru->kewarganegaraan) }}" required>
                        @error('kewarganegaraan')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Status Kawin <span class="text-danger">*</span></label>
                        <select name="status_kawin" class="form-control @error('status_kawin') is-invalid @enderror" required>
                            <option value="">Pilih Status</option>
                            <option value="Belum Kawin" {{ old('status_kawin', $guru->status_kawin) == 'Belum Kawin' ? 'selected' : '' }}>Belum Kawin</option>
                            <option value="Kawin" {{ old('status_kawin', $guru->status_kawin) == 'Kawin' ? 'selected' : '' }}>Kawin</option>
                            <option value="Cerai Hidup" {{ old('status_kawin', $guru->status_kawin) == 'Cerai Hidup' ? 'selected' : '' }}>Cerai Hidup</option>
                            <option value="Cerai Mati" {{ old('status_kawin', $guru->status_kawin) == 'Cerai Mati' ? 'selected' : '' }}>Cerai Mati</option>
                        </select>
                        @error('status_kawin')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Nama Pasangan</label>
                        <input type="text" name="nama_pasangan" class="form-control @error('nama_pasangan') is-invalid @enderror" value="{{ old('nama_pasangan', $guru->nama_pasangan) }}">
                        @error('nama_pasangan')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Pekerjaan Pasangan</label>
                        <input type="text" name="pekerjaan_pasangan" class="form-control @error('pekerjaan_pasangan') is-invalid @enderror" value="{{ old('pekerjaan_pasangan', $guru->pekerjaan_pasangan) }}">
                        @error('pekerjaan_pasangan')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- KEPEGAWAIAN -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <h6 class="section-title font-weight-bold">KEPEGAWAIAN</h6>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Status Pegawai <span class="text-danger">*</span></label>
                        <select name="status_pegawai" class="form-control @error('status_pegawai') is-invalid @enderror" required>
                            <option value="">Pilih Status</option>
                            <option value="Tetap" {{ old('status_pegawai', $guru->status_pegawai) == 'Tetap' ? 'selected' : '' }}>Tetap</option>
                            <option value="Kontrak" {{ old('status_pegawai', $guru->status_pegawai) == 'Kontrak' ? 'selected' : '' }}>Kontrak</option>
                            <option value="Honorer" {{ old('status_pegawai', $guru->status_pegawai) == 'Honorer' ? 'selected' : '' }}>Honorer</option>
                        </select>
                        @error('status_pegawai')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>NIY</label>
                        <input type="text" name="niy" class="form-control @error('niy') is-invalid @enderror" value="{{ old('niy', $guru->niy) }}">
                        @error('niy')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>NUPTK</label>
                        <input type="text" name="nuptk" class="form-control @error('nuptk') is-invalid @enderror" value="{{ old('nuptk', $guru->nuptk) }}">
                        @error('nuptk')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Jenis PTK <span class="text-danger">*</span></label>
                        <select name="jenis_ptk" class="form-control @error('jenis_ptk') is-invalid @enderror" required>
                            <option value="">Pilih Jenis PTK</option>
                            <option value="Guru Kelas" {{ old('jenis_ptk', $guru->jenis_ptk) == 'Guru Kelas' ? 'selected' : '' }}>Guru Kelas</option>
                            <option value="Guru Mata Pelajaran" {{ old('jenis_ptk', $guru->jenis_ptk) == 'Guru Mata Pelajaran' ? 'selected' : '' }}>Guru Mata Pelajaran</option>
                            <option value="Guru BK" {{ old('jenis_ptk', $guru->jenis_ptk) == 'Guru BK' ? 'selected' : '' }}>Guru BK</option>
                            <option value="Guru Pendamping" {{ old('jenis_ptk', $guru->jenis_ptk) == 'Guru Pendamping' ? 'selected' : '' }}>Guru Pendamping</option>
                        </select>
                        @error('jenis_ptk')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Mata Pelajaran</label>
                        <input type="text" name="mata_pelajaran" class="form-control @error('mata_pelajaran') is-invalid @enderror" value="{{ old('mata_pelajaran', $guru->mata_pelajaran) }}">
                        @error('mata_pelajaran')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Penugasan -->
            <div class="col-md-4">
                    <div class="form-group">
                        <label>Lembaga Penugasan</label>
                        <input type="text" name="lembaga_penugasan" class="form-control @error('lembaga_penugasan') is-invalid @enderror" value="{{ old('lembaga_penugasan', $guru->lembaga_penugasan) }}">
                        @error('lembaga_penugasan')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Status <span class="text-danger">*</span></label>
                        <select name="status" class="form-control" required>
                            <option value="Aktif" {{ old('status', $guru->status) == 'Aktif' ? 'selected' : '' }}>Aktif</option>
                            <option value="Non-Aktif" {{ old('status', $guru->status) == 'Non-Aktif' ? 'selected' : '' }}>Non-Aktif</option>
                        </select>
                    </div>
                </div>

            <!-- KONTAK -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <h6 class="section-title font-weight-bold">KONTAK</h6>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>No. HP</label>
                        <input type="text" name="no_telp" class="form-control @error('no_telp') is-invalid @enderror" value="{{ old('no_telp', $guru->no_telp) }}">
                        @error('no_telp')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" name="email" class="form-control @error('email') is-invalid @enderror" value="{{ old('email', $guru->email) }}">
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                <a href="{{ route('gtk.guru.index') }}" class="btn btn-secondary">Kembali</a>
            </div>
        </form>
    </div>
</div>
@endsection

@section('css')
    <!-- CSS tambahan jika diperlukan -->
@stop

@section('js')
    <!-- JavaScript tambahan jika diperlukan -->
@stop









