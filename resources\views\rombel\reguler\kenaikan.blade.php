@extends('adminlte::page')

@section('title', 'Kenai<PERSON>')

@section('content_header')
    <h1><PERSON><PERSON><PERSON></h1>
@stop

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Kenai<PERSON> Tahun Ajar<PERSON> {{ $tahunAjaranAktif->nama }}</h3>
            </div>
            <div class="card-body">
                <input type="hidden" id="tahunAjaran" value="{{ $tahunAjaranBerikutnya }}">

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                
                @if(session('error'))
                    <div class="alert alert-danger">{{ session('error') }}</div>
                @endif

                <!-- Filter Jenjang -->
                @if(auth()->user()->hasRole('Administrator'))
                <div class="form-group">
                    <label>Filter Jenjang:</label>
                    <select id="filterJenjang" class="form-control">
                        <option value="">Se<PERSON>a <PERSON></option>
                        <option value="1">PG</option>
                        <option value="2">SD</option>
                        <option value="3">SMP</option>
                        <option value="4">SMA</option>
                    </select>
                </div>
                @endif

                <div class="row">
                    <!-- Daftar Kelas Asal -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary">
                                <h3 class="card-title">Kelas Asal</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label>Pilih Kelas Asal:</label>
                                    <select id="kelasAsal" class="form-control">
                                        <option value="">-- Pilih Kelas --</option>
                                        @foreach($kelas as $k)
                                            <option value="{{ $k->id }}" data-jenjang-id="{{ $k->jenjang_id }}">{{ $k->nama }} ({{ $k->siswa->count() }} siswa)</option>
                                        @endforeach
                                    </select>
                                </div>
                                
                                <div id="daftarSiswaAsal" class="mt-3">
                                    <h5>Daftar Siswa</h5>
                                    <form id="formKenaikanKelas" action="{{ route('rombel.reguler.kenaikan.terpilih') }}" method="POST">
                                        @csrf
                                        <input type="hidden" name="kelas_asal_id" id="kelasAsalId">
                                        <input type="hidden" name="kelas_tujuan_id" id="kelasTujuanId">
                                        <input type="hidden" name="tahun_ajaran" id="tahunAjaranKenaikan">
                                        
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped" id="tabelSiswaAsal">
                                                <thead>
                                                    <tr>
                                                        <th width="5%">No</th>
                                                        <th width="15%">NIS</th>
                                                        <th>Nama</th>
                                                        <th width="10%"><input type="checkbox" id="checkAll"> Pilih</th>
                                                        <th width="15%">Aksi</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($kelas as $k)
                                                        @foreach($k->siswa as $index => $s)
                                                        <tr class="siswa-row" data-kelas-id="{{ $k->id }}" style="display: none;">
                                                            <td>{{ $index + 1 }}</td>
                                                            <td>{{ $s->nis ?? '-' }}</td>
                                                            <td>{{ $s->nama }}</td>
                                                            <td><input type="checkbox" name="siswa_ids[]" value="{{ $s->id }}" class="siswa-checkbox"></td>
                                                            <td>
                                                                <button type="button" class="btn btn-sm btn-primary btn-kenaikan-individu" 
                                                                        data-id="{{ $s->id }}" 
                                                                        data-nama="{{ $s->nama }}">
                                                                    <i class="fas fa-arrow-right"></i> Naikkan
                                                                </button>
                                                            </td>
                                                        </tr>
                                                        @endforeach
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                        <button type="submit" class="btn btn-success mt-2" id="btnNaikkanTerpilih">
                                            <i class="fas fa-arrow-right"></i> Naikkan Siswa Terpilih
                                        </button>
                                       <!-- <button type="button" class="btn btn-info mt-2 ml-2" id="btnDebugForm">
                                            <i class="fas fa-bug"></i> Debug Form
                                        </button> -->
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Daftar Kelas Tujuan -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-success">
                                <h3 class="card-title">Kelas Tujuan (Tahun Ajaran {{ $tahunAjaranBerikutnya }})</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label>Pilih Kelas Tujuan:</label>
                                    <select id="kelasTujuan" class="form-control" disabled>
                                        <option value="">-- Pilih Kelas --</option>
                                        @foreach($kelasTarget as $kt)
                                            <option value="{{ $kt->id }}" data-jenjang-id="{{ $kt->jenjang_id }}">{{ $kt->nama }} ({{ $kt->siswa->count() }} siswa)</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Kenaikan Individu -->
<div class="modal fade" id="modalKenaikanIndividu" tabindex="-1" role="dialog" aria-labelledby="modalKenaikanIndividuLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalKenaikanIndividuLabel">Kenaikan Kelas Individu</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('rombel.reguler.kenaikan.individu') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="siswa_id" id="siswaId">
                    <p>Naikkan siswa <strong id="namaSiswa"></strong> ke kelas:</p>
                    <div class="form-group">
                        <select name="kelas_id" class="form-control" required>
                            <option value="">-- Pilih Kelas --</option>
                            @foreach($kelasTarget as $kt)
                                <option value="{{ $kt->id }}">{{ $kt->nama }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Naikkan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
    <meta name="csrf-token" content="{{ csrf_token() }}">
@stop

@section('js')
<script>
    console.log('Script kenaikan kelas loaded');

    $(document).ready(function() {
        // Set nilai default tahun ajaran saat halaman dimuat
        $('#tahunAjaranKenaikan').val('{{ $tahunAjaranBerikutnya }}');

        // Handler untuk pemilihan kelas asal
        $('#kelasAsal').change(function() {
            const kelasId = $(this).val();
            if (kelasId) {
                $('#kelasAsalId').val(kelasId);
                
                // Sembunyikan semua baris siswa
                $('.siswa-row').hide();
                
                // Tampilkan hanya siswa dari kelas yang dipilih
                $('.siswa-row[data-kelas-id="' + kelasId + '"]').show();
                
                // Tampilkan div daftar siswa
                $('#daftarSiswaAsal').show();
                
                // Enable kelas tujuan dropdown
                $('#kelasTujuan').prop('disabled', false);
                
                console.log('Kelas asal dipilih:', kelasId);
            } else {
                $('#daftarSiswaAsal').hide();
                $('#kelasTujuan').prop('disabled', true);
                $('#kelasAsalId').val('');
            }
        });
        
        // Handler untuk pemilihan kelas tujuan
        $('#kelasTujuan').change(function() {
            const kelasTujuanId = $(this).val();
            if (kelasTujuanId) {
                $('#kelasTujuanId').val(kelasTujuanId);
                console.log('Kelas tujuan dipilih:', kelasTujuanId);
            } else {
                $('#kelasTujuanId').val('');
            }
        });
        
        // Handler untuk pemilihan tahun ajaran
        $('#tahunAjaran').change(function() {
            const tahunAjaran = $(this).val();
            $('#tahunAjaranKenaikan').val(tahunAjaran);
        });
        
        // Set nilai default tahun ajaran
        $('#tahunAjaranKenaikan').val($('#tahunAjaran').val());
        
        // Fungsi untuk memuat daftar siswa berdasarkan kelas
        function loadSiswaByKelas(kelasId) {
            console.log('Loading siswa for kelas ID:', kelasId);
            
            // Tampilkan loading indicator
            $('#tabelSiswaAsal tbody').html('<tr><td colspan="4" class="text-center"><i class="fas fa-spinner fa-spin"></i> Memuat data...</td></tr>');
            
            // Pastikan URL sudah benar (tambahkan /api di depan jika perlu)
            $.ajax({
                url: '/api/kelas/' + kelasId + '/siswa',
                type: 'GET',
                dataType: 'json',
                beforeSend: function() {
                    console.log('Sending request to:', '/api/kelas/' + kelasId + '/siswa');
                },
                success: function(data) {
                    console.log('Received data:', data);
                    let html = '';
                    
                    if (data && data.length > 0) {
                        $.each(data, function(index, siswa) {
                            html += `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${siswa.nis || '-'}</td>
                                    <td>${siswa.nama}</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary btn-kenaikan-individu" 
                                                data-id="${siswa.id}" 
                                                data-nama="${siswa.nama}">
                                            <i class="fas fa-arrow-right"></i> Naikkan
                                        </button>
                                    </td>
                                </tr>
                            `;
                        });
                        
                        // Tampilkan tombol "Naikkan Semua"
                        $('#btnNaikkanSemua').show();
                    } else {
                        html = '<tr><td colspan="4" class="text-center">Tidak ada siswa di kelas ini</td></tr>';
                        
                        // Sembunyikan tombol "Naikkan Semua"
                        $('#btnNaikkanSemua').hide();
                    }
                    
                    // Masukkan HTML ke dalam tabel
                    $('#tabelSiswaAsal tbody').html(html);
                    
                    // Aktifkan event handler untuk tombol kenaikan individu
                    $('.btn-kenaikan-individu').click(function() {
                        const siswaId = $(this).data('id');
                        const namaSiswa = $(this).data('nama');
                        
                        // Set nilai pada modal
                        $('#siswaId').val(siswaId);
                        $('#namaSiswa').text(namaSiswa);
                        
                        // Tampilkan modal
                        $('#modalKenaikanIndividu').modal('show');
                    });
                },
                error: function(xhr, status, error) {
                    console.error('Error status:', status);
                    console.error('Error message:', error);
                    console.error('Response text:', xhr.responseText);
                    
                    let errorMessage = 'Gagal memuat data siswa';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response && response.error) {
                            errorMessage += ': ' + response.error;
                        }
                    } catch (e) {
                        // Jika tidak bisa parse JSON, gunakan pesan error default
                    }
                    
                    $('#tabelSiswaAsal tbody').html(`<tr><td colspan="4" class="text-center text-danger">Error: ${errorMessage}</td></tr>`);
                    
                    // Sembunyikan tombol "Naikkan Semua"
                    $('#btnNaikkanSemua').hide();
                }
            });
        }

        // Validasi form kenaikan kelas terpilih
        $('#formKenaikanKelas').submit(function(e) {
            // Debug: tampilkan data yang akan dikirim
            console.log('Form data:');
            console.log('Kelas Asal ID:', $('#kelasAsalId').val());
            console.log('Kelas Tujuan ID:', $('#kelasTujuanId').val());
            console.log('Tahun Ajaran:', $('#tahunAjaranKenaikan').val());
            console.log('Siswa IDs:', $('.siswa-checkbox:checked').map(function() {
                return $(this).val();
            }).get());
            
            // Validasi form
            if ($('.siswa-checkbox:checked').length === 0) {
                e.preventDefault();
                alert('Silakan pilih minimal satu siswa untuk dinaikkan kelas');
                return false;
            }
            
            // Cek apakah kelas tujuan sudah dipilih
            if (!$('#kelasTujuanId').val()) {
                e.preventDefault();
                alert('Silakan pilih kelas tujuan terlebih dahulu');
                return false;
            }
            
            if (!$('#kelasAsalId').val()) {
                e.preventDefault();
                alert('Silakan pilih kelas asal terlebih dahulu');
                return false;
            }
            
            if (!$('#tahunAjaranKenaikan').val()) {
                e.preventDefault();
                alert('Tahun ajaran tidak terdeteksi');
                return false;
            }
            
            return true;
        });

        // Handler untuk checkbox "Pilih Semua"
        $('#checkAll').change(function() {
            // Hanya pilih siswa yang sedang ditampilkan (dari kelas yang dipilih)
            $('.siswa-row:visible .siswa-checkbox').prop('checked', $(this).prop('checked'));
        });

        // Tambahkan handler untuk tombol debug
        $('#btnDebugForm').click(function() {
            alert(
                'Debug Info:\n' +
                'Kelas Asal ID: ' + $('#kelasAsalId').val() + '\n' +
                'Kelas Tujuan ID: ' + $('#kelasTujuanId').val() + '\n' +
                'Tahun Ajaran: ' + $('#tahunAjaranKenaikan').val() + '\n' +
                'Siswa Terpilih: ' + $('.siswa-checkbox:checked').length
            );
        });

        // Aktifkan event handler untuk tombol kenaikan individu di baris tabel
        $(document).on('click', '.btn-kenaikan-individu', function() {
            const siswaId = $(this).data('id');
            const namaSiswa = $(this).data('nama');
            const kelasAsalId = $('#kelasAsalId').val();
            const kelasTujuanId = $('#kelasTujuanId').val();
            const tahunAjaran = $('#tahunAjaranKenaikan').val();
            
            console.log('Kenaikan individu:', {
                siswaId: siswaId,
                namaSiswa: namaSiswa,
                kelasAsalId: kelasAsalId,
                kelasTujuanId: kelasTujuanId,
                tahunAjaran: tahunAjaran
            });
            
            // Validasi data
            if (!kelasTujuanId) {
                alert('Silakan pilih kelas tujuan terlebih dahulu');
                return;
            }
            
            if (!kelasAsalId) {
                alert('Silakan pilih kelas asal terlebih dahulu');
                return;
            }
            
            if (!tahunAjaran) {
                alert('Tahun ajaran tidak terdeteksi');
                return;
            }
            
            // Konfirmasi sebelum menaikkan
            if (confirm(`Apakah Anda yakin ingin menaikkan siswa ${namaSiswa} ke kelas tujuan?`)) {
                // Buat form dan tambahkan ke DOM
                const formHtml = `
                    <form id="singlePromoteForm" action="{{ route('rombel.reguler.kenaikan.terpilih') }}" method="POST" style="display: none;">
                        @csrf
                        <input type="hidden" name="kelas_asal_id" value="${kelasAsalId}">
                        <input type="hidden" name="kelas_tujuan_id" value="${kelasTujuanId}">
                        <input type="hidden" name="tahun_ajaran" value="${tahunAjaran}">
                        <input type="hidden" name="siswa_ids[]" value="${siswaId}">
                    </form>
                `;
                
                $('body').append(formHtml);
                $('#singlePromoteForm').submit();
            }
        });
    });
</script>
@stop































