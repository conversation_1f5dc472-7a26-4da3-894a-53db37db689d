/**
 * Jam Mengajar Tambahan
 * Script untuk mengelola fungsionalitas jam mengajar tambahan
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded - Jam Tambahan Module');
    
    // Tambahkan style untuk animasi
    const style = document.createElement('style');
    style.textContent = `
        .fade-in {
            animation: fadeIn 0.5s;
        }
        @keyframes fadeIn {
            from { opacity: 0; background-color: #d4edda; }
            to { opacity: 1; background-color: transparent; }
        }
    `;
    document.head.appendChild(style);
    
    // Fungsi untuk menghitung total jam tambahan
    function hitungTotalJamTambahan() {
        const inputs = document.querySelectorAll('.jam-tambahan-durasi');
        let total = 0;
        
        inputs.forEach(input => {
            const nilai = parseFloat(input.value) || 0;
            total += nilai;
        });
        
        document.getElementById('totalJamTambahan').textContent = `Total: ${total.toFixed(1)} jam`;
        
        // Tambahkan input hidden untuk menyimpan total jam tambahan
        let inputTotal = document.querySelector('input[name="total_jam_tambahan"]');
        if (!inputTotal) {
            inputTotal = document.createElement('input');
            inputTotal.type = 'hidden';
            inputTotal.name = 'total_jam_tambahan';
            document.querySelector('form').appendChild(inputTotal);
        }
        inputTotal.value = total.toFixed(1);
        
        return total;
    }
    
    // Fungsi untuk menambah jam tambahan
    const tambahJamTambahanBtn = document.getElementById('tambahJamTambahan');
    if (tambahJamTambahanBtn) {
        tambahJamTambahanBtn.addEventListener('click', function() {
            console.log('Tombol tambah jam tambahan diklik');
            const tbody = document.querySelector('#tabelJamTambahan tbody');
            const index = tbody.querySelectorAll('tr').length;
            
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td>
                    <input type="text" name="jam_tambahan[${index}][kelas]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_tambahan[${index}][mapel]" class="form-control" required>
                </td>
                <td>
                    <div class="d-flex">
                        <input type="time" name="jam_tambahan[${index}][waktu_mulai]" class="form-control mr-1 waktu-mulai-tambahan" required>
                        <span class="align-self-center mx-1">-</span>
                        <input type="time" name="jam_tambahan[${index}][waktu_selesai]" class="form-control ml-1 waktu-selesai-tambahan" required>
                    </div>
                    <input type="hidden" name="jam_tambahan[${index}][waktu]" class="waktu-gabungan">
                </td>
                <td>
                    <input type="number" name="jam_tambahan[${index}][durasi]" class="form-control jam-tambahan-durasi" value="1.0" readonly>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger hapus-jam-tambahan">
                        <i class="fas fa-trash"></i> Hapus
                    </button>
                </td>
            `;
            
            tbody.appendChild(newRow);
            newRow.classList.add('fade-in');
            console.log('Baris baru ditambahkan ke tabel jam tambahan');
            
            // Tambahkan event listener untuk tombol hapus
            newRow.querySelector('.hapus-jam-tambahan').addEventListener('click', function() {
                if (confirm('Apakah Anda yakin ingin menghapus baris ini?')) {
                    console.log('Tombol hapus diklik');
                    this.closest('tr').remove();
                    hitungTotalJamTambahan();
                }
            });
            
            // Tambahkan event listener untuk waktu
            const waktuMulaiInput = newRow.querySelector('.waktu-mulai-tambahan');
            const waktuSelesaiInput = newRow.querySelector('.waktu-selesai-tambahan');
            const durasiInput = newRow.querySelector('.jam-tambahan-durasi');
            
            function updateWaktuDanDurasi() {
                const waktuMulai = waktuMulaiInput.value;
                const waktuSelesai = waktuSelesaiInput.value;
                
                if (waktuMulai && waktuSelesai) {
                    newRow.querySelector('.waktu-gabungan').value = waktuMulai + ' - ' + waktuSelesai;
                    
                    // Validasi waktu
                    const mulai = new Date('2000-01-01T' + waktuMulai + ':00');
                    const selesai = new Date('2000-01-01T' + waktuSelesai + ':00');
                    
                    if (selesai > mulai) {
                        // Set nilai durasi tetap 1 jam untuk setiap baris
                        durasiInput.value = "1.0";
                        
                        hitungTotalJamTambahan();
                    } else {
                        alert('Waktu selesai harus lebih besar dari waktu mulai');
                        waktuSelesaiInput.value = '';
                    }
                }
            }
            
            waktuMulaiInput.addEventListener('change', updateWaktuDanDurasi);
            waktuSelesaiInput.addEventListener('change', updateWaktuDanDurasi);
        });
    }
    
    // Event handler untuk hapus jam tambahan (menggunakan delegasi event)
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('hapus-jam-tambahan')) {
            if (confirm('Apakah Anda yakin ingin menghapus baris ini?')) {
                console.log('Tombol hapus diklik (delegasi)');
                e.target.closest('tr').remove();
                hitungTotalJamTambahan();
            }
        }
    });
});




