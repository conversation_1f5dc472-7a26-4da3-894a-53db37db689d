if ($request->hasFile('gambar')) {
    // Hapus gambar lama jika ada
    if ($ekstrakurikuler->gambar && file_exists(public_path('storage/ekstrakurikuler/' . $ekstrakurikuler->gambar))) {
        unlink(public_path('storage/ekstrakurikuler/' . $ekstrakurikuler->gambar));
    }
    
    $image = $request->file('gambar');
    $filename = time() . '.' . $image->getClientOriginalExtension();
    
    // Pastikan folder ada
    if (!file_exists(public_path('storage/ekstrakurikuler'))) {
        mkdir(public_path('storage/ekstrakurikuler'), 0755, true);
    }
    
    // Simpan gambar
    $image->move(public_path('storage/ekstrakurikuler'), $filename);
    $validated['gambar'] = $filename;
    
    // Log untuk debugging
    \Log::info('Gambar ekstrakurikuler disimpan: ' . public_path('storage/ekstrakurikuler') . '/' . $filename);
}
