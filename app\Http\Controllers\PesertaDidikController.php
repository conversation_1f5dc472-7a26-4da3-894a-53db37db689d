<?php

namespace App\Http\Controllers;

use App\Models\PesertaDidik;
use Illuminate\Http\Request;
use App\Models\Kelas;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\PesertaDidikImport;
use App\Exports\PesertaDidikExport;
use App\Exports\PesertaDidikTemplateExport;
use Illuminate\Support\Facades\Log;
use App\Traits\FiltersByUserUnit;
use Illuminate\Support\Facades\DB;

class PesertaDidikController extends Controller
{
    use FiltersByUserUnit;
    
    public function aktif()
    {
        $query = PesertaDidik::with('kelas')
                    ->where('is_active', true)
                    ->where('status', 'aktif'); // Tambahkan filter status aktif
        
        // Terapkan filter unit langsung pada tabel peserta_didik
        $query = $this->applyUnitFilter($query);
        
        $siswa = $query->get();
        
        return view('peserta-didik.aktif', compact('siswa'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'nis' => 'required',
            'nisn' => 'required',
            'nik' => 'required|unique:peserta_didik,nik',
            'nama' => 'required',
            'jenis_kelamin' => 'required|in:L,P',
            'kelas_id' => 'required|exists:kelas,id',
            'tempat_lahir' => 'required',
            'tanggal_lahir' => 'required|date',
            'alamat' => 'required',
            // ... validasi lainnya
            'unit_id' => 'required|exists:units,id',
        ]);

        // Gunakan transaksi database untuk memastikan semua operasi berhasil atau gagal bersama
        DB::beginTransaction();
        
        try {
            // Simpan data peserta didik
            $pesertaDidik = PesertaDidik::create($validated);
            
            // Ambil tahun ajaran aktif
            $tahunAjaran = \App\Models\TahunAjaran::where('aktif', true)->first();
            $tahunAjaranNama = $tahunAjaran ? $tahunAjaran->nama : date('Y').'/'.((int)date('Y')+1);
            
            // Catat riwayat kelas untuk siswa baru
            $riwayatKelas = \App\Models\RiwayatKelas::create([
                'siswa_id' => $pesertaDidik->id,
                'kelas_lama_id' => null, // Siswa baru tidak memiliki kelas lama
                'kelas_baru_id' => $request->kelas_id,
                'tahun_ajaran' => $tahunAjaranNama,
                'jenis_perpindahan' => 'pindah_kelas',
                'tanggal_pindah' => now(),
                'alasan' => 'Pendaftaran siswa baru',
                'created_by' => auth()->id()
            ]);
            
            // Tambahkan logging untuk debugging
            \Log::info('Siswa baru ditambahkan dengan ID: ' . $pesertaDidik->id);
            \Log::info('Riwayat kelas dibuat dengan ID: ' . $riwayatKelas->id);
            
            // Commit transaksi jika semua operasi berhasil
            DB::commit();
            
            return redirect()->route('peserta-didik.aktif')
                ->with('success', 'Data peserta didik berhasil ditambahkan.');
        } catch (\Exception $e) {
            // Rollback transaksi jika terjadi error
            DB::rollback();
            
            // Log error untuk debugging
            \Log::error('Error saat menambahkan siswa baru: ' . $e->getMessage());
            
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'Terjadi kesalahan saat menyimpan data. ' . $e->getMessage()]);
        }
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'nis' => 'required',
            'nisn' => 'required',
            'nik' => 'required|unique:peserta_didik,nik,'.$id,
            'nama' => 'required',
            'jenis_kelamin' => 'required|in:L,P',
            'kelas_id' => 'required|exists:kelas,id',
            'unit_id' => 'required|exists:units,id',
            'tempat_lahir' => 'required',
            'tanggal_lahir' => 'required|date',
            'alamat' => 'required',
        ]);

        $pesertaDidik = PesertaDidik::findOrFail($id);
        $pesertaDidik->update($request->all());

        return redirect()->back()->with('success', 'Data peserta didik berhasil diperbarui');
    }

    public function destroy($id)
    {
        $pesertaDidik = PesertaDidik::findOrFail($id);
        $pesertaDidik->delete();

        return redirect()->back()->with('success', 'Data peserta didik berhasil dihapus');
    }

    /**
     * Import data peserta didik dari file Excel
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls',
        ]);

        try {
            // Aktifkan logging untuk debugging
            Log::info("Memulai proses impor peserta didik");
            
            // Teruskan ID user saat ini ke importer
            $import = new PesertaDidikImport(auth()->id());
            
            // Jalankan impor
            Excel::import($import, $request->file('file'));
            
            Log::info("Impor peserta didik selesai");
            return redirect()->back()->with('success', 'Data peserta didik berhasil diimpor.');
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            Log::error("Validasi gagal saat impor: " . json_encode($e->failures()));
            return redirect()->back()->with('error', 'Validasi gagal: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error("Error saat impor: " . $e->getMessage());
            Log::error("Stack trace: " . $e->getTraceAsString());
            return redirect()->back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Export data peserta didik ke file Excel
     */
    public function export()
    {
        return Excel::download(new PesertaDidikExport, 'data-peserta-didik.xlsx');
    }

    /**
     * Download template Excel untuk import data
     */
    public function exportTemplate()
    {
        return Excel::download(new PesertaDidikTemplateExport, 'template-import-peserta-didik.xlsx');
    }

    public function alumni()
    {
        $query = PesertaDidik::where('is_active', false)
                    ->with(['riwayatKelas' => function($query) {
                        // Ambil riwayat kelulusan terakhir
                        $query->where('jenis_perpindahan', 'kelulusan')
                              ->orderBy('tanggal_pindah', 'desc');
                    }]);
        
        // Terapkan filter unit menggunakan trait
        $query = $this->applyUnitFilter($query, 'riwayatKelas.kelasLama');
        
        $alumni = $query->get()
                    ->map(function($siswa) {
                        // Tambahkan tahun lulus dari riwayat kelas
                        $siswa->tahun_lulus = $siswa->riwayatKelas->first()->tahun_ajaran ?? null;
                        $siswa->kelas_terakhir = $siswa->riwayatKelas->first()->kelasLama->nama ?? null;
                        return $siswa;
                    });
        
        // Ambil daftar tahun kelulusan untuk filter
        $tahunKelulusan = \App\Models\RiwayatKelas::where('jenis_perpindahan', 'kelulusan')
                    ->distinct()
                    ->pluck('tahun_ajaran')
                    ->filter()
                    ->sort()
                    ->values();
        
        return view('peserta-didik.alumni', compact('alumni', 'tahunKelulusan'));
    }

    public function mutasiKeluar()
    {
        $query = PesertaDidik::where('status', 'mutasi_keluar')
                    ->with(['riwayatKelas' => function($query) {
                        // Get the latest mutation history
                        $query->where('jenis_perpindahan', 'mutasi_keluar')
                              ->orderBy('tanggal_pindah', 'desc')
                              ->with('kelasLama'); // Eager load kelasLama relation
                    }]);
        
        // Apply unit filter using trait
        $query = $this->applyUnitFilter($query, 'riwayatKelas.kelasLama');
        
        $mutasiKeluar = $query->get()
                    ->map(function($siswa) {
                        // Add mutation date and last class from history
                        $riwayat = $siswa->riwayatKelas->first();
                        
                        // Check if riwayat exists before accessing properties
                        if ($riwayat) {
                            $siswa->tanggal_mutasi = $riwayat->tanggal_pindah;
                            $siswa->kelas_terakhir = $riwayat->kelasLama ? $riwayat->kelasLama->nama : null;
                            $siswa->sekolah_tujuan = $riwayat->sekolah_tujuan;
                        } else {
                            $siswa->tanggal_mutasi = null;
                            $siswa->kelas_terakhir = null;
                            $siswa->sekolah_tujuan = null;
                        }
                        
                        return $siswa;
                    });
        
        return view('peserta-didik.mutasi-keluar', compact('mutasiKeluar'));
    }

    /**
     * Show the form for editing the specified mutasi record.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function editMutasi($id)
    {
        $siswa = PesertaDidik::with(['riwayatKelas' => function($query) {
            $query->where('jenis_perpindahan', 'mutasi_keluar')
                  ->orderBy('tanggal_pindah', 'desc');
        }])->findOrFail($id);
        
        // Get additional data needed for the form
        $kelas = Kelas::all();
        
        return view('peserta-didik.edit-mutasi', compact('siswa', 'kelas'));
    }

    /**
     * Remove the specified mutasi record.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroyMutasi($id)
    {
        $siswa = PesertaDidik::findOrFail($id);
        
        // Delete related mutation records
        $siswa->riwayatKelas()->where('jenis_perpindahan', 'mutasi_keluar')->delete();
        
        // Update student status back to active
        $siswa->status = 'aktif';
        $siswa->save();
        
        return redirect()->route('peserta-didik.mutasi-keluar')
                         ->with('success', 'Data mutasi berhasil dihapus.');
    }

    /**
     * Proses mutasi keluar siswa
     */
    public function prosesMutasiKeluar(Request $request, $id)
    {
        $request->validate([
            'tanggal_mutasi' => 'required|date',
            'sekolah_tujuan' => 'required|string|max:255',
            'alasan' => 'nullable|string'
        ]);

        $siswa = PesertaDidik::findOrFail($id);
        $siswa->status = 'mutasi_keluar';
        $siswa->save();

        // Simpan riwayat kelas
        RiwayatKelas::create([
            'siswa_id' => $siswa->id,
            'kelas_lama_id' => $request->kelas_id, // Kelas terakhir siswa
            'kelas_baru_id' => null,
            'tahun_ajaran' => TahunAjaran::active()->first()->nama ?? date('Y').'/'.date('Y')+1,
            'jenis_perpindahan' => 'mutasi_keluar',
            'tanggal_pindah' => $request->tanggal_mutasi,
            'alasan' => $request->alasan,
            'sekolah_tujuan' => $request->sekolah_tujuan,
            'created_by' => auth()->id()
        ]);

        return redirect()->route('peserta-didik.mutasi-keluar')
                         ->with('success', 'Siswa berhasil dimutasi keluar');
    }

    /**
     * Update the specified mutasi record.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateMutasi(Request $request, $id)
    {
        $request->validate([
            'tanggal_mutasi' => 'required|date',
            'sekolah_tujuan' => 'required|string|max:255',
            'alasan' => 'nullable|string'
        ]);

        $siswa = PesertaDidik::findOrFail($id);
        
        // Update status siswa jika perlu
        if ($siswa->status !== 'mutasi_keluar') {
            $siswa->status = 'mutasi_keluar';
            $siswa->save();
        }
        
        // Update riwayat mutasi
        $riwayatMutasi = $siswa->riwayatKelas()
                              ->where('jenis_perpindahan', 'mutasi_keluar')
                              ->orderBy('tanggal_pindah', 'desc')
                              ->first();
        
        if ($riwayatMutasi) {
            $riwayatMutasi->update([
                'tanggal_pindah' => $request->tanggal_mutasi,
                'alasan' => $request->alasan,
                'sekolah_tujuan' => $request->sekolah_tujuan
            ]);
        } else {
            // Jika tidak ada riwayat, buat baru
            RiwayatKelas::create([
                'siswa_id' => $siswa->id,
                'kelas_lama_id' => $siswa->kelas_id ?? $request->kelas_id,
                'kelas_baru_id' => null,
                'tahun_ajaran' => TahunAjaran::active()->first()->nama ?? date('Y').'/'.date('Y')+1,
                'jenis_perpindahan' => 'mutasi_keluar',
                'tanggal_pindah' => $request->tanggal_mutasi,
                'alasan' => $request->alasan,
                'sekolah_tujuan' => $request->sekolah_tujuan,
                'created_by' => auth()->id()
            ]);
        }
        
        return redirect()->route('peserta-didik.mutasi-keluar')
                         ->with('success', 'Data mutasi berhasil diperbarui');
    }

    /**
     * Menampilkan form edit alumni
     */
    public function editAlumni($id)
    {
        $alumni = PesertaDidik::findOrFail($id);
        
        // Pastikan siswa berstatus alumni
        if ($alumni->status !== 'alumni' || $alumni->is_active) {
            return redirect()->route('peserta-didik.alumni')->with('error', 'Data yang diminta bukan alumni');
        }
        
        return view('peserta-didik.alumni-edit', compact('alumni'));
    }

    /**
     * Update data alumni
     */
    public function updateAlumni(Request $request, $id)
    {
        $alumni = PesertaDidik::findOrFail($id);
        
        // Pastikan siswa berstatus alumni
        if ($alumni->status !== 'alumni' || $alumni->is_active) {
            return redirect()->route('peserta-didik.alumni')->with('error', 'Data yang diminta bukan alumni');
        }
        
        $request->validate([
            'nama' => 'required|string|max:255',
            'nis' => 'required|string|max:20',
            'nisn' => 'required|string|max:20',
            'jenis_kelamin' => 'required|in:L,P',
            // Tambahkan validasi lain sesuai kebutuhan
        ]);
        
        $alumni->update($request->all());
        
        return redirect()->route('peserta-didik.alumni')->with('success', 'Data alumni berhasil diperbarui');
    }

    /**
     * Hapus data alumni
     */
    public function destroyAlumni($id)
    {
        $alumni = PesertaDidik::findOrFail($id);
        
        // Pastikan siswa berstatus alumni
        if ($alumni->status !== 'alumni' || $alumni->is_active) {
            return redirect()->route('peserta-didik.alumni')->with('error', 'Data yang diminta bukan alumni');
        }
        
        // Hapus riwayat kelas terkait
        $alumni->riwayatKelas()->delete();
        
        // Hapus alumni
        $alumni->delete();
        
        return redirect()->route('peserta-didik.alumni')->with('success', 'Data alumni berhasil dihapus');
    }
}
