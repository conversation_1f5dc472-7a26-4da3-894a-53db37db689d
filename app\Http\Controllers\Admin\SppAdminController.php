<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Spp;
use App\Models\SppUpload;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\SppImport;
use App\Exports\SppExport;

class SppAdminController extends Controller
{
    /**
     * Menampilkan halaman admin SPP
     */
    public function index()
    {
        // Ambil data SPP terbaru
        $spps = Spp::latest()->paginate(10);
        
        // Ambil data riwayat upload
        $uploads = SppUpload::with('uploader')->latest()->get();
        
        return view('spp.admin.index', compact('spps', 'uploads'));
    }

    /**
     * Menampilkan form upload data SPP
     */
    public function showUploadForm()
    {
        return view('spp.admin.upload');
    }

    /**
     * Memproses upload file SPP
     */
    public function processUpload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv|max:10240',
            'keterangan' => 'nullable|string|max:255',
        ]);

        try {
            $file = $request->file('file');
            $originalName = $file->getClientOriginalName();
            $fileName = time() . '_' . $originalName;
            
            // Simpan file ke storage
            $path = $file->storeAs('spp_uploads', $fileName, 'public');
            
            // Simpan informasi upload ke database
            SppUpload::create([
                'file_name' => $fileName,
                'original_name' => $originalName,
                'keterangan' => $request->keterangan,
                'uploaded_by' => auth()->id(),
            ]);
            
            // Import data dari Excel dengan upsert
            Excel::import(new SppImport, $file);
            
            return redirect()->route('admin.spp.index')->with('success', 'Data SPP berhasil diupload dan diperbarui.');
        } catch (\Exception $e) {
            return back()->with('error', 'Gagal mengupload data: ' . $e->getMessage());
        }
    }

    /**
     * Menampilkan detail data SPP
     */
    public function show(Spp $spp)
    {
        return view('spp.admin.detail', compact('spp'));
    }

    /**
     * Menampilkan form edit data SPP
     */
    public function edit(Spp $spp)
    {
        return view('spp.admin.edit', compact('spp'));
    }

    /**
     * Memperbarui data SPP
     */
    public function update(Request $request, Spp $spp)
    {
        $request->validate([
            'nama' => 'required|string|max:255',
            'tanggal_lahir' => 'required|date',
            'spp_bulan_ini' => 'required|numeric|min:0',
            'tunggakan' => 'required|numeric|min:0',
            'buku' => 'required|numeric|min:0',
            'uang_program' => 'required|numeric|min:0',
            'les' => 'required|numeric|min:0',
        ]);

        $spp->update($request->all());

        return redirect()->route('admin.spp.index')->with('success', 'Data SPP berhasil diperbarui.');
    }

    /**
     * Menghapus data SPP
     */
    public function destroy(Spp $spp)
    {
        $spp->delete();

        return redirect()->route('admin.spp.index')->with('success', 'Data SPP berhasil dihapus.');
    }

    /**
     * Export data SPP ke Excel
     */
    public function export()
    {
        return Excel::download(new SppExport, 'data-spp-' . date('Y-m-d') . '.xlsx');
    }
}
