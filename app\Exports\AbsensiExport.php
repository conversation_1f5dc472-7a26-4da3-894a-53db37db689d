<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class AbsensiExport implements FromCollection, WithHeadings, WithStyles, WithTitle, WithEvents
{
    protected $rekapData;
    protected $bulan;
    protected $tahun;
    protected $kelasNama;
    protected $totalDays;
    protected $guruNama;

    public function __construct($rekapData, $bulan, $tahun, $kelasNama, $totalDays)
    {
        $this->rekapData = $rekapData;
        $this->bulan = $bulan;
        $this->tahun = $tahun;
        $this->kelasNama = $kelasNama;
        $this->totalDays = $totalDays;
        $this->guruNama = Auth::user()->name ?? 'Admin';
    }

    public function collection()
    {
        $collection = new Collection();
        
        $no = 1;
        $totalHadir = 0;
        $totalSakit = 0;
        $totalIzin = 0;
        $totalAlpa = 0;
        
        foreach ($this->rekapData as $siswa) {
            $row = [
                'no' => $no++,
                'nisn' => $siswa['nisn'],
                'nama' => $siswa['nama']
            ];
            
            // Tambahkan data absensi per tanggal
            for ($i = 1; $i <= $this->totalDays; $i++) {
                $status = $siswa['absensi'][$i] ?? '';
                // Pastikan status tidak kosong dengan menambahkan debugging
                \Illuminate\Support\Facades\Log::debug("Tanggal $i, Status: " . json_encode($status));
                $row['tanggal_'.$i] = $this->formatStatus($status);
            }
            
            // Tambahkan ringkasan
            $row['hadir'] = $siswa['hadir'];
            $row['sakit'] = $siswa['sakit'];
            $row['izin'] = $siswa['izin'];
            $row['alpa'] = $siswa['alpa'];
            
            // Akumulasi total
            $totalHadir += $siswa['hadir'];
            $totalSakit += $siswa['sakit'];
            $totalIzin += $siswa['izin'];
            $totalAlpa += $siswa['alpa'];
            
            $collection->push($row);
        }
        
        // Tambahkan baris total
        $totalRow = [
            'no' => '',
            'nisn' => '',
            'nama' => 'TOTAL'
        ];
        
        // Tambahkan kolom kosong untuk tanggal
        for ($i = 1; $i <= $this->totalDays; $i++) {
            $totalRow['tanggal_'.$i] = '';
        }
        
        // Tambahkan total untuk ringkasan
        $totalRow['hadir'] = $totalHadir;
        $totalRow['sakit'] = $totalSakit;
        $totalRow['izin'] = $totalIzin;
        $totalRow['alpa'] = $totalAlpa;
        
        $collection->push($totalRow);
        
        return $collection;
    }

    public function headings(): array
    {
        // Tambahkan 3 baris kosong untuk judul
        return [
            ['Absensi Siswa Kelas ' . $this->kelasNama],
            ['Guru: ' . $this->guruNama],
            ['Bulan: ' . $this->getNamaBulan($this->bulan) . ' ' . $this->tahun],
            [
                'No',
                'NISN',
                'Nama Siswa',
                // Tambahkan heading untuk setiap tanggal
                ...array_map(function($i) { return $i; }, range(1, $this->totalDays)),
                // Tambahkan heading untuk ringkasan
                'H', 'S', 'I', 'A'
            ]
        ];
    }

    public function styles(Worksheet $sheet)
    {
        try {
            // Hitung jumlah kolom (menggunakan pendekatan yang lebih sederhana)
            $totalColumns = 3 + $this->totalDays + 4; // No, NISN, Nama + totalDays + 4 kolom ringkasan
            $lastColumn = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($totalColumns);
            
            // Merge cells untuk judul
            $sheet->mergeCells('A1:' . $lastColumn . '1');
            $sheet->mergeCells('A2:' . $lastColumn . '2');
            $sheet->mergeCells('A3:' . $lastColumn . '3');
            
            // Style untuk judul
            $sheet->getStyle('A1')->getFont()->setBold(true);
            $sheet->getStyle('A1')->getFont()->setSize(14);
            $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            
            $sheet->getStyle('A2:A3')->getFont()->setBold(true);
            $sheet->getStyle('A2:A3')->getFont()->setSize(12);
            $sheet->getStyle('A2:A3')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            
            // Style untuk header tabel
            $sheet->getStyle('A4:' . $lastColumn . '4')->getFont()->setBold(true);
            
            // Set lebar kolom
            $sheet->getColumnDimension('A')->setWidth(5);  // No
            $sheet->getColumnDimension('B')->setWidth(15); // NISN
            $sheet->getColumnDimension('C')->setWidth(30); // Nama
            
            // Hitung jumlah baris data (termasuk baris total)
            $lastRow = count($this->rekapData) + 5; // +4 untuk header, +1 untuk baris total
            
            // Tambahkan border untuk seluruh tabel
            $tableRange = 'A4:' . $lastColumn . $lastRow;
            $sheet->getStyle($tableRange)->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
            
            // Tambahkan warna latar untuk header
            $sheet->getStyle('A4:' . $lastColumn . '4')->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('E2EFDA');
            
            // Alignment untuk data
            $sheet->getStyle('A5:' . $lastColumn . $lastRow)->getAlignment()
                ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            
            // Alignment khusus untuk kolom nama (rata kiri)
            $sheet->getStyle('C5:C' . $lastRow)->getAlignment()
                ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
            
            // Style untuk baris total
            $totalRowIndex = $lastRow;
            $sheet->getStyle('A' . $totalRowIndex . ':' . $lastColumn . $totalRowIndex)->getFont()->setBold(true);
            $sheet->getStyle('A' . $totalRowIndex . ':' . $lastColumn . $totalRowIndex)->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()->setRGB('F2F2F2');
            
            // Tambahkan keterangan persentase di bawah tabel
            $keteranganRow = $lastRow + 2;
            
            // Hitung total kehadiran dari baris total
            $totalHadir = $this->getTotalFromSheet($sheet, $lastRow, $totalColumns - 3);
            $totalSakit = $this->getTotalFromSheet($sheet, $lastRow, $totalColumns - 2);
            $totalIzin = $this->getTotalFromSheet($sheet, $lastRow, $totalColumns - 1);
            $totalAlpa = $this->getTotalFromSheet($sheet, $lastRow, $totalColumns);
            
            $totalKeseluruhan = $totalHadir + $totalSakit + $totalIzin + $totalAlpa;
            
            // Tambahkan keterangan persentase
            $sheet->setCellValue('A' . $keteranganRow, 'Keterangan:');
            $sheet->getStyle('A' . $keteranganRow)->getFont()->setBold(true);
            
            $sheet->setCellValue('A' . ($keteranganRow + 1), 'H = Hadir');
            $sheet->setCellValue('A' . ($keteranganRow + 2), 'S = Sakit');
            $sheet->setCellValue('A' . ($keteranganRow + 3), 'I = Izin');
            $sheet->setCellValue('A' . ($keteranganRow + 4), 'A = Alpa (Tanpa Keterangan)');
            
            // Tambahkan persentase
            $persenHadir = $totalKeseluruhan > 0 ? round(($totalHadir / $totalKeseluruhan) * 100, 2) : 0;
            $persenSakit = $totalKeseluruhan > 0 ? round(($totalSakit / $totalKeseluruhan) * 100, 2) : 0;
            $persenIzin = $totalKeseluruhan > 0 ? round(($totalIzin / $totalKeseluruhan) * 100, 2) : 0;
            $persenAlpa = $totalKeseluruhan > 0 ? round(($totalAlpa / $totalKeseluruhan) * 100, 2) : 0;
            
            $sheet->setCellValue('C' . ($keteranganRow + 1), "Persentase: {$persenHadir}% ({$totalHadir})");
            $sheet->setCellValue('C' . ($keteranganRow + 2), "Persentase: {$persenSakit}% ({$totalSakit})");
            $sheet->setCellValue('C' . ($keteranganRow + 3), "Persentase: {$persenIzin}% ({$totalIzin})");
            $sheet->setCellValue('C' . ($keteranganRow + 4), "Persentase: {$persenAlpa}% ({$totalAlpa})");
            
            return [];
        } catch (\Exception $e) {
            // Log error untuk debugging
            \Illuminate\Support\Facades\Log::error('Error in AbsensiExport::styles: ' . $e->getMessage());
            \Illuminate\Support\Facades\Log::error('Stack trace: ' . $e->getTraceAsString());
            
            return [];
        }
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                // Auto-fit kolom setelah data diisi
                $event->sheet->getDelegate()->calculateColumnWidths();
            },
        ];
    }

    public function title(): string
    {
        return "Rekap Absensi {$this->kelasNama}";
    }
    
    private function formatStatus($status)
    {
        // Tambahkan debugging
        \Illuminate\Support\Facades\Log::debug("Format status input: " . json_encode($status));
        
        switch ($status) {
            case 'H': 
            case 'hadir': return 'H';
            case 'S': 
            case 'sakit': return 'S';
            case 'I': 
            case 'izin': return 'I';
            case 'A': 
            case 'alpa': return 'A';
            default: return '';
        }
    }
    
    private function getNamaBulan($bulan)
    {
        $namaBulan = [
            1 => 'Januari',
            2 => 'Februari',
            3 => 'Maret',
            4 => 'April',
            5 => 'Mei',
            6 => 'Juni',
            7 => 'Juli',
            8 => 'Agustus',
            9 => 'September',
            10 => 'Oktober',
            11 => 'November',
            12 => 'Desember'
        ];
        
        return $namaBulan[$bulan] ?? 'Unknown';
    }

    /**
     * Mendapatkan nilai total dari sheet pada posisi tertentu
     */
    private function getTotalFromSheet(Worksheet $sheet, $row, $columnIndex)
    {
        $columnLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($columnIndex);
        $cellValue = $sheet->getCell($columnLetter . $row)->getValue();
        return is_numeric($cellValue) ? (int)$cellValue : 0;
    }
}



