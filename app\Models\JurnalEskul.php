<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JurnalEskul extends Model
{
    use HasFactory;

    protected $table = 'jurnal_eskul';

    protected $fillable = [
        'jurnal_kegiatan_id',
        'nama_eskul',
        'kelas',
        'jumlah_siswa',
        'kegiatan',
        'jumlah_jam',
        'keterangan'
    ];

    // Relasi ke jurnal kegiatan
    public function jurnalKegiatan()
    {
        return $this->belongsTo(JurnalKegiatan::class);
    }
}