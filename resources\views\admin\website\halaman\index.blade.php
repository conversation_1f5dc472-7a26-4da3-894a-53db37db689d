@extends('adminlte::page')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content_header')
    <h1><PERSON><PERSON><PERSON></h1>
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <a href="{{ route('admin.website.halaman.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> <PERSON><PERSON>
            </a>
        </div>
        <div class="card-body">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Judul</th>
                        <th>Unit</th>
                        <th>Tipe</th>
                        <th>Status</th>
                        <th width="150px">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($halaman as $key => $page)
                    <tr>
                        <td>{{ $key + 1 }}</td>
                        <td>{{ $page->judul }}</td>
                        <td>{{ $page->unit->nama_unit }}</td>
                        <td>{{ ucfirst($page->tipe) }}</td>
                        <td>
                            @if($page->is_active)
                                <span class="badge badge-success">Aktif</span>
                            @else
                                <span class="badge badge-danger">Tidak Aktif</span>
                            @endif
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ route('admin.website.halaman.edit', $page->id) }}" 
                                   class="btn btn-sm btn-info" 
                                   title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('admin.website.halaman.destroy', $page->id) }}" 
                                      method="POST" 
                                      class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" 
                                            class="btn btn-sm btn-danger" 
                                            onclick="return confirm('Yakin ingin menghapus?')"
                                            title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
@stop
@section('css')
<style>
    .btn-group form {
        margin: 0;
    }
    .btn-group .btn {
        margin: 0 2px;
    }
</style>
@stop
