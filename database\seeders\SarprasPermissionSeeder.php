<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class SarprasPermissionSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles dan permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Daftar permission untuk sarana dan prasarana
        $permissions = [
            'view-sarpras',                // Untuk melihat data sarpras
            'create-sarpras',              // Untuk menambah data sarpras
            'edit-sarpras',                // Untuk mengedit data sarpras
            'delete-sarpras',              // Untuk menghapus data sarpras
            'manage-sarpras',              // Permission umum untuk mengelola sarpras
            'view-laporan-kerusakan',      // Untuk melihat laporan kerusakan
            'create-laporan-kerusakan',    // Untuk membuat laporan kerusakan
            'edit-laporan-kerusakan',      // Untuk mengedit laporan kerusakan
            'delete-laporan-kerusakan',    // Untuk menghapus laporan kerusakan
            'view-laporan-kerusakan-detail', // Untuk melihat detail laporan kerusakan
            'manage-laporan-kerusakan',    // Untuk mengelola laporan kerusakan
            'view-laporan-kerusakan-approval', // Untuk melihat laporan kerusakan yang perlu disetujui
            'approve-laporan-kerusakan',   // Untuk menyetujui laporan kerusakan
        ];

        // Buat permission jika belum ada
        foreach ($permissions as $permissionName) {
            Permission::firstOrCreate(['name' => $permissionName]);
        }

        // Assign permission ke role yang sesuai
        $rolePermissions = [
            'Administrator' => [
                'view-sarpras',
                'create-sarpras',
                'edit-sarpras',
                'delete-sarpras',
                'manage-sarpras',
                'view-laporan-kerusakan',
                'create-laporan-kerusakan',
                'edit-laporan-kerusakan',
                'delete-laporan-kerusakan',
                'view-laporan-kerusakan-detail',
                'manage-laporan-kerusakan',
                'view-laporan-kerusakan-approval',
                'approve-laporan-kerusakan',
            ],
            'Kepala Sekolah' => [
                'view-sarpras',
                'view-laporan-kerusakan',
                'approve-laporan-kerusakan',
            ],
            'Waka Sarpras' => [
                'view-sarpras',
                'create-sarpras',
                'edit-sarpras',
                'delete-sarpras',
                'manage-sarpras',
                'view-laporan-kerusakan',
                'edit-laporan-kerusakan',
                'manage-laporan-kerusakan',
                'view-laporan-kerusakan-approval',
                'approve-laporan-kerusakan',
            ],
            'Guru' => [
                'view-sarpras',
                'view-laporan-kerusakan',
                'create-laporan-kerusakan',
            ],
            'Tata Usaha' => [
                'view-sarpras',
                'view-laporan-kerusakan',
                'create-laporan-kerusakan',
            ],
        ];

        // Assign permission ke role tanpa menghapus permission yang sudah ada
        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Role::firstOrCreate(['name' => $roleName]);
            foreach ($permissions as $permission) {
                if (!$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                }
            }
        }
    }
}

