<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Achievement;
use Illuminate\Support\Str;

class GenerateAchievementSlugs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'achievements:generate-slugs {--force : Force regenerate all slugs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate slugs for achievements that don\'t have them';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $force = $this->option('force');
        
        if ($force) {
            $achievements = Achievement::all();
            $this->info('Regenerating ALL achievement slugs...');
        } else {
            $achievements = Achievement::whereNull('slug')->orWhere('slug', '')->get();
            $this->info('Generating slugs for achievements without slugs...');
        }

        if ($achievements->isEmpty()) {
            $this->info('No achievements need slug generation.');
            return;
        }

        $bar = $this->output->createProgressBar($achievements->count());
        $bar->start();

        foreach ($achievements as $achievement) {
            $baseSlug = Str::slug($achievement->title);
            $slug = $baseSlug;
            $counter = 1;

            // Pastikan slug unik
            while (Achievement::where('slug', $slug)->where('id', '!=', $achievement->id)->exists()) {
                $slug = $baseSlug . '-' . $counter;
                $counter++;
            }

            $achievement->update(['slug' => $slug]);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("Successfully generated slugs for {$achievements->count()} achievements!");
        
        // Tampilkan hasil
        $this->newLine();
        $this->table(
            ['ID', 'Title', 'Slug'],
            $achievements->map(function ($achievement) {
                return [
                    $achievement->id,
                    Str::limit($achievement->title, 40),
                    $achievement->slug
                ];
            })->toArray()
        );
    }
}
