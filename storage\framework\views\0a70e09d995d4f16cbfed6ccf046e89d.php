<?php $__env->startSection('title', $article->title); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <article>
                <div class="title-container">
                    <h1 class="article-title"><?php echo e($article->title); ?></h1>
                </div>
                
                <div class="mb-3 text-muted text-center">
                    <small>
                    Dipublish oleh <?php echo e($article->author->name); ?> pada <?php echo e($article->created_at->format('d M Y')); ?>

                    </small>
                </div>

                <?php if($article->image): ?>
                    <img src="<?php echo e(Storage::url($article->image)); ?>" 
                         class="img-fluid rounded mb-4" 
                         alt="<?php echo e($article->title); ?>">
                <?php endif; ?>

                <div class="article-content">
                    <?php echo $article->content; ?>

                </div>

                <!-- Tombol Bagikan -->
                <div class="mt-4 d-flex justify-content-start align-items-center flex-wrap" style="gap: 8px;">
                    <span class="me-2 fw-bold">Bagikan:</span>
                    <!-- WhatsApp -->
                    <a href="https://wa.me/?text=<?php echo e(urlencode($article->title.' '.request()->fullUrl())); ?>" target="_blank" class="btn btn-success btn-sm" title="Bagikan ke WhatsApp" style="min-width: 36px;">
                        <i class="fab fa-whatsapp"></i>
                    </a>
                    <!-- Facebook -->
                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode(request()->fullUrl())); ?>&amp;picture=<?php echo e(urlencode(Storage::url($article->image))); ?>" target="_blank" class="btn btn-primary btn-sm" title="Bagikan ke Facebook" style="min-width: 36px;">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <!-- Twitter -->
                    <a href="https://twitter.com/intent/tweet?url=<?php echo e(urlencode(request()->fullUrl())); ?>&amp;text=<?php echo e(urlencode($article->title)); ?>&amp;via=simaspelopor&amp;hashtags=sekolah,artikel&amp;image=<?php echo e(urlencode(Storage::url($article->image))); ?>" target="_blank" class="btn btn-info btn-sm" style="color:white; min-width: 36px;" title="Bagikan ke Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <!-- Instagram (hanya info, tidak bisa direct share) -->
                    <a href="https://www.instagram.com/?url=<?php echo e(urlencode(request()->fullUrl())); ?>" target="_blank" class="btn btn-danger btn-sm" title="Bagikan ke Instagram" style="min-width: 36px;">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <!-- Telegram -->
                    <a href="https://t.me/share/url?url=<?php echo e(urlencode(request()->fullUrl())); ?>&amp;text=<?php echo e(urlencode($article->title)); ?>" target="_blank" class="btn btn-secondary btn-sm" title="Bagikan ke Telegram" style="min-width: 36px;">
                        <i class="fab fa-telegram-plane"></i>
                    </a>
                    <?php if($article->image): ?>
                        <img src="<?php echo e(Storage::url($article->image)); ?>" alt="Thumbnail" class="img-thumbnail ms-3" style="max-width:60px; max-height:60px;">
                    <?php endif; ?>
                </div>
            </article>
            
            <!-- Bagian Artikel Terkait -->
            <div class="related-articles mt-5">
                <h3 class="related-title">Baca Juga</h3>
                <div class="title-line"></div>
                
                <div class="mt-4">
                    <?php
                        // Ambil 3 artikel terbaru selain artikel yang sedang dibaca
                        $relatedArticles = \App\Models\Article::where('id', '!=', $article->id)
                            ->where('status', 'published')
                            ->latest()
                            ->take(3)
                            ->get();
                    ?>
                    
                    <ul class="list-unstyled">
                        <?php $__currentLoopData = $relatedArticles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $related): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="related-item mb-3 p-3 border rounded">
                            <div class="d-flex">
                                <div class="related-image-small me-3">
                                    <img src="<?php echo e(Storage::url($related->image)); ?>" alt="<?php echo e($related->title); ?>" class="img-thumbnail" style="width: 80px; height: 60px; object-fit: cover;">
                                </div>
                                <div class="related-content flex-grow-1">
                                    <h6 class="related-title-list mb-1">
                                        <a href="<?php echo e(route('website.artikel.show', $related->slug)); ?>" class="text-decoration-none text-dark">
                                            <?php echo e($related->title); ?>

                                        </a>
                                    </h6>
                                    <p class="related-excerpt mb-1 text-muted small">
                                        <?php echo e(Str::limit($related->excerpt, 100)); ?>

                                    </p>
                                    <p class="related-date-list mb-0 text-muted small">
                                        <i class="fas fa-calendar-alt"></i> <?php echo e($related->created_at->format('d M Y')); ?>

                                    </p>
                                </div>
                            </div>
                        </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
                
                <div class="text-center mt-3">
                    <a href="<?php echo e(route('website.artikel')); ?>" class="btn btn-primary">
                        <i class="fas fa-newspaper"></i> Lihat Semua Artikel
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .title-container {
        margin-bottom: 30px;
        text-align: center;
        padding: 20px 0;
    }
    
    .article-title {
        font-size: 2.5rem;
        text-transform: uppercase;
        font-weight: 800;
        color: #1a1a1a;
        position: relative;
        display: inline-block;
        padding-bottom: 15px;
        margin-bottom: 0;
        letter-spacing: 1px;
        line-height: 1.3;
    }
    
    .article-title:after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background: linear-gradient(to right, #007bff, #00ff88);
        border-radius: 2px;
    }
    
    .article-title:before {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 200px;
        height: 1px;
        background: #e0e0e0;
    }
    
    @media (max-width: 768px) {
        .article-title {
            font-size: 2rem;
        }
    }
    
    .article-content {
        line-height: 1.6;
    }
    
    .article-content img {
        max-width: 100%;
        height: auto;
    }
    
    /* Styling untuk Artikel Terkait */
    .related-articles {
        padding-top: 30px;
        border-top: 1px solid #e0e0e0;
    }
    
    .related-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        position: relative;
        display: inline-block;
        margin-bottom: 10px;
    }
    
    .title-line {
        width: 50px;
        height: 3px;
        background: linear-gradient(to right, #007bff, #00ff88);
        border-radius: 2px;
        margin-bottom: 20px;
    }
    
    .related-card {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        height: 100%;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .related-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }
    
    .related-image {
        position: relative;
        height: 150px;
        overflow: hidden;
    }
    
    .related-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }
    
    .related-card:hover .related-image img {
        transform: scale(1.1);
    }
    
    .related-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.2);
        transition: background 0.3s ease;
    }
    
    .related-card:hover .related-overlay {
        background: rgba(0, 0, 0, 0.4);
    }
    
    .related-body {
        padding: 15px;
    }
    
    .related-card-title {
        font-size: 1rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
        line-height: 1.4;
    }
    
    .related-date {
        font-size: 0.8rem;
        color: #7f8c8d;
        margin-bottom: 0;
    }
    
    @media (max-width: 768px) {
        .related-title {
            font-size: 1.5rem;
        }
    }
</style>
<?php $__env->stopSection(); ?>





<?php echo $__env->make('layouts.website', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/website/artikel/show.blade.php ENDPATH**/ ?>