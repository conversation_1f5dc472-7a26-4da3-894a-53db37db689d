<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JurnalKegiatan extends Model
{
    use HasFactory;

    // Tentukan nama tabel yang benar
    protected $table = 'jurnal_kegiatan';

    protected $fillable = [
        'user_id',
        'unit_id', // Tambahkan unit_id
        'tanggal',
        'kegiatan',
        'ada',
        'tidak',
        'keterangan',
        'status', // 'draft', 'submitted', 'approved', 'rejected'
        'submitted_at',
        'approved_at',
        'rejected_at',
        'rejected_by',
        'alasan_penolakan'
    ];

    // Menambahkan cast untuk tanggal
    protected $casts = [
        'tanggal' => 'date',
        'submitted_at' => 'datetime',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime'
    ];

    // Relasi ke user
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Relasi ke unit
    public function unit()
    {
        return $this->belongsTo(\App\Models\Unit::class);
    }

    // Tambahkan relasi ke jurnal eskul
    public function eskulItems()
    {
        return $this->hasMany(JurnalEskul::class, 'jurnal_kegiatan_id');
    }

    // Tambahkan relasi ke model JurnalKegiatan
    public function jamPengganti()
    {
        return $this->hasMany(JamPengganti::class);
    }

    // Pastikan relasi ini ada di model JurnalKegiatan
    public function penggantiItems()
    {
        return $this->hasMany(JamPengganti::class, 'jurnal_kegiatan_id');
    }

    // Tambahkan relasi untuk jam pengganti
    public function jamPenggantiItems()
    {
        return $this->hasMany(JamPengganti::class);
    }
}






