<?php $__env->startSection('title', 'Daftar Prestasi'); ?>

<?php $__env->startSection('content_header'); ?>
    <h1>Daftar Prestasi</h1>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <a href="<?php echo e(route('admin.website.prestasi.create')); ?>" class="btn btn-primary">
            Tambah Prestasi
        </a>
    </div>
    <div class="card-body">
        <?php if(session('success')): ?>
            <div class="alert alert-success"><?php echo e(session('success')); ?></div>
        <?php endif; ?>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Gambar</th>
                        <th>Judul</th>
                        <th>Tingkat</th>
                        <th>Unit</th>
                        <th>Peserta</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $prestasis; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $prestasi): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td><?php echo e($loop->iteration); ?></td>
                        <td>
                            <?php if($prestasi->image): ?>
                                <img src="<?php echo e(asset('storage/prestasi/' . $prestasi->image)); ?>" 
                                     alt="<?php echo e($prestasi->title); ?>"
                                     class="img-thumbnail"
                                     style="max-height: 100px">
                            <?php else: ?>
                                <span class="text-muted">Tidak ada gambar</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo e($prestasi->title); ?></td>
                        <td><?php echo e($prestasi->level); ?></td>
                        <td><?php echo e($prestasi->unit->nama_unit ?? '-'); ?></td>
                        <td><?php echo e($prestasi->participant); ?></td>
                        <td>
                            <a href="<?php echo e(route('admin.website.prestasi.edit', $prestasi->id)); ?>" 
                               class="btn btn-sm btn-info">Edit</a>
                            <form action="<?php echo e(route('admin.website.prestasi.destroy', $prestasi->id)); ?>" 
                                  method="POST" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-sm btn-danger" 
                                        onclick="return confirm('Yakin ingin menghapus?')">Hapus</button>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="7" class="text-center">Tidak ada data</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <div class="mt-3">
            <?php echo e($prestasis->links()); ?>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
<style>
    .img-thumbnail {
        object-fit: cover;
        width: 100px;
        height: 100px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('adminlte::page', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/admin/website/prestasi/index.blade.php ENDPATH**/ ?>