@extends('layouts.admin')

@section('title')
    <PERSON><PERSON>wal Pelajaran - <PERSON><PERSON><PERSON>
@endsection

@section('content')
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="card-title">
                <i class="fas fa-calendar-alt"></i> J<PERSON>wal Pelajaran - Tam<PERSON>lan <PERSON>
            </h3>
            <div class="card-tools">
                <div class="btn-group">
                    <button type="button" class="btn btn-secondary btn-sm dropdown-toggle" data-toggle="dropdown">
                        <i class="fas fa-filter"></i> Tahun Ajaran: {{ $tahunAjaranTerpilih ?? '2023/2024' }}
                    </button>
                    <div class="dropdown-menu">
                        @if(isset($tahunAjaranList))
                            @foreach($tahunAjaranList as $ta)
                                <a class="dropdown-item {{ $ta == ($tahunAjaranTerpilih ?? '') ? 'active' : '' }}"
                                   href="{{ route('jadwal.jadlaykot', ['tahun_ajaran' => $ta]) }}">
                                    {{ $ta }}
                                </a>
                            @endforeach
                        @endif
                    </div>
                </div>
                @can('manage-jadwal')
                <a href="{{ route('jadwal.create') }}" class="btn btn-primary btn-sm ml-2">
                    <i class="fas fa-plus"></i> Tambah Jadwal
                </a>
                <div class="btn-group ml-2">
                    <button type="button" class="btn btn-success btn-sm dropdown-toggle" data-toggle="dropdown"
                            @if(!isset($jadwalList) || $jadwalList->isEmpty()) disabled title="Tidak ada data jadwal untuk diexport" @endif>
                        <i class="fas fa-file-excel"></i> Download Excel
                    </button>
                    <div class="dropdown-menu">
                        @if(isset($jadwalList) && !$jadwalList->isEmpty())
                            <h6 class="dropdown-header">
                                <i class="fas fa-download"></i> Pilih Format Export
                            </h6>
                            <a href="{{ route('jadwal.export', ['tahunAjaran' => str_replace('/', '-', $tahunAjaranTerpilih ?? '2023-2024'), 'type' => 'kelas']) }}"
                               class="dropdown-item"
                               data-toggle="tooltip"
                               data-placement="left"
                               title="Download jadwal yang dikelompokkan berdasarkan kelas">
                                <i class="fas fa-users text-primary"></i> Per Kelas
                                <small class="text-muted d-block">Jadwal dikelompokkan per kelas</small>
                            </a>
                            <a href="{{ route('jadwal.export', ['tahunAjaran' => str_replace('/', '-', $tahunAjaranTerpilih ?? '2023-2024'), 'type' => 'guru']) }}"
                               class="dropdown-item"
                               data-toggle="tooltip"
                               data-placement="left"
                               title="Download jadwal yang dikelompokkan berdasarkan guru pengajar">
                                <i class="fas fa-chalkboard-teacher text-success"></i> Per Guru
                                <small class="text-muted d-block">Jadwal dikelompokkan per guru</small>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ route('jadwal.export', ['tahunAjaran' => str_replace('/', '-', $tahunAjaranTerpilih ?? '2023-2024'), 'type' => 'all']) }}"
                               class="dropdown-item"
                               data-toggle="tooltip"
                               data-placement="left"
                               title="Download jadwal lengkap dengan semua informasi">
                                <i class="fas fa-table text-info"></i> Lengkap (Semua)
                                <small class="text-muted d-block">Jadwal lengkap semua data</small>
                            </a>
                        @else
                            <span class="dropdown-item-text text-muted">
                                <i class="fas fa-exclamation-circle"></i> Tidak ada data jadwal untuk diexport
                            </span>
                        @endif
                    </div>
                </div>
                @endcan
                <a href="{{ route('jadwal.index') }}" class="btn btn-info btn-sm ml-2">
                    <i class="fas fa-list"></i> Tampilan List
                </a>
            </div>
        </div>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if(isset($jadwalList) && $jadwalList->isEmpty())
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Tidak ada jadwal pelajaran untuk tahun ajaran ini.
            </div>
        @elseif(isset($jadwalList))
            @foreach($jadwalList as $jadwal)
                <div class="mb-5">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="text-primary">
                            <i class="fas fa-school"></i> {{ $jadwal->nama_kelas_text }}
                        </h4>
                        <div class="d-flex align-items-center">
                            <span class="badge badge-info mr-3">{{ $jadwal->wali_kelas }}</span>
                            @can('manage-jadwal')
                            <div class="btn-group">
                                <a href="{{ route('jadwal.edit', $jadwal->id) }}"
                                   class="btn btn-warning btn-sm"
                                   data-toggle="tooltip"
                                   title="Edit Jadwal {{ $jadwal->nama_kelas_text }}">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <button type="button"
                                        class="btn btn-danger btn-sm"
                                        data-toggle="modal"
                                        data-target="#deleteModal{{ $jadwal->id }}"
                                        data-toggle="tooltip"
                                        title="Hapus Jadwal {{ $jadwal->nama_kelas_text }}">
                                    <i class="fas fa-trash"></i> Hapus
                                </button>
                            </div>
                            @endcan
                        </div>
                    </div>
                    
                    @php
                        // Siapkan data untuk tabel
                        $hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];
                        $jamSlots = [];
                        $jadwalData = [];
                        
                        // Kumpulkan semua slot waktu dan data jadwal
                        foreach($jadwal->detailJadwal as $detail) {
                            $waktu = substr($detail->waktu_mulai, 0, 5) . '-' . substr($detail->waktu_selesai, 0, 5);
                            if (!in_array($waktu, $jamSlots)) {
                                $jamSlots[] = $waktu;
                            }
                            $jadwalData[$detail->hari][$waktu] = $detail;
                        }
                        
                        // Urutkan slot waktu
                        sort($jamSlots);
                    @endphp
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped jadwal-table">
                            <thead class="thead-dark">
                                <tr>
                                    <th class="jam-column">
                                        <i class="fas fa-clock"></i> Jam
                                    </th>
                                    @foreach($hari as $h)
                                        <th class="hari-column text-center">
                                            {{ $h }}
                                        </th>
                                    @endforeach
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($jamSlots as $jam)
                                    <tr>
                                        <td class="jam-cell font-weight-bold">
                                            {{ $jam }}
                                        </td>
                                        @foreach($hari as $h)
                                            <td class="mapel-cell">
                                                @if(isset($jadwalData[$h][$jam]))
                                                    @php $detail = $jadwalData[$h][$jam]; @endphp
                                                    @if($detail->is_istirahat || $detail->keterangan)
                                                        <div class="jadwal-item istirahat">
                                                            <div class="mapel-name">
                                                                <i class="fas fa-coffee"></i> {{ $detail->keterangan }}
                                                            </div>
                                                        </div>
                                                    @else
                                                        <div class="jadwal-item mapel">
                                                            <div class="mapel-name">
                                                                {{ $detail->mataPelajaran->nama_mapel }}
                                                            </div>
                                                            @if(auth()->user()->role !== 'guru')
                                                                <div class="guru-name">
                                                                    <i class="fas fa-user"></i> {{ $detail->mataPelajaran->pengajar->name }}
                                                                </div>
                                                            @endif
                                                        </div>
                                                    @endif
                                                @else
                                                    <div class="jadwal-item kosong">
                                                        <span class="text-muted">-</span>
                                                    </div>
                                                @endif
                                            </td>
                                        @endforeach
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    @can('manage-jadwal')
                    <!-- Modal Konfirmasi Hapus -->
                    <div class="modal fade" id="deleteModal{{ $jadwal->id }}" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel{{ $jadwal->id }}" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header bg-danger text-white">
                                    <h5 class="modal-title" id="deleteModalLabel{{ $jadwal->id }}">
                                        <i class="fas fa-exclamation-triangle"></i> Konfirmasi Hapus Jadwal
                                    </h5>
                                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <div class="text-center">
                                        <i class="fas fa-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
                                        <h5 class="mt-3">Apakah Anda yakin?</h5>
                                        <p class="text-muted">
                                            Anda akan menghapus jadwal untuk kelas <strong>{{ $jadwal->nama_kelas_text }}</strong>.<br>
                                            Tindakan ini tidak dapat dibatalkan!
                                        </p>
                                        <div class="alert alert-warning">
                                            <i class="fas fa-info-circle"></i>
                                            <strong>Perhatian:</strong> Semua detail jadwal untuk kelas ini akan dihapus secara permanen.
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                        <i class="fas fa-times"></i> Batal
                                    </button>
                                    <form action="{{ route('jadwal.destroy', $jadwal->id) }}" method="POST" style="display: inline;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger">
                                            <i class="fas fa-trash"></i> Ya, Hapus Jadwal
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endcan
                </div>
            @endforeach
        @else
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> Data jadwal tidak tersedia.
            </div>
        @endif
    </div>
</div>
@endsection

@section('css')
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<style>
    .jadwal-table {
        font-size: 0.9rem;
        border: 2px solid #dee2e6;
    }
    
    .jadwal-table th {
        background: linear-gradient(135deg, #343a40 0%, #495057 100%);
        color: white;
        text-align: center;
        vertical-align: middle;
        border: 1px solid #495057;
        font-weight: 600;
    }
    
    .jam-column {
        width: 120px;
        min-width: 120px;
    }
    
    .hari-column {
        width: calc((100% - 120px) / 5);
        min-width: 150px;
    }
    
    .jam-cell {
        background-color: #f8f9fa;
        text-align: center;
        vertical-align: middle;
        color: #495057;
        border-right: 2px solid #dee2e6;
    }
    
    .mapel-cell {
        padding: 8px;
        vertical-align: middle;
        height: 80px;
        position: relative;
    }
    
    .jadwal-item {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        border-radius: 6px;
        padding: 5px;
        transition: all 0.3s ease;
    }
    
    .jadwal-item.mapel {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border: 1px solid #2196f3;
        color: #1565c0;
    }
    
    .jadwal-item.mapel:hover {
        background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
    }
    
    .jadwal-item.istirahat {
        background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
        border: 1px solid #ff9800;
        color: #e65100;
    }
    
    .jadwal-item.istirahat:hover {
        background: linear-gradient(135deg, #ffcc80 0%, #ffb74d 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);
    }
    
    .jadwal-item.kosong {
        background-color: #f8f9fa;
        border: 1px dashed #dee2e6;
    }
    
    .mapel-name {
        font-weight: 600;
        font-size: 0.85rem;
        line-height: 1.2;
        margin-bottom: 2px;
    }
    
    .guru-name {
        font-size: 0.75rem;
        opacity: 0.8;
        font-style: italic;
    }
    
    .card-title i {
        color: #007bff;
    }
    
    .alert {
        border-radius: 8px;
    }
    
    .badge {
        font-size: 0.8rem;
        padding: 6px 12px;
    }

    /* Styling untuk dropdown dan button */
    .card-tools .btn-group {
        margin-right: 5px;
    }

    .dropdown-menu {
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border: none;
    }

    .dropdown-item {
        padding: 8px 16px;
        transition: all 0.3s ease;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        color: #495057;
        transform: translateX(5px);
    }

    .dropdown-item i {
        width: 20px;
        margin-right: 8px;
    }

    .dropdown-divider {
        margin: 5px 0;
    }

    .btn-success.dropdown-toggle {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }

    .btn-success.dropdown-toggle:hover {
        background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
    }

    .dropdown-item.disabled {
        opacity: 0.6;
        pointer-events: none;
    }

    .dropdown-header {
        font-weight: 600;
        color: #495057;
        font-size: 0.85rem;
        padding: 8px 16px 4px 16px;
        border-bottom: 1px solid #e9ecef;
        margin-bottom: 5px;
    }

    .dropdown-item small {
        font-size: 0.75rem;
        margin-top: 2px;
        line-height: 1.2;
    }

    .dropdown-item:hover small {
        color: #6c757d !important;
    }

    .dropdown-item-text {
        padding: 12px 16px;
        font-size: 0.85rem;
        text-align: center;
    }

    /* Styling untuk tombol aksi jadwal */
    .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
    }

    .btn-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        border: none;
        color: #212529;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
        transition: all 0.3s ease;
    }

    .btn-warning:hover {
        background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
        color: #212529;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(255, 193, 7, 0.4);
    }

    .btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
    }

    /* Styling untuk modal */
    .modal-content {
        border-radius: 10px;
        border: none;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .modal-header.bg-danger {
        border-radius: 10px 10px 0 0;
    }

    .modal-footer {
        border-radius: 0 0 10px 10px;
        border-top: 1px solid #dee2e6;
    }

    @media (max-width: 768px) {
        .jadwal-table {
            font-size: 0.8rem;
        }
        
        .hari-column {
            min-width: 120px;
        }
        
        .jam-column {
            width: 100px;
            min-width: 100px;
        }
        
        .mapel-cell {
            height: 70px;
            padding: 5px;
        }
        
        .mapel-name {
            font-size: 0.8rem;
        }
        
        .guru-name {
            font-size: 0.7rem;
        }
    }
</style>
@endsection

@section('js')
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Flash message fade out
    setTimeout(function() {
        $('.alert-success').fadeOut('slow');
    }, 3000);

    // Add hover effect for better UX
    $('.jadwal-item.mapel, .jadwal-item.istirahat').hover(
        function() {
            $(this).css('cursor', 'pointer');
        }
    );

    // Handle download Excel clicks
    $('.dropdown-item[href*="export"]').click(function(e) {
        var $this = $(this);
        var originalText = $this.html();

        // Show loading state
        $this.html('<i class="fas fa-spinner fa-spin"></i> Mengunduh...');
        $this.addClass('disabled');

        // Reset after 3 seconds
        setTimeout(function() {
            $this.html(originalText);
            $this.removeClass('disabled');
        }, 3000);
    });

    // Add success notification for downloads
    @if(session('download_success'))
        Swal.fire({
            icon: 'success',
            title: 'Berhasil!',
            text: 'File Excel berhasil diunduh.',
            timer: 3000,
            showConfirmButton: false
        });
    @endif

    // Handle delete confirmation
    $('form[action*="destroy"]').on('submit', function(e) {
        e.preventDefault();
        var form = this;
        var modal = $(this).closest('.modal');

        Swal.fire({
            title: 'Menghapus jadwal...',
            text: 'Mohon tunggu sebentar',
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Submit form after showing loading
        setTimeout(function() {
            form.submit();
        }, 1000);
    });

    // Enhanced button hover effects
    $('.btn-group .btn').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );

    // Show success message for CRUD operations
    @if(session('success'))
        Swal.fire({
            icon: 'success',
            title: 'Berhasil!',
            text: '{{ session('success') }}',
            timer: 3000,
            showConfirmButton: false,
            toast: true,
            position: 'top-end'
        });
    @endif

    // Show error message for CRUD operations
    @if(session('error'))
        Swal.fire({
            icon: 'error',
            title: 'Oops...',
            text: '{{ session('error') }}',
            confirmButtonText: 'OK'
        });
    @endif
});
</script>
@endsection
