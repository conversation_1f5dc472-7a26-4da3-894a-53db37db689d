<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Gedung extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'unit_id',
        'nama_gedung',
        'keterangan'
    ];
    
    // Relasi ke unit
    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }
    
    // <PERSON>lasi ke sarana (satu gedung memiliki banyak sarana)
    public function sarana()
    {
        return $this->hasMany(Sarana::class);
    }
}

