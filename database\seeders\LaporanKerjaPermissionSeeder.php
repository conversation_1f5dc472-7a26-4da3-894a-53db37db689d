<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class LaporanKerjaPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buat permission untuk laporan kerja
        Permission::create(['name' => 'lihat-laporan-kerja', 'guard_name' => 'web']);
        Permission::create(['name' => 'kelola-laporan-kerja', 'guard_name' => 'web']);

        // Berikan permission ke role Administrator
        $adminRole = Role::findByName('Administrator', 'web');
        if ($adminRole) {
            $adminRole->givePermissionTo(['lihat-laporan-kerja', 'kelola-laporan-kerja']);
        }

        // Berikan permission ke role lain yang memerlukan akses
        $roles = ['Kepala Sekolah', 'Waka Kurikulum'];
        foreach ($roles as $roleName) {
            $role = Role::findByName($roleName, 'web');
            if ($role) {
                $role->givePermissionTo('lihat-laporan-kerja');
            }
        }
    }
}