<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Achievement extends Model
{
    protected $fillable = [
        'title',
        'slug',
        'description',
        'level',
        'participant',
        'image',
        'unit_id'
    ];

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * Generate slug otomatis saat membuat achievement baru
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($achievement) {
            if (empty($achievement->slug)) {
                $achievement->slug = static::generateUniqueSlug($achievement->title);
            }
        });

        static::updating(function ($achievement) {
            if ($achievement->isDirty('title') && empty($achievement->slug)) {
                $achievement->slug = static::generateUniqueSlug($achievement->title);
            }
        });
    }

    /**
     * Generate slug unik
     */
    private static function generateUniqueSlug($title)
    {
        $baseSlug = Str::slug($title);
        $slug = $baseSlug;
        $counter = 1;

        while (static::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }
}
