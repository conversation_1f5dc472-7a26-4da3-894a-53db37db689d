/**
 * Mendapatkan daftar siswa berdasarkan kelas untuk tahun ajaran aktif
 */
public function getSiswaByKelas($kelasId)
{
    try {
        // Ambil tahun ajaran aktif
        $tahunAjaranAktif = \App\Models\TahunAjaran::where('aktif', true)->first();
        
        if (!$tahunAjaranAktif) {
            \Log::error('Tahun ajaran aktif tidak ditemukan');
            return response()->json(['error' => 'Tahun ajaran aktif tidak ditemukan'], 404);
        }
        
        // Ambil siswa dari riwayat kelas berdasarkan kelas_id dan tahun ajaran aktif
        $siswa = \App\Models\PesertaDidik::select('peserta_didik.id', 'peserta_didik.nis', 'peserta_didik.nama')
            ->join('riwayat_kelas', 'peserta_didik.id', '=', 'riwayat_kelas.siswa_id')
            ->where('riwayat_kelas.kelas_id', $kelasId)
            ->where('riwayat_kelas.tahun_ajaran', $tahunAjaranAktif->nama)
            ->where('peserta_didik.status', 'aktif')
            ->orderBy('peserta_didik.nama')
            ->get();
        
        \Log::info('Berhasil mengambil ' . $siswa->count() . ' siswa dari kelas ' . $kelasId . ' untuk tahun ajaran ' . $tahunAjaranAktif->nama);
        
        return response()->json($siswa);
    } catch (\Exception $e) {
        \Log::error('Error saat mengambil data siswa: ' . $e->getMessage());
        \Log::error($e->getTraceAsString());
        
        return response()->json(['error' => $e->getMessage()], 500);
    }
}