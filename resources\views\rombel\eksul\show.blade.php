@extends('adminlte::page')

@section('title', 'Detail Rombel Ekstrakurikuler')

@section('content_header')
    <h1>Detail Rombel Ekstrakurikuler</h1>
@stop
 
@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Informasi Rombel</h3>
                <div class="card-tools">
                    <a href="{{ route('rombel.eksul') }}" class="btn btn-sm btn-default">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">Nama Rombel</th>
                                <td>{{ $rombelEksul->nama_rombel }}</td>
                            </tr>
                            <tr>
                                <th>Ekstrakurikuler</th>
                                <td>{{ $rombelEksul->ekstrakurikuler }}</td>
                            </tr>
                            <!-- Hapus atau komentari baris berikut karena tidak ada relasi unit -->
                            <!-- <tr>
                                <th>Unit</th>
                                <td>{{ $rombelEksul->ekstrakurikuler->unit->nama_unit ?? '-' }}</td>
                            </tr> -->
                            <tr>
                                <th>Tahun Ajaran</th>
                                <td>{{ $rombelEksul->tahun_ajaran }}</td>
                            </tr>
                            <tr>
                                <th>Pembina</th>
                                <td>{{ $rombelEksul->pembina }}</td>
                            </tr>
                            <tr>
                                <th>Jumlah Anggota</th>
                                <td>{{ $rombelEksul->anggota->count() }} siswa</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Daftar Anggota</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>NIS</th>
                            <th>Nama Siswa</th>
                            <th>Kelas</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($rombelEksul->anggota as $key => $anggota)
                            <tr>
                                <td>{{ $key + 1 }}</td>
                                <td>{{ $anggota->siswa->nis ?? '-' }}</td>
                                <td>{{ $anggota->siswa->nama }}</td>
                                <td>
                                    @if($anggota->kelas)
                                        {{ $anggota->kelas->nama }}
                                    @else
                                        -
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4" class="text-center">Belum ada anggota</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            // Inisialisasi DataTables jika diperlukan
            $('.table').DataTable({
                "paging": true,
                "lengthChange": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
            });
        });
    </script>
@stop



