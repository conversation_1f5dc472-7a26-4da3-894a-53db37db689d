<?php $__env->startSection('title', 'Manajemen Artikel'); ?>

<?php $__env->startSection('content_header'); ?>
    <h1>Manajemen Artikel</h1>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar Artikel</h3>
        <div class="card-tools">
            <a href="<?php echo e(route('admin.website.artikel.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah Artikel
            </a>
        </div>
    </div>
    <div class="card-body">
        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo e(session('success')); ?>

                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        <?php endif; ?>

        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th style="width: 50px">No</th>
                    <th style="width: 150px">Gambar</th>
                    <th>Judul</th>
                    <th>Status</th>
                    <th>Penulis</th>
                    <th>Tanggal Publikasi</th>
                    <th style="width: 150px">Aksi</th>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $articles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td><?php echo e($loop->iteration); ?></td>
                    <td>
                        <img src="<?php echo e(asset('storage/' . $article->image)); ?>" 
                             alt="<?php echo e($article->title); ?>" 
                             class="img-thumbnail"
                             style="max-height: 100px">
                    </td>
                    <td><?php echo e($article->title); ?></td>
                    <td>
                        <span class="badge badge-<?php echo e($article->status === 'published' ? 'success' : ($article->status === 'draft' ? 'warning' : 'secondary')); ?>">
                            <?php echo e(ucfirst($article->status)); ?>

                        </span>
                    </td>
                    <td><?php echo e($article->author->name); ?></td>
                    <td><?php echo e($article->published_at ? $article->published_at->format('d/m/Y H:i') : '-'); ?></td>
                    <td>
                        <a href="<?php echo e(route('admin.website.artikel.edit', $article)); ?>" 
                           class="btn btn-sm btn-info">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="<?php echo e(route('admin.website.artikel.destroy', $article)); ?>" 
                              method="POST" 
                              class="d-inline">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" 
                                    class="btn btn-sm btn-danger" 
                                    onclick="return confirm('Apakah Anda yakin ingin menghapus artikel ini?')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="7" class="text-center">Tidak ada artikel</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>

        <div class="mt-3">
            <?php echo e($articles->links()); ?>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" href="/css/admin_custom.css">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script>
        $(document).ready(function() {
            // Auto close alert after 3 seconds
            setTimeout(function() {
                $(".alert").alert('close');
            }, 3000);
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('adminlte::page', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/admin/website/artikel/index.blade.php ENDPATH**/ ?>