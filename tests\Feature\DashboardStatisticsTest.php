<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Event;
use App\Models\Achievement;
use App\Models\Facility;
use App\Models\Ekstrakurikuler;
use App\Models\Slide;
use App\Models\Unit;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;

class DashboardStatisticsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'Administrator']);
        Role::create(['name' => 'Guru']);
    }

    /** @test */
    public function admin_can_view_dashboard_with_all_statistics()
    {
        // Create admin user
        $admin = User::factory()->create();
        $admin->assignRole('Administrator');

        // Create test data
        $unit = Unit::create([
            'jenjang_id' => 'SD',
            'nama_unit' => 'SD Test Unit',
            'nss' => '123456',
            'npsn' => '654321'
        ]);

        Event::create([
            'judul' => 'Test Event',
            'slug' => 'test-event',
            'deskripsi' => 'Test Description',
            'tanggal' => now(),
            'lokasi' => 'Test Location',
            'unit_id' => $unit->id
        ]);

        Achievement::create([
            'title' => 'Test Achievement',
            'description' => 'Test Description',
            'level' => 'Nasional',
            'participant' => 'Test Student',
            'unit_id' => $unit->id
        ]);

        Facility::create([
            'title' => 'Test Facility',
            'description' => 'Test Description'
        ]);

        Ekstrakurikuler::create([
            'nama' => 'Test Ekstrakurikuler',
            'deskripsi' => 'Test Description',
            'unit_id' => $unit->id
        ]);

        Slide::create([
            'title' => 'Test Slide',
            'description' => 'Test Description',
            'image' => 'test.jpg',
            'status' => 1
        ]);

        // Act
        $response = $this->actingAs($admin)->get('/admin/dashboard');

        // Assert
        $response->assertStatus(200);
        $response->assertViewHas([
            'totalEvents',
            'totalAchievements', 
            'totalFacilities',
            'totalExtracurriculars',
            'totalSlides'
        ]);
    }

    /** @test */
    public function unit_user_can_view_dashboard_with_filtered_statistics()
    {
        // Create unit
        $unit = Unit::create([
            'jenjang_id' => 'SD',
            'nama_unit' => 'SD Test Unit',
            'nss' => '123456',
            'npsn' => '654321'
        ]);

        // Create user with unit
        $user = User::factory()->create(['unit_id' => $unit->id]);
        $user->assignRole('Guru');

        // Create test data for this unit
        Event::create([
            'judul' => 'Unit Event',
            'slug' => 'unit-event',
            'deskripsi' => 'Test Description',
            'tanggal' => now(),
            'lokasi' => 'Test Location',
            'unit_id' => $unit->id
        ]);

        Achievement::create([
            'title' => 'Unit Achievement',
            'description' => 'Test Description',
            'level' => 'Nasional',
            'participant' => 'Test Student',
            'unit_id' => $unit->id
        ]);

        // Act
        $response = $this->actingAs($user)->get('/admin/dashboard');

        // Assert
        $response->assertStatus(200);
        $response->assertViewHas([
            'totalEvents',
            'totalAchievements',
            'totalFacilities',
            'totalExtracurriculars',
            'totalSlides'
        ]);
    }
}
