<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PesertaDidik extends Model
{
    use HasFactory;

    protected $table = 'peserta_didik';
    
    protected $fillable = [
        'nis',
        'nisn',
        'nik',
        'nama',
        'jenis_kelamin',
        'tempat_lahir',
        'tanggal_lahir',
        'agama',
        'kebutuhan_khusus',
        'alamat',
        'rt',
        'rw',
        'dusun',
        'kelurahan',
        'kecamatan',
        'kode_pos',
        'jenis_tinggal',
        'transportasi',
        'no_telp',
        'no_hp',
        'email',
        'kelas_id',
        'tingkat',
        'nama_program',
        'no_reg',
        'npsn',
        'sekolah_asal',
        'seri_ijasah',
        'seri_skhun',
        'no_unas',
        'tinggi_badan',
        'berat_badan',
        'jarak_rumah',
        'waktu_tempuh',
        'jumlah_saudara',
        'no_kks',
        'status_kps',
        'no_ksp',
        'status_pip',
        'usulan_pip',
        'nama_ayah',
        'NIK_ayah',
        'tahun_lahir_ayah',
        'pendidikan_ayah',
        'pekerjaan_ayah',
        'penghasilan_ayah',
        'kebutuhan_khusus_ayah',
        'nama_ibu',
        'NIK_ibu',
        'tahun_lahir_ibu',
        'pendidikan_ibu',
        'pekerjaan_ibu',
        'penghasilan_ibu',
        'kebutuhan_khusus_ibu',
        'nama_wali',
        'NIK_wali',
        'tahun_lahir_wali',
        'pendidikan_wali',
        'pekerjaan_wali',
        'penghasilan_wali',
        'unit_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'tanggal_lahir' => 'date',
    ];

    protected $attributes = [
        'is_active' => true,
        'status' => 'aktif', // nilai default: aktif, alumni, mutasi_keluar
    ];

    public function kelas()
    {
        return $this->belongsTo(Kelas::class);
    }

    /**
     * Relasi dengan riwayat kelas
     */
    public function riwayatKelas()
    {
        return $this->hasMany(RiwayatKelas::class, 'siswa_id');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function scopeByUnit($query, $unitId)
    {
        return $query->where('unit_id', $unitId);
    }

    public function scopeAktif($query)
    {
        return $query->where('status', 'aktif');
    }
}
