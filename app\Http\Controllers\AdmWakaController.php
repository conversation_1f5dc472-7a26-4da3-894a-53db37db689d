<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\AdmWaka;
use Illuminate\Support\Facades\Storage;
use App\Traits\FiltersByUserUnit;

class AdmWakaController extends Controller
{
    use FiltersByUserUnit;
    
    public function index()
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        $query = AdmWaka::with(['user', 'unit'])->latest();
        
        // Jika user bukan admin atau kepala sekolah, hanya tampilkan ADM miliknya
        if (!$user->hasAnyRole(['Administrator', 'Kepala Sekolah', 'Waka Kurikulum'])) {
            $query->where('user_id', $user->id);
        } else {
            // Terapkan filter unit menggunakan trait
            $query = $this->applyUnitFilter($query);
        }
        
        $admList = $query->get();
        return view('adm.waka.index', compact('admList'));
    }

    public function store(Request $request)
    {
        try {
            $request->validate([
                'judul' => 'required|string',
                'keterangan' => 'nullable|string',
                'file' => 'required|mimes:pdf|max:10240'
            ]);

            $file = $request->file('file');
            $filename = time() . '_' . $file->getClientOriginalName();
            
            // Simpan file langsung ke public disk
            $path = $file->storeAs('adm-waka', $filename, 'public');
            
            // Log untuk debugging
            Log::info('File uploaded', [
                'original_name' => $file->getClientOriginalName(),
                'stored_path' => $path,
                'full_path' => storage_path('app/public/' . $path),
                'exists' => Storage::disk('public')->exists($path)
            ]);

            // Ambil user yang sedang login
            $user = auth()->user();
            
            // Tambahkan log untuk debugging unit_id
            Log::info('User unit_id', [
                'user_id' => $user->id,
                'unit_id' => $user->unit_id,
                'has_unit' => !is_null($user->unit_id)
            ]);

            // Buat record ADM Waka dengan unit_id dari user
            AdmWaka::create([
                'user_id' => $user->id,
                'unit_id' => $user->unit_id, // Pastikan unit_id diambil dari user
                'judul' => $request->judul,
                'keterangan' => $request->keterangan,
                'file_path' => $path,
                'status' => 'pending'
            ]);
            
            return redirect()->route('adm.waka.index')
                ->with('success', 'ADM Waka berhasil diupload');
        } catch (\Exception $e) {
            Log::error('Error in AdmWakaController@store: ' . $e->getMessage());
            return redirect()->route('adm.waka.index')
                ->with('error', 'Terjadi kesalahan saat upload ADM: ' . $e->getMessage());
        }
    }

    public function approve(AdmWaka $adm)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        if (!$user->hasRole('Kepala Sekolah')) {
            return redirect()->route('dashboard')
                ->with('error', 'Anda tidak memiliki izin untuk menyetujui ADM Waka.');
        }

        try {
            if ($adm->status !== 'pending') {
                return redirect()->route('adm.waka.index')
                    ->with('error', 'ADM sudah diproses sebelumnya');
            }

            $adm->update([
                'status' => 'approved',
                'approved_by' => auth()->id(),
                'approved_at' => now()
            ]);

            return redirect()->route('adm.waka.index')
                ->with('success', 'ADM berhasil disetujui');
        } catch (\Exception $e) {
            Log::error('Error in AdmWakaController@approve: ' . $e->getMessage());
            return redirect()->route('adm.waka.index')
                ->with('error', 'Terjadi kesalahan saat menyetujui ADM');
        }
    }

    public function reject(Request $request, AdmWaka $adm)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        if (!$user->hasRole('Kepala Sekolah')) {
            return redirect()->route('dashboard')
                ->with('error', 'Anda tidak memiliki izin untuk menolak ADM Waka.');
        }

        try {
            $request->validate([
                'alasan_penolakan' => 'required|string|max:255'
            ]);

            if ($adm->status !== 'pending') {
                return redirect()->route('adm.waka.index')
                    ->with('error', 'ADM sudah diproses sebelumnya');
            }

            $adm->update([
                'status' => 'ditangguhkan',
                'rejected_by' => auth()->id(),
                'rejected_at' => now(),
                'alasan_penolakan' => $request->alasan_penolakan
            ]);

            return redirect()->route('adm.waka.index')
                ->with('success', 'ADM berhasil ditangguhkan');
        } catch (\Exception $e) {
            Log::error('Error in AdmWakaController@reject: ' . $e->getMessage());
            return redirect()->route('adm.waka.index')
                ->with('error', 'Terjadi kesalahan saat menolak ADM');
        }
    }

    /**
     * Menampilkan halaman view PDF
     *
     * @param string $filename
     * @return \Illuminate\View\View
     */
    public function viewPage($filename)
    {
        $filePath = 'adm-waka/' . $filename;
        
        if (!Storage::disk('public')->exists($filePath)) {
            abort(404, 'File tidak ditemukan');
        }
        
        return view('adm.waka.view-pdf', compact('filename'));
    }

    /**
     * Menampilkan file ADM Waka
     *
     * @param string $filename
     * @return \Illuminate\Http\Response
     */
    public function viewFile($filename)
    {
        try {
            // Cari file di disk public
            if (!Storage::disk('public')->exists('adm-waka/' . $filename)) {
                Log::warning('File not found: adm-waka/' . $filename);
                abort(404, 'File tidak ditemukan');
            }

            $path = Storage::disk('public')->path('adm-waka/' . $filename);
            
            return response()->file($path, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="' . $filename . '"'
            ]);
        } catch (\Exception $e) {
            Log::error('Error viewing file: ' . $e->getMessage());
            return response()->json(['error' => 'File tidak dapat diakses'], 404);
        }
    }
}










