@extends('layouts.admin')

@section('title', 'Detail Guru')

@section('content_header')
    <h1>Detail Guru</h1>
@stop

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Informasi Guru</h3>
        <div class="card-tools">
            <!--
            <a href="{{ route('gtk.guru.index') }}" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            -->
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>

            <a href="{{ route('gtk.guru.edit', $guru->id) }}" class="btn btn-sm btn-warning">
                <i class="fas fa-edit"></i> Edit
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-12">
                <h5 class="section-title font-weight-bold mb-3">IDENTITAS SEKOLAH</h5>
            </div>
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 200px">Unit</th>
                        <td>{{ $guru->unit->nama_unit ?? '-' }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <h5 class="section-title font-weight-bold mb-3">IDENTITAS GURU</h5>
            </div>
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 200px">Nama</th>
                        <td>{{ $guru->nama }}</td>
                    </tr>
                    <tr>
                        <th>NIK</th>
                        <td>{{ $guru->nik }}</td>
                    </tr>
                    <tr>
                        <th>Jenis Kelamin</th>
                        <td>{{ $guru->jenis_kelamin == 'L' ? 'Laki-laki' : 'Perempuan' }}</td>
                    </tr>
                    <tr>
                        <th>Tempat, Tanggal Lahir</th>
                        <td>{{ $guru->tempat_lahir }}, {{ \Carbon\Carbon::parse($guru->tanggal_lahir)->format('d-m-Y') }}</td>
                    </tr>
                    <tr>
                        <th>Nama Ibu Kandung</th>
                        <td>{{ $guru->nama_ibu_kandung }}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 200px">Agama</th>
                        <td>{{ $guru->agama }}</td>
                    </tr>
                    <tr>
                        <th>Status Kawin</th>
                        <td>{{ $guru->status_kawin }}</td>
                    </tr>
                    <tr>
                        <th>Kewarganegaraan</th>
                        <td>{{ $guru->kewarganegaraan }}</td>
                    </tr>
                    <tr>
                        <th>NPWP</th>
                        <td>{{ $guru->npwp ?? '-' }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <h5 class="section-title font-weight-bold mb-3">ALAMAT</h5>
            </div>
            <div class="col-md-12">
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 200px">Alamat</th>
                        <td>{{ $guru->alamat }}</td>
                    </tr>
                    <tr>
                        <th>Kelurahan</th>
                        <td>{{ $guru->kelurahan }}</td>
                    </tr>
                    <tr>
                        <th>Kecamatan</th>
                        <td>{{ $guru->kecamatan }}</td>
                    </tr>
                    <tr>
                        <th>Kabupaten/Kota</th>
                        <td>{{ $guru->kabupaten }}</td>
                    </tr>
                    <tr>
                        <th>Provinsi</th>
                        <td>{{ $guru->provinsi }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <h5 class="section-title font-weight-bold mb-3">DATA KEPEGAWAIAN</h5>
            </div>
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 200px">Status Pegawai</th>
                        <td>{{ $guru->status_pegawai ?? '-' }}</td>
                    </tr>
                    <tr>
                        <th>NIY/NIGK</th>
                        <td>{{ $guru->niy ?? '-' }}</td>
                    </tr>
                    <tr>
                        <th>NUPTK</th>
                        <td>{{ $guru->nuptk ?? '-' }}</td>
                    </tr>
                    <tr>
                        <th>Jenis PTK</th>
                        <td>{{ $guru->jenis_ptk ?? '-' }}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 200px">SK Pengangkatan</th>
                        <td>{{ $guru->sk_pengangkatan ?? '-' }}</td>
                    </tr>
                    <tr>
                        <th>TMT Pengangkatan</th>
                        <td>{{ $guru->tmt_pengangkatan ? \Carbon\Carbon::parse($guru->tmt_pengangkatan)->format('d-m-Y') : '-' }}</td>
                    </tr>
                    <tr>
                        <th>Lembaga Pengangkat</th>
                        <td>{{ $guru->lembaga_pengangkat ?? '-' }}</td>
                    </tr>
                    <tr>
                        <th>Pangkat/Golongan</th>
                        <td>{{ $guru->pangkat_golongan ?? '-' }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <h5 class="section-title font-weight-bold mb-3">KONTAK</h5>
            </div>
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 200px">No. Telepon</th>
                        <td>{{ $guru->no_telp ?? '-' }}</td>
                    </tr>
                    <tr>
                        <th>Email</th>
                        <td>{{ $guru->email ?? '-' }}</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 200px">Mata Pelajaran</th>
                        <td>{{ $guru->mata_pelajaran ?? '-' }}</td>
                    </tr>
                    <tr>
                        <th>Status</th>
                        <td>
                            @if($guru->status == 'Aktif')
                                <span class="badge badge-success">Aktif</span>
                            @else
                                <span class="badge badge-danger">Non-Aktif</span>
                            @endif
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Tambahkan Section Pengembangan Diri -->
        <div class="row mt-4">
            <div class="col-md-12">
                <h5 class="section-title font-weight-bold mb-3">PENGEMBANGAN DIRI</h5>
                <div class="mb-3">
                    <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#modalTambahPengembangan">
                        <i class="fas fa-plus"></i> Tambah Data Pengembangan Diri
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="tablePengembanganDiri">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Jenis Diklat</th>
                                <th>Nama</th>
                                <th>Penyelenggara</th>
                                <th>Tingkat</th>
                                <th>Tahun</th>
                                <th>Peran</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($pengembanganDiri as $index => $pd)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $pd->jenis_diklat }}</td>
                                <td>{{ $pd->nama }}</td>
                                <td>{{ $pd->penyelenggara }}</td>
                                <td>{{ $pd->tingkat }}</td>
                                <td>{{ $pd->tahun }}</td>
                                <td>{{ $pd->peran }}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-warning" 
                                            data-toggle="modal" data-target="#editPengembangan{{ $pd->id }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <form action="{{ route('gtk.guru.pengembangan-diri.destroy', $pd->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus data pengembangan diri ini?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            
                            <!-- Modal Edit untuk setiap pengembangan diri -->
                            <div class="modal fade" id="editPengembangan{{ $pd->id }}" tabindex="-1" role="dialog" aria-labelledby="editPengembangan{{ $pd->id }}Label" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="editPengembangan{{ $pd->id }}Label">Edit Pengembangan Diri</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form action="{{ route('gtk.guru.pengembangan-diri.update', $pd->id) }}" method="POST">
                                            @csrf
                                            @method('PUT')
                                            <div class="modal-body">
                                                <div class="form-group">
                                                    <label for="jenis_diklat{{ $pd->id }}">Jenis Diklat <span class="text-danger">*</span></label>
                                                    <select name="jenis_diklat" id="jenis_diklat{{ $pd->id }}" class="form-control" required>
                                                        <option value="">Pilih Jenis Diklat</option>
                                                        <option value="Diklat Fungsional" {{ $pd->jenis_diklat == 'Diklat Fungsional' ? 'selected' : '' }}>Diklat Fungsional</option>
                                                        <option value="Diklat Teknis" {{ $pd->jenis_diklat == 'Diklat Teknis' ? 'selected' : '' }}>Diklat Teknis</option>
                                                        <option value="Seminar" {{ $pd->jenis_diklat == 'Seminar' ? 'selected' : '' }}>Seminar</option>
                                                        <option value="Workshop" {{ $pd->jenis_diklat == 'Workshop' ? 'selected' : '' }}>Workshop</option>
                                                        <option value="Pelatihan" {{ $pd->jenis_diklat == 'Pelatihan' ? 'selected' : '' }}>Pelatihan</option>
                                                        <option value="Lainnya" {{ $pd->jenis_diklat == 'Lainnya' ? 'selected' : '' }}>Lainnya</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="nama{{ $pd->id }}">Nama Kegiatan <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="nama{{ $pd->id }}" name="nama" value="{{ $pd->nama }}" required>
                                                </div>
                                                <div class="form-group">
                                                    <label for="penyelenggara{{ $pd->id }}">Penyelenggara <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="penyelenggara{{ $pd->id }}" name="penyelenggara" value="{{ $pd->penyelenggara }}" required>
                                                </div>
                                                <div class="form-group">
                                                    <label for="tingkat{{ $pd->id }}">Tingkat</label>
                                                    <select name="tingkat" id="tingkat{{ $pd->id }}" class="form-control">
                                                        <option value="">Pilih Tingkat</option>
                                                        <option value="Kecamatan" {{ $pd->tingkat == 'Kecamatan' ? 'selected' : '' }}>Kecamatan</option>
                                                        <option value="Kabupaten/Kota" {{ $pd->tingkat == 'Kabupaten/Kota' ? 'selected' : '' }}>Kabupaten/Kota</option>
                                                        <option value="Provinsi" {{ $pd->tingkat == 'Provinsi' ? 'selected' : '' }}>Provinsi</option>
                                                        <option value="Nasional" {{ $pd->tingkat == 'Nasional' ? 'selected' : '' }}>Nasional</option>
                                                        <option value="Internasional" {{ $pd->tingkat == 'Internasional' ? 'selected' : '' }}>Internasional</option>
                                                    </select>
                                                </div>
                                                <div class="form-group">
                                                    <label for="tahun{{ $pd->id }}">Tahun <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control" id="tahun{{ $pd->id }}" name="tahun" min="2020" max="{{ date('Y') }}" value="{{ $pd->tahun }}" required>
                                                </div>
                                                <div class="form-group">
                                                    <label for="peran{{ $pd->id }}">Peran</label>
                                                    <select name="peran" id="peran{{ $pd->id }}" class="form-control">
                                                        <option value="">Pilih Peran</option>
                                                        <option value="Peserta" {{ $pd->peran == 'Peserta' ? 'selected' : '' }}>Peserta</option>
                                                        <option value="Pemateri" {{ $pd->peran == 'Pemateri' ? 'selected' : '' }}>Pemateri</option>
                                                        <option value="Panitia" {{ $pd->peran == 'Panitia' ? 'selected' : '' }}>Panitia</option>
                                                        <option value="Lainnya" {{ $pd->peran == 'Lainnya' ? 'selected' : '' }}>Lainnya</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                                <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center">Belum ada data pengembangan diri</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Tambahkan Section Tugas Tambahan setelah Pengembangan Diri -->
        <div class="row mt-4">
            <div class="col-md-12">
                <h5 class="section-title font-weight-bold mb-3">TUGAS TAMBAHAN</h5>
                <div class="mb-3">
                    <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#modalTambahTugas">
                        <i class="fas fa-plus"></i> Tambah Tugas Tambahan
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="tableTugasTambahan">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Tugas Tambahan</th>
                                <th>Kelas</th>
                                <th>Tahun Ajaran</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($tugasTambahan as $index => $tugas)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $tugas->tugas_tambahan }}</td>
                                <td>{{ $tugas->kelas ?? '-' }}</td>
                                <td>{{ $tugas->tahunAjaran->nama }}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-warning" 
                                            data-toggle="modal" data-target="#editTugas{{ $tugas->id }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <form action="{{ route('gtk.guru.tugas-tambahan.destroy', $tugas->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus data tugas tambahan ini?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            
                            <!-- Modal Edit untuk setiap tugas tambahan -->
                            <div class="modal fade" id="editTugas{{ $tugas->id }}" tabindex="-1" role="dialog" aria-labelledby="editTugas{{ $tugas->id }}Label" aria-hidden="true">
                                <div class="modal-dialog" role="document">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="editTugas{{ $tugas->id }}Label">Edit Tugas Tambahan</h5>
                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        <form action="{{ route('gtk.guru.tugas-tambahan.update', $tugas->id) }}" method="POST">
                                            @csrf
                                            @method('PUT')
                                            <div class="modal-body">
                                                <div class="form-group">
                                                    <label for="tugas_tambahan{{ $tugas->id }}">Tugas Tambahan <span class="text-danger">*</span></label>
                                                    <select name="tugas_tambahan" id="tugas_tambahan{{ $tugas->id }}" class="form-control" required onchange="toggleKelasFieldEdit({{ $tugas->id }})">
                                                        <option value="">Pilih Tugas Tambahan</option>
                                                        <option value="Kepala Sekolah" {{ $tugas->tugas_tambahan == 'Kepala Sekolah' ? 'selected' : '' }}>Kepala Sekolah</option>
                                                        <option value="Waka Kurikulum" {{ $tugas->tugas_tambahan == 'Waka Kurikulum' ? 'selected' : '' }}>Waka Kurikulum</option>
                                                        <option value="Waka Sarpras" {{ $tugas->tugas_tambahan == 'Waka Sarpras' ? 'selected' : '' }}>Waka Sarpras</option>
                                                        <option value="Waka Kesiswaan" {{ $tugas->tugas_tambahan == 'Waka Kesiswaan' ? 'selected' : '' }}>Waka Kesiswaan</option>
                                                        <option value="Wali Kelas" {{ $tugas->tugas_tambahan == 'Wali Kelas' ? 'selected' : '' }}>Wali Kelas</option>
                                                        <option value="Guru Kelas" {{ $tugas->tugas_tambahan == 'Guru Kelas' ? 'selected' : '' }}>Guru Kelas</option>
                                                        <option value="Perpustakaan" {{ $tugas->tugas_tambahan == 'Perpustakaan' ? 'selected' : '' }}>Perpustakaan</option>
                                                    </select>
                                                </div>
                                                <div class="form-group" id="kelas_container{{ $tugas->id }}" style="{{ in_array($tugas->tugas_tambahan, ['Wali Kelas', 'Guru Kelas']) ? 'display: block;' : 'display: none;' }}">
                                                    <label for="kelas{{ $tugas->id }}">Kelas <span class="text-danger">*</span></label>
                                                    <input type="text" name="kelas" id="kelas{{ $tugas->id }}" class="form-control" value="{{ $tugas->kelas }}" placeholder="Contoh: X IPA 1">
                                                </div>
                                                <div class="form-group">
                                                    <label for="tahun_ajaran_id{{ $tugas->id }}">Tahun Ajaran <span class="text-danger">*</span></label>
                                                    <select name="tahun_ajaran_id" id="tahun_ajaran_id{{ $tugas->id }}" class="form-control" required>
                                                        @foreach($tahunAjaranList as $ta)
                                                            <option value="{{ $ta->id }}" {{ $tugas->tahun_ajaran_id == $ta->id ? 'selected' : '' }}>
                                                                {{ $ta->nama }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                                <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            @empty
                            <tr>
                                <td colspan="5" class="text-center">Belum ada data tugas tambahan</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Modal Tambah Pengembangan Diri -->
        <div class="modal fade" id="modalTambahPengembangan" tabindex="-1" role="dialog" aria-labelledby="modalTambahPengembanganLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalTambahPengembanganLabel">Tambah Data Pengembangan Diri</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <form action="{{ route('gtk.guru.pengembangan-diri.store', $guru->id) }}" method="POST">
                        @csrf
                        <div class="modal-body">
                            <div class="form-group">
                                <label for="jenis_diklat">Jenis Diklat <span class="text-danger">*</span></label>
                                <select name="jenis_diklat" id="jenis_diklat" class="form-control" required>
                                    <option value="">Pilih Jenis Diklat</option>
                                    <option value="Diklat Fungsional">Diklat Fungsional</option>
                                    <option value="Diklat Teknis">Diklat Teknis</option>
                                    <option value="Seminar">Seminar</option>
                                    <option value="Workshop">Workshop</option>
                                    <option value="Pelatihan">Pelatihan</option>
                                    <option value="Lainnya">Lainnya</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="nama">Nama Kegiatan <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nama" name="nama" required>
                            </div>
                            <div class="form-group">
                                <label for="penyelenggara">Penyelenggara <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="penyelenggara" name="penyelenggara" required>
                            </div>
                            <div class="form-group">
                                <label for="tingkat">Tingkat</label>
                                <select name="tingkat" id="tingkat" class="form-control">
                                    <option value="">Pilih Tingkat</option>
                                    <option value="Kecamatan">Kecamatan</option>
                                    <option value="Kabupaten/Kota">Kabupaten/Kota</option>
                                    <option value="Provinsi">Provinsi</option>
                                    <option value="Nasional">Nasional</option>
                                    <option value="Internasional">Internasional</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="tahun">Tahun <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="tahun" name="tahun" min="2020" max="{{ date('Y') }}" required>
                            </div>
                            <div class="form-group">
                                <label for="peran">Peran</label>
                                <select name="peran" id="peran" class="form-control">
                                    <option value="">Pilih Peran</option>
                                    <option value="Peserta">Peserta</option>
                                    <option value="Pemateri">Pemateri</option>
                                    <option value="Panitia">Panitia</option>
                                    <option value="Lainnya">Lainnya</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-primary">Simpan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Modal Edit Pengembangan Diri -->
        <div class="modal fade" id="modalEditPengembangan" tabindex="-1" role="dialog" aria-labelledby="modalEditPengembanganLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalEditPengembanganLabel">Edit Pengembangan Diri</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <form id="formEditPengembangan" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="modal-body">
                            <div class="form-group">
                                <label for="edit_jenis_diklat">Jenis Diklat</label>
                                <select class="form-control" id="edit_jenis_diklat" name="jenis_diklat" required>
                                    <option value="">Pilih Jenis Diklat</option>
                                    <option value="Diklat Fungsional">Diklat Fungsional</option>
                                    <option value="Diklat Teknis">Diklat Teknis</option>
                                    <option value="Seminar">Seminar</option>
                                    <option value="Workshop">Workshop</option>
                                    <option value="Pelatihan">Pelatihan</option>
                                    <option value="Lainnya">Lainnya</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="edit_nama">Nama Kegiatan</label>
                                <input type="text" class="form-control" id="edit_nama" name="nama" required>
                            </div>
                            <div class="form-group">
                                <label for="edit_penyelenggara">Penyelenggara</label>
                                <input type="text" class="form-control" id="edit_penyelenggara" name="penyelenggara" required>
                            </div>
                            <div class="form-group">
                                <label for="edit_tingkat">Tingkat</label>
                                <select class="form-control" id="edit_tingkat" name="tingkat">
                                    <option value="">Pilih Tingkat</option>
                                    <option value="Kecamatan">Kecamatan</option>
                                    <option value="Kabupaten/Kota">Kabupaten/Kota</option>
                                    <option value="Provinsi">Provinsi</option>
                                    <option value="Nasional">Nasional</option>
                                    <option value="Internasional">Internasional</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="edit_tahun">Tahun</label>
                                <input type="number" class="form-control" id="edit_tahun" name="tahun" min="2020" max="{{ date('Y') }}" required>
                            </div>
                            <div class="form-group">
                                <label for="edit_peran">Peran</label>
                                <select class="form-control" id="edit_peran" name="peran">
                                    <option value="">Pilih Peran</option>
                                    <option value="Peserta">Peserta</option>
                                    <option value="Pemateri">Pemateri</option>
                                    <option value="Panitia">Panitia</option>
                                    <option value="Lainnya">Lainnya</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Tugas Tambahan -->
<div class="modal fade" id="modalTambahTugas" tabindex="-1" role="dialog" aria-labelledby="modalTambahTugasLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTambahTugasLabel">Tambah Tugas Tambahan</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('gtk.guru.tugas-tambahan.store', $guru->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="tugas_tambahan">Tugas Tambahan <span class="text-danger">*</span></label>
                        <select name="tugas_tambahan" id="tugas_tambahan" class="form-control" required onchange="toggleKelasField()">
                            <option value="">Pilih Tugas Tambahan</option>
                            <option value="Kepala Sekolah">Kepala Sekolah</option>
                            <option value="Waka Kurikulum">Waka Kurikulum</option>
                            <option value="Waka Sarpras">Waka Sarpras</option>
                            <option value="Waka Kesiswaan">Waka Kesiswaan</option>
                            <option value="Wali Kelas">Wali Kelas</option>
                            <option value="Guru Kelas">Guru Kelas</option>
                            <option value="Perpustakaan">Perpustakaan</option>
                        </select>
                    </div>
                    <div class="form-group" id="kelas_container" style="display: none;">
                        <label for="kelas">Kelas <span class="text-danger">*</span></label>
                        <input type="text" name="kelas" id="kelas" class="form-control" placeholder="Contoh: X IPA 1">
                    </div>
                    <div class="form-group">
                        <label for="tahun_ajaran_id">Tahun Ajaran <span class="text-danger">*</span></label>
                        <select name="tahun_ajaran_id" id="tahun_ajaran_id" class="form-control" required>
                            @foreach($tahunAjaranList as $ta)
                                <option value="{{ $ta->id }}" {{ $ta->aktif ? 'selected' : '' }}>
                                    {{ $ta->nama }} {{ $ta->aktif ? '(Aktif)' : '' }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('css')
<meta name="csrf-token" content="{{ csrf_token() }}">
<style>
.section-title {
    background-color: #007bff;
    color: white;
    padding: 8px 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    font-weight: bold;
    position: relative;
    display: inline-block;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.section-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, #0056b3, #17a2b8);
    border-radius: 0 0 5px 5px;
}
</style>
@stop

@section('js')
<script>
    $(document).ready(function() {
        // Setup AJAX CSRF token
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        // DataTable untuk tabel pengembangan diri
        $('#tablePengembanganDiri').DataTable({
            "paging": true,
            "lengthChange": false,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
            }
        });
        
        // DataTable untuk tabel tugas tambahan
        $('#tableTugasTambahan').DataTable({
            "paging": true,
            "lengthChange": false,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
            }
        });
        
        // Inisialisasi tampilan field kelas pada form tambah
        toggleKelasField();
    });
    
    // Fungsi untuk menampilkan/menyembunyikan field kelas pada form tambah
    function toggleKelasField() {
        var tugasTambahan = document.getElementById('tugas_tambahan').value;
        var kelasContainer = document.getElementById('kelas_container');
        var kelasInput = document.getElementById('kelas');
        
        if (tugasTambahan === 'Wali Kelas' || tugasTambahan === 'Guru Kelas') {
            kelasContainer.style.display = 'block';
            kelasInput.required = true;
        } else {
            kelasContainer.style.display = 'none';
            kelasInput.required = false;
        }
    }
    
    // Fungsi untuk menampilkan/menyembunyikan field kelas pada form edit
    function toggleKelasFieldEdit(id) {
        var tugasTambahan = document.getElementById('tugas_tambahan' + id).value;
        var kelasContainer = document.getElementById('kelas_container' + id);
        var kelasInput = document.getElementById('kelas' + id);
        
        if (tugasTambahan === 'Wali Kelas' || tugasTambahan === 'Guru Kelas') {
            kelasContainer.style.display = 'block';
            kelasInput.required = true;
        } else {
            kelasContainer.style.display = 'none';
            kelasInput.required = false;
        }
    }
</script>
@stop
























