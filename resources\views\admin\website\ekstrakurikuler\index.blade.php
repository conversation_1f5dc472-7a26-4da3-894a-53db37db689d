@extends('adminlte::page')

@section('title', 'Daftar Ekstrakurikuler')

@section('content_header')
    <h1>Daftar Ekstrakurikuler</h1>
@stop

@section('content')
<div class="card">
    <div class="card-header">
        <a href="{{ route('admin.website.ekstrakurikuler.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Tambah Ekstrakurikuler
        </a>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-check"></i> Sukses!</h5>
                {{ session('success') }}
            </div>
        @endif

        <table class="table table-bordered table-striped table-hover">
            <thead>
                <tr>
                    <th width="5%">No</th>
                    <th width="15%">Gambar</th>
                    <th width="20%">Judul</th>
                    <th>Deskripsi</th>
                    <th width="15%">Aksi</th>
                </tr>
            </thead>
            <tbody>
                @forelse($ekstrakurikuler as $key => $item)
                <tr>
                    <td class="text-center">{{ $key + 1 }}</td>
                    <td class="text-center">
                        @if($item->gambar)
                            <img src="{{ Storage::url($item->gambar) }}" 
                                 alt="{{ $item->nama }}" 
                                 class="img-thumbnail"
                                 style="max-width: 100px;">
                        @else
                            <span class="badge badge-secondary">No Image</span>
                        @endif
                    </td>
                    <td>{{ $item->nama }}</td>
                    <td>{{ Str::limit($item->deskripsi, 100) }}</td>
                    <td class="text-center">
                        <a href="{{ route('admin.website.ekstrakurikuler.edit', $item->id) }}" 
                           class="btn btn-sm btn-warning">
                           <i class="fas fa-edit"></i> Edit
                        </a>
                        <form action="{{ route('admin.website.ekstrakurikuler.destroy', $item->id) }}" 
                              method="POST" 
                              style="display: inline-block;">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="btn btn-sm btn-danger" 
                                    onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')">
                                <i class="fas fa-trash"></i> Hapus
                            </button>
                        </form>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="5" class="text-center">Tidak ada data ekstrakurikuler</td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
@stop

@section('css')
<style>
    .table th, .table td {
        vertical-align: middle;
    }
</style>
@stop

@section('js')
<script>
    $(document).ready(function() {
        // Enable DataTables
        $('.table').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
        });
    });
</script>
@stop



