<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckLaporanKerusakanAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        
        // Cek akses lihat laporan
        if ($request->routeIs('laporan-kerusakan.index') || $request->routeIs('laporan-kerusakan.show')) {
            if (!$user->hasPermissionTo('lihat-laporan-kerusakan')) {
                return redirect()->route('dashboard')
                    ->with('error', 'Anda tidak memiliki izin untuk melihat laporan kerusakan.');
            }
        }
        
        // Cek akses buat laporan
        if ($request->routeIs('laporan-kerusakan.create') || $request->routeIs('laporan-kerusakan.store')) {
            if (!$user->hasPermissionTo('buat-laporan-kerusakan')) {
                return redirect()->route('dashboard')
                    ->with('error', 'Anda tidak memiliki izin untuk membuat laporan kerusakan.');
            }
        }
        
        // Cek akses kelola laporan (update, delete, proses)
        if ($request->routeIs('laporan-kerusakan.edit') || 
            $request->routeIs('laporan-kerusakan.update') || 
            $request->routeIs('laporan-kerusakan.destroy') ||
            $request->routeIs('laporan-kerusakan.proses') ||
            $request->routeIs('laporan-kerusakan.selesai')) {
            if (!$user->hasPermissionTo('kelola-laporan-kerusakan')) {
                return redirect()->route('dashboard')
                    ->with('error', 'Anda tidak memiliki izin untuk mengelola laporan kerusakan.');
            }
        }

        return $next($request);
    }
}