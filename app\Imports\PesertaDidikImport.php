<?php

namespace App\Imports;

use App\Models\PesertaDidik;
use App\Models\RiwayatKelas;
use App\Models\TahunAjaran;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterImport;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PesertaDidikImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, WithEvents
{
    use SkipsErrors;
    
    // Menyimpan ID user yang melakukan impor
    protected $userId;
    
    // Menyimpan daftar siswa yang diimpor (simpan ID, bukan objek)
    protected $importedStudents = [];
    
    public function __construct($userId = null)
    {
        // Simpan ID user yang melakukan impor
        $this->userId = $userId ?? auth()->id();
        Log::info("PesertaDidikImport dibuat dengan user ID: {$this->userId}");
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        try {
            // Tangani konversi tanggal dengan aman
            $tanggal_lahir = null;
            if (!empty($row['tanggal_lahir'])) {
                try {
                    // Coba konversi jika nilai numerik
                    if (is_numeric($row['tanggal_lahir'])) {
                        $tanggal_lahir = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row['tanggal_lahir']);
                    } else {
                        // Jika string, coba parse sebagai tanggal (format Y-m-d)
                        $tanggal_lahir = \Carbon\Carbon::parse($row['tanggal_lahir']);
                    }
                } catch (\Exception $e) {
                    Log::warning("Gagal mengkonversi tanggal lahir: " . $e->getMessage());
                }
            }

            // Buat siswa baru
            $siswa = new PesertaDidik([
                'nis' => $row['nis'],
                'nisn' => $row['nisn'],
                'nik' => $row['nik'] ?? null,
                'nama' => $row['nama'],
                'jenis_kelamin' => $row['jenis_kelamin'],
                'tempat_lahir' => $row['tempat_lahir'] ?? null,
                'tanggal_lahir' => $tanggal_lahir,
                'agama' => $row['agama'] ?? null,
                'alamat' => $row['alamat'] ?? null,
                'rt' => $row['rt'] ?? null,
                'rw' => $row['rw'] ?? null,
                'dusun' => $row['dusun'] ?? null,
                'kelurahan' => $row['kelurahan'] ?? null,
                'kecamatan' => $row['kecamatan'] ?? null,
                'kode_pos' => $row['kode_pos'] ?? null,
                'kelas_id' => $row['kelas_id'] ?? null,
                'tingkat' => $row['tingkat'] ?? null,
                'nama_program' => $row['nama_program'] ?? null,
                'no_reg' => $row['no_reg'] ?? null,
                'npsn' => $row['npsn'] ?? null,
                'sekolah_asal' => $row['sekolah_asal'] ?? null,
                'seri_ijasah' => $row['seri_ijasah'] ?? null,
                'seri_skhun' => $row['seri_skhun'] ?? null,
                'no_unas' => $row['no_unas'] ?? null,
                'jenis_tinggal' => $row['jenis_tinggal'] ?? null,
                'transportasi' => $row['transportasi'] ?? null,
                'no_telp' => $row['no_telp'] ?? null,
                'no_handphone' => $row['no_handphone'] ?? null,
                'email' => $row['email'] ?? null,
                'unit_id' => $row['unit_id'] ?? null,

                // Data PIP
                'no_kks' => $row['no_kks'] ?? null,
                'status_kps' => $row['status_kps'] ?? null,
                'no_ksp' => $row['no_ksp'] ?? null,
                'status_pip' => $row['status_pip'] ?? null,
                'usulan_pip' => $row['usulan_pip'] ?? null,

                // Data kesehatan
                'tinggi_badan' => $row['tinggi_badan'] ?? null,
                'berat_badan' => $row['berat_badan'] ?? null,
                'jarak_rumah' => $row['jarak_rumah'] ?? null,
                'waktu_tempuh' => $row['waktu_tempuh'] ?? null,
                'jumlah_saudara' => $row['jumlah_saudara'] ?? null,
                
                
                // Data ayah - perbaiki pemetaan NIK_ayah/nik_ayah
                'nama_ayah' => $row['nama_ayah'] ?? null,
                'NIK_ayah' => isset($row['NIK_ayah']) ? $row['NIK_ayah'] : (isset($row['nik_ayah']) ? $row['nik_ayah'] : null),
                'tahun_lahir_ayah' => $row['tahun_lahir_ayah'] ?? null,
                'pendidikan_ayah' => $row['pendidikan_ayah'] ?? null,
                'pekerjaan_ayah' => $row['pekerjaan_ayah'] ?? null,
                'penghasilan_ayah' => $row['penghasilan_ayah'] ?? null,
                'kebutuhan_khusus_ayah' => $row['kebutuhan_khusus_ayah'] ?? null,
                
                // Data ibu - perbaiki pemetaan NIK_ibu/nik_ibu
                'nama_ibu' => $row['nama_ibu'] ?? null,
                'NIK_ibu' => $row['NIK_ibu'] ?? $row['nik_ibu'] ?? null,
                'tahun_lahir_ibu' => $row['tahun_lahir_ibu'] ?? null,
                'pendidikan_ibu' => $row['pendidikan_ibu'] ?? null,
                'pekerjaan_ibu' => $row['pekerjaan_ibu'] ?? null,
                'penghasilan_ibu' => $row['penghasilan_ibu'] ?? null,
                'kebutuhan_khusus_ibu' => $row['kebutuhan_khusus_ibu'] ?? null,
                
                // Data wali - perbaiki pemetaan NIK_wali/nik_wali
                'nama_wali' => $row['nama_wali'] ?? null,
                'NIK_wali' => isset($row['NIK_wali']) ? $row['NIK_wali'] : (isset($row['nik_wali']) ? $row['nik_wali'] : null),
                'tahun_lahir_wali' => $row['tahun_lahir_wali'] ?? null,
                'pendidikan_wali' => $row['pendidikan_wali'] ?? null,
                'pekerjaan_wali' => $row['pekerjaan_wali'] ?? null,
                'penghasilan_wali' => $row['penghasilan_wali'] ?? null,
                // Tambahkan field lainnya sesuai kebutuhan
            ]);
            
            // Simpan data kelas_id untuk diproses nanti
            // Kita hanya menyimpan kelas_id, bukan objek siswa
            if (!empty($row['kelas_id'])) {
                // Simpan data baris untuk diproses nanti
                $this->importedStudents[] = [
                    'row_data' => $row,
                    'kelas_id' => $row['kelas_id']
                ];
            }
            
            Log::info("Model siswa dibuat: {$row['nama']} dengan kelas_id: " . (isset($row['kelas_id']) ? $row['kelas_id'] : 'null'));
            return $siswa;
        } catch (\Exception $e) {
            Log::error("Error saat membuat model siswa: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            //'nis' => 'required',
            'nisn' => 'required',
            'nama' => 'required',
            'nik' => 'required',
            'jenis_kelamin' => 'required|in:L,P',
            'tingkat' => 'required',
           // 'nik' => 'required|unique:peserta_didik,nik', // Tambahkan validasi unique
        ];
    }
    
    /**
     * Register events untuk dijalankan setelah impor selesai
     */
    public function registerEvents(): array
    {
        return [
            AfterImport::class => function(AfterImport $event) {
                Log::info("AfterImport event dipanggil. Memproses " . count($this->importedStudents) . " siswa");
                $this->processClassHistory();
            },
        ];
    }
    
    /**
     * Proses pencatatan riwayat kelas untuk semua siswa yang diimpor
     */
    protected function processClassHistory()
    {
        // Ambil tahun ajaran aktif
        $tahunAjaran = TahunAjaran::where('aktif', true)->first();
        $tahunAjaranNama = $tahunAjaran ? $tahunAjaran->nama : date('Y').'/'.((int)date('Y')+1);
        
        Log::info("Memproses riwayat kelas dengan tahun ajaran: {$tahunAjaranNama}");
        
        // Gunakan transaksi database
        DB::beginTransaction();
        
        try {
            foreach ($this->importedStudents as $data) {
                $rowData = $data['row_data'];
                $kelasId = $data['kelas_id'];
                
                // Cari siswa berdasarkan NIK (yang baru saja diimpor)
                $siswa = null;
                if (!empty($rowData['nik'])) {
                    $siswa = PesertaDidik::where('nik', $rowData['nik'])
                                        ->orderBy('created_at', 'desc')
                                        ->first();
                    
                    if ($siswa && $kelasId) {
                        // Catat riwayat kelas
                        RiwayatKelas::create([
                            'siswa_id' => $siswa->id,
                            'kelas_lama_id' => null, // Siswa baru tidak memiliki kelas lama
                            'kelas_baru_id' => $kelasId,
                            'tahun_ajaran' => $tahunAjaranNama,
                            'jenis_perpindahan' => 'pindah_kelas',
                            'tanggal_pindah' => now(),
                            'alasan' => 'Pendaftaran siswa baru melalui impor',
                            'created_by' => $this->userId
                        ]);
                        
                        Log::info("Riwayat kelas dicatat untuk siswa ID: {$siswa->id}, Nama: {$siswa->nama}, NIK: {$siswa->nik}, Kelas ID: {$kelasId}");
                    } else {
                        if (!$siswa) {
                            Log::warning("Tidak dapat menemukan siswa dengan NIK: {$rowData['nik']}");
                        } else {
                            Log::warning("Kelas ID kosong untuk siswa: {$siswa->nama} (ID: {$siswa->id})");
                        }
                    }
                } else {
                    // Fallback ke pencarian berdasarkan NIS dan NISN jika NIK kosong
                    $siswa = PesertaDidik::where('nis', $rowData['nis'])
                                        ->where('nisn', $rowData['nisn'])
                                        ->where('unit_id', $rowData['unit_id'] ?? null)
                                        ->orderBy('created_at', 'desc')
                                        ->first();
                    
                    if ($siswa && $kelasId) {
                        // Catat riwayat kelas
                        RiwayatKelas::create([
                            'siswa_id' => $siswa->id,
                            'kelas_lama_id' => null, // Siswa baru tidak memiliki kelas lama
                            'kelas_baru_id' => $kelasId,
                            'tahun_ajaran' => $tahunAjaranNama,
                            'jenis_perpindahan' => 'pindah_kelas',
                            'tanggal_pindah' => now(),
                            'alasan' => 'Pendaftaran siswa baru melalui impor',
                            'created_by' => $this->userId
                        ]);
                        
                        Log::info("Riwayat kelas dicatat untuk siswa ID: {$siswa->id}, Nama: {$siswa->nama}, NIS: {$siswa->nis}, NISN: {$siswa->nisn}, Kelas ID: {$kelasId}");
                    } else {
                        Log::warning("Tidak dapat menemukan siswa dengan NIS: {$rowData['nis']} dan NISN: {$rowData['nisn']} atau kelas_id kosong");
                    }
                }
            }
            
            DB::commit();
            Log::info("Semua riwayat kelas berhasil dicatat");
        } catch (\Exception $e) {
            DB::rollback();
            Log::error("Error saat mencatat riwayat kelas: " . $e->getMessage());
            Log::error("Stack trace: " . $e->getTraceAsString());
        }
    }
}















