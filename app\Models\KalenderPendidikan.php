<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class KalenderPendidikan extends Model
{
    use HasFactory;
    
    protected $table = 'kalender_pendidikan';
    
    protected $fillable = [
        'judul',
        'tanggal_mulai',
        'tanggal_selesai',
        'deskripsi',
        'warna',
        'created_by'
    ];
    
    protected $casts = [
        'tanggal_mulai' => 'datetime',
        'tanggal_selesai' => 'datetime',
    ];
    
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}