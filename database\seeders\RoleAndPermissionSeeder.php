<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleAndPermissionSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions if they don't exist
        $permissions = [
            // GTK
            'view-gtk', 'manage-gtk',
            // Peserta Didik
            'view-peserta-didik', 'view-peserta-didik-aktif', 'manage-peserta-didik',
            // ADM
            'view-adm', 'manage-adm',
            'manage-adm-waka', 'manage-adm-guru', 'approve-adm-kepsek', 'manage-adm-kepsek',
            // Rombel
            'view-rombel', 'manage-rombel',
            // Sarpras
            'view-sarpras', 'manage-sarpras',
            // Website
            'view-website', 'manage-website',
            'manage-website-prestasi', 'manage-website-event',
            'manage-website-ekstrakurikuler', 'manage-website-fasilitas',
            // E-Learning
            'view-elearning', 'manage-elearning',
            // J<PERSON><PERSON>
            'view-jadwal', 'manage-jadwal', 'view-jadwal-pribadi', 
            // Jadwal Mengajar
            'view-jadwal-mengajar',
            // BK
            'view-bk', 'manage-bk',
            // Nilai
            'view-nilai', 'manage-nilai',
            // Absensi
            'view-absensi', 'manage-absensi',
            // SPP
            'view-spp', 'manage-spp',
            'view-adm-waka',
            'upload-adm-waka',
            'manage-adm-waka',
            // Jurnal permissions
            'view-jurnal',
            'manage-jurnal',
            'approve-jurnal',
            //penaturan
            'view-pengaturan', 'manage-pengaturan',
            // User Management
            'manage-users',
            'view-users',
            'create-users',
            'edit-users',
            'delete-users',
            'reset-user-password',
            // Role Management
            'view-roles',
            'create-roles',
            'edit-roles',
            'delete-roles',
            // Permission Management
            'manage-permissions',
            'view-permissions',
            'create-permissions',
            'edit-permissions',
            'delete-permissions',
        ];

        // Create permissions if they don't exist
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to roles
        $roles = [
            'Administrator' => $permissions, // Administrator sudah mendapatkan semua permissions termasuk manage-permissions
            'Yayasan' => $permissions,
            'Kepala Sekolah' => [
                'manage-gtk', 
                'view-peserta-didik', 
                'view-peserta-didik-aktif', 
                'manage-peserta-didik', // hapus koma ganda disini
                'manage-adm',
                'manage-rombel', 
                'manage-sarpras', 
                'manage-website',
                'manage-elearning', 
                'manage-jadwal',
                'view-bk', 
                'view-website', 
                'view-nilai', 
                'view-absensi',
                'view-adm-waka', 
                'upload-adm-waka', 
                'manage-adm-waka',
                'manage-adm-kepsek',
                // Tambahkan permissions jurnal
                'view-jurnal',
                'manage-jurnal',
                'approve-jurnal',
            ],
            'Waka Kurikulum' => [
                'manage-gtk', 'view-peserta-didik', 'view-peserta-didik-aktif', 'manage-peserta-didik','manage-rombel', 'manage-elearning',
                'manage-jadwal', 'manage-absensi', 'manage-adm-waka',
                'manage-website-prestasi', 'manage-website-event',
                'manage-website-ekstrakurikuler',
                'view-bk', 'view-nilai',
                'view-adm-waka', 'upload-adm-waka',
                'view-adm', // Add this permission
                // Tambahkan permissions jurnal
                'view-jurnal',
                'manage-jurnal',
                'approve-jurnal',
            ],
            'Waka Kesiswaan' => [
                'manage-peserta-didik', 'manage-rombel', 'manage-adm-waka',
                'manage-website-prestasi', 'manage-website-event',
                'manage-website-ekstrakurikuler',
                'view-bk', 'view-absensi', 'view-jadwal',
                'view-adm-waka', 'upload-adm-waka',
                'view-adm', // Add this permission
            ],
            'Waka Sarpras' => [
                'manage-sarpras', 'manage-adm-waka',
                'manage-website-prestasi', 'manage-website-event',
                'manage-website-ekstrakurikuler', 'manage-website-fasilitas',
                'view-adm-waka', 'upload-adm-waka',
                'view-adm', // Add this permission
            ],
            'Guru' => [
                'manage-absensi', 
                'manage-nilai', 
                'manage-adm-guru',
                'view-jadwal-pribadi',
                'view-jadwal-mengajar',
                'view-adm', // Menambahkan permission view-adm
                'view-adm-guru',
                'upload-adm-guru',
                // Tambahkan permissions jurnal
                'view-jurnal',
                'manage-jurnal', // bisa membuat dan mengelola jurnal
            ],
            'Pengawas' => [
                'view-gtk', 'view-peserta-didik', 'view-adm',
                'view-rombel', 'view-sarpras', 'view-jadwal',
                'view-bk', 'view-nilai', 'view-absensi',
                // Tambahkan permission view jurnal saja
                'view-jurnal',
                'approve-adm-kepsek',
                'manage-jurnal',
                'view-adm-kepsek',
            ],
            'Bimbingan Konseling' => [
                'manage-bk',
                // Tambahkan permissions jurnal
                'view-jurnal',
                'manage-jurnal',
            ],
            'Tata Usaha' => [
                'manage-spp',
                // Tambahkan permission view jurnal saja
                'view-jurnal',
                'manage-jurnal',
            ],
            'Pustakawan' => [
                // Tambahkan permission view jurnal saja
                'view-jurnal',
                'manage-jurnal',
            ]
        ];

        foreach ($roles as $roleName => $permissions) {
            $role = Role::firstOrCreate(['name' => $roleName]);
            $role->syncPermissions($permissions);
        }
    }
}




