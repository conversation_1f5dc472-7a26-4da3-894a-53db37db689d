<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Unit;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class EventController extends Controller
{
    // Hapus method __construct()
    
    public function index()
    {
        $user = auth()->user();
        $query = Event::query();
        
        if (!$user->hasRole('Administrator')) {
            $query->where('unit_id', $user->unit_id);
        }
        
        $events = $query->latest()->paginate(10);
        $units = $user->hasRole('Administrator') ? Unit::all() : null;
        
        return view('admin.website.event.index', compact('events', 'units'));
    }

    public function create()
    {
        // Ubah dari website.event.create menjadi admin.website.event.create
        $user = auth()->user();
        $units = $user->hasRole('Administrator') ? Unit::all() : null;
        return view('admin.website.event.create', compact('units'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'judul' => 'required|max:255',
            'deskripsi' => 'required',
            'tanggal' => 'required|date',
            'lokasi' => 'required|string',
            'jenis_media' => 'required|in:gambar,youtube',
            'gambar' => $request->jenis_media == 'gambar' ? 'required|image|mimes:jpeg,png,jpg|max:2048' : 'nullable',
            'youtube_url' => $request->jenis_media == 'youtube' ? 'required|url' : 'nullable',
        ]);

        // Debugging - tambahkan ini sementara untuk melihat nilai yang dikirim
        // dd($request->all());

        $data = $request->all(); // Gunakan all() daripada except() untuk memastikan semua data terambil
        $data['slug'] = Str::slug($request->judul);
        
        // Mengambil unit_id dari user yang login
        $user = auth()->user();
        if (!$user->hasRole('Administrator')) {
            $data['unit_id'] = $user->unit_id;
        } else {
            // Jika admin, unit_id diambil dari form
            $request->validate(['unit_id' => 'required|exists:units,id']);
            $data['unit_id'] = $request->unit_id;
        }
        
        // Jika jenis media adalah gambar, simpan gambar dan kosongkan youtube_url
        if ($request->jenis_media == 'gambar') {
            if ($request->hasFile('gambar')) {
                $gambar = $request->file('gambar');
                $nama_gambar = time() . '.' . $gambar->getClientOriginalExtension();
                $gambar->storeAs('events', $nama_gambar, 'public');
                $data['gambar'] = $nama_gambar;
            }
            $data['youtube_url'] = null;
        } 
        // Jika jenis media adalah youtube, kosongkan gambar
        else {
            $data['gambar'] = null;
            $data['youtube_url'] = $request->youtube_url; // Tambahkan baris ini
        }

        Event::create($data);

        return redirect()->route('admin.website.event.index')
            ->with('success', 'Event berhasil ditambahkan');
    }

    public function edit($id)
    {
        $event = Event::findOrFail($id);
        return view('admin.website.event.edit', compact('event'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'judul' => 'required|max:255',
            'deskripsi' => 'required',
            'tanggal' => 'required|date',
            'lokasi' => 'required|string',
            'jenis_media' => 'required|in:gambar,youtube',
            'gambar' => $request->jenis_media == 'gambar' ? 'nullable|image|mimes:jpeg,png,jpg|max:2048' : 'nullable',
            'youtube_url' => $request->jenis_media == 'youtube' ? 'required|url' : 'nullable',
        ]);

        $event = Event::findOrFail($id);
        
        // Verifikasi bahwa pengguna memiliki akses ke event ini
        $user = auth()->user();
        if (!$user->hasRole('Administrator') && $event->unit_id != $user->unit_id) {
            return redirect()->route('admin.website.event.index')
                ->with('error', 'Anda tidak memiliki akses untuk mengedit event ini');
        }
        
        // Persiapkan data untuk update
        $event->judul = $request->judul;
        $event->slug = Str::slug($request->judul);
        $event->deskripsi = $request->deskripsi;
        $event->tanggal = $request->tanggal;
        $event->lokasi = $request->lokasi;
        
        // Update unit_id jika pengguna adalah admin
        if ($user->hasRole('Administrator') && $request->has('unit_id')) {
            $request->validate(['unit_id' => 'required|exists:units,id']);
            $event->unit_id = $request->unit_id;
        }
        
        // Jika jenis media adalah gambar
        if ($request->jenis_media == 'gambar') {
            if ($request->hasFile('gambar')) {
                // Hapus gambar lama jika ada
                if ($event->gambar) {
                    Storage::disk('public')->delete('events/' . $event->gambar);
                }
                
                // Upload gambar baru
                $gambar = $request->file('gambar');
                $nama_gambar = time() . '.' . $gambar->getClientOriginalExtension();
                $gambar->storeAs('events', $nama_gambar, 'public');
                $event->gambar = $nama_gambar;
            }
            $event->youtube_url = null;
        } 
        // Jika jenis media adalah youtube
        else {
            // Hapus gambar lama jika ada
            if ($event->gambar) {
                Storage::disk('public')->delete('events/' . $event->gambar);
                $event->gambar = null;
            }
            $event->youtube_url = $request->youtube_url;
        }

        $event->save();

        return redirect()->route('admin.website.event.index')
            ->with('success', 'Event berhasil diperbarui');
    }

    public function destroy($id)
    {
        $event = Event::findOrFail($id);
        
        if ($event->gambar) {
            Storage::disk('public')->delete('events/' . $event->gambar);
        }
        
        $event->delete();

        return redirect()->route('admin.website.event.index')
            ->with('success', 'Event berhasil dihapus');
    }
}








