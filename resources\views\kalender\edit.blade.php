@extends('layouts.admin')

@section('title', 'Edit Event Kalender Pendidikan')
@section('page_title', 'Edit Event Kalender Pendidikan')

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">Form Edit Event</h3>
            </div>
            <form action="{{ route('kalender.update', $event->id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="card-body">
                    <div class="form-group">
                        <label for="judul">Judul Event</label>
                        <input type="text" class="form-control @error('judul') is-invalid @enderror" 
                               id="judul" name="judul" value="{{ old('judul', $event->judul) }}" required>
                        @error('judul')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="tanggal_mulai"><PERSON><PERSON></label>
                                <div class="input-group date" id="tanggal_mulai_datepicker" data-target-input="nearest">
                                    <input type="date" class="form-control @error('tanggal_mulai') is-invalid @enderror" 
                                           id="tanggal_mulai" name="tanggal_mulai" 
                                           value="{{ old('tanggal_mulai', $event->tanggal_mulai->format('Y-m-d')) }}" required>
                                    @error('tanggal_mulai')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="tanggal_selesai">Tanggal Selesai</label>
                                <div class="input-group date" id="tanggal_selesai_datepicker" data-target-input="nearest">
                                    <input type="date" class="form-control @error('tanggal_selesai') is-invalid @enderror" 
                                           id="tanggal_selesai" name="tanggal_selesai" 
                                           value="{{ old('tanggal_selesai', $event->tanggal_selesai->format('Y-m-d')) }}" required>
                                    @error('tanggal_selesai')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="deskripsi">Deskripsi</label>
                        <textarea class="form-control @error('deskripsi') is-invalid @enderror" 
                                  id="deskripsi" name="deskripsi" rows="3">{{ old('deskripsi', $event->deskripsi) }}</textarea>
                        @error('deskripsi')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="warna">Warna</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-palette"></i></span>
                            </div>
                            <select class="form-control @error('warna') is-invalid @enderror" id="warna" name="warna">
                                <option value="#007bff" {{ old('warna', $event->warna) == '#007bff' ? 'selected' : '' }}>Biru</option>
                                <option value="#28a745" {{ old('warna', $event->warna) == '#28a745' ? 'selected' : '' }}>Hijau</option>
                                <option value="#dc3545" {{ old('warna', $event->warna) == '#dc3545' ? 'selected' : '' }}>Merah</option>
                                <option value="#ffc107" {{ old('warna', $event->warna) == '#ffc107' ? 'selected' : '' }}>Kuning</option>
                                <option value="#6f42c1" {{ old('warna', $event->warna) == '#6f42c1' ? 'selected' : '' }}>Ungu</option>
                                <option value="#fd7e14" {{ old('warna', $event->warna) == '#fd7e14' ? 'selected' : '' }}>Oranye</option>
                            </select>
                            @error('warna')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> Simpan Perubahan
                    </button>
                    <a href="{{ route('dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left mr-1"></i> Kembali
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('css')
<style>
    /* Styling untuk preview warna */
    #warna option[value="#007bff"] {
        background-color: #007bff;
        color: white;
    }
    #warna option[value="#28a745"] {
        background-color: #28a745;
        color: white;
    }
    #warna option[value="#dc3545"] {
        background-color: #dc3545;
        color: white;
    }
    #warna option[value="#ffc107"] {
        background-color: #ffc107;
        color: black;
    }
    #warna option[value="#6f42c1"] {
        background-color: #6f42c1;
        color: white;
    }
    #warna option[value="#fd7e14"] {
        background-color: #fd7e14;
        color: white;
    }
</style>
@endsection

@section('js')
<script>
    $(function() {
        // Menampilkan preview warna saat pilihan berubah
        $('#warna').on('change', function() {
            $(this).css('background-color', $(this).val());
            $(this).css('color', getContrastYIQ($(this).val()));
        });
        
        // Set warna awal
        $('#warna').css('background-color', $('#warna').val());
        $('#warna').css('color', getContrastYIQ($('#warna').val()));
        
        // Fungsi untuk menentukan warna teks berdasarkan kontras
        function getContrastYIQ(hexcolor) {
            hexcolor = hexcolor.replace('#', '');
            var r = parseInt(hexcolor.substr(0, 2), 16);
            var g = parseInt(hexcolor.substr(2, 2), 16);
            var b = parseInt(hexcolor.substr(4, 2), 16);
            var yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
            return (yiq >= 128) ? 'black' : 'white';
        }
        
        // Validasi tanggal
        $('#tanggal_selesai').on('change', function() {
            var tanggalMulai = new Date($('#tanggal_mulai').val());
            var tanggalSelesai = new Date($(this).val());
            
            if (tanggalSelesai < tanggalMulai) {
                alert('Tanggal selesai tidak boleh lebih awal dari tanggal mulai');
                $(this).val($('#tanggal_mulai').val());
            }
        });
        
        $('#tanggal_mulai').on('change', function() {
            var tanggalMulai = new Date($(this).val());
            var tanggalSelesai = new Date($('#tanggal_selesai').val());
            
            if (tanggalSelesai < tanggalMulai) {
                $('#tanggal_selesai').val($(this).val());
            }
        });
    });
</script>
@endsection
