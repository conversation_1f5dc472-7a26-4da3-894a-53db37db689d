<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DetailJadwal extends Model
{
    protected $table = 'detail_jadwal';
    
    protected $fillable = [
        'jadwal_pelajaran_id',
        'mata_pelajaran_id',
        'hari',
        'waktu_mulai',
        'waktu_selesai',
        'is_istirahat',
        'keterangan'
    ];
    
    public function jadwalPelajaran()
    {
        return $this->belongsTo(JadwalPelajaran::class, 'jadwal_pelajaran_id');
    }
    
    public function mataPelajaran()
    {
        return $this->belongsTo(MataPelajaran::class, 'mata_pelajaran_id');
    }
}


