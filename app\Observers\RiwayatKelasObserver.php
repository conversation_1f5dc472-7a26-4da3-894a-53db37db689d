<?php

namespace App\Observers;

use App\Models\RiwayatKelas;
use App\Models\PesertaDidik;

class RiwayatKelasObserver
{
    /**
     * Handle the RiwayatKelas "creating" event.
     */
    public function creating(RiwayatKelas $riwayatKelas): void
    {
        $this->setNamaPd($riwayatKelas);
    }

    /**
     * Handle the RiwayatKelas "updating" event.
     */
    public function updating(RiwayatKelas $riwayatKelas): void
    {
        $this->setNamaPd($riwayatKelas);
    }

    /**
     * Set nama_pd based on siswa_id
     */
    private function setNamaPd(RiwayatKelas $riwayatKelas): void
    {
        if ($riwayatKelas->siswa_id) {
            $siswa = PesertaDidik::find($riwayatKelas->siswa_id);
            if ($siswa) {
                $riwayatKelas->nama_pd = $siswa->nama;
            }
        }
    }
}