<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Simpan data tanggal_lapor yang sudah ada
        $laporanData = DB::table('laporan_kerusakan')
            ->select('id', 'tanggal_lapor', 'created_at')
            ->get();
            
        // Ubah definisi kolom tanggal_lapor
        Schema::table('laporan_kerusakan', function (Blueprint $table) {
            $table->timestamp('tanggal_lapor')->nullable()->change();
        });
        
        // Kembalikan data tanggal_lapor yang sudah ada
        foreach ($laporanData as $laporan) {
            DB::table('laporan_kerusakan')
                ->where('id', $laporan->id)
                ->update([
                    'tanggal_lapor' => $laporan->tanggal_lapor ?? $laporan->created_at
                ]);
        }
    }

    public function down(): void
    {
        // Tidak perlu rollback karena ini perbaikan
    }
};