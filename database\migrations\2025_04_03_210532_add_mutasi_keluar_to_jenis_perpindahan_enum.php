<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Mengubah tipe enum dengan menambahkan nilai 'mutasi_keluar'
        DB::statement("ALTER TABLE riwayat_kelas MODIFY COLUMN jenis_perpindahan ENUM('pindah_kelas', 'kenaikan_kelas', 'kelulusan', 'mutasi_keluar')");
    }

    public function down()
    {
        // Mengembalikan ke nilai enum sebelumnya
        DB::statement("ALTER TABLE riwayat_kelas MODIFY COLUMN jenis_perpindahan ENUM('pindah_kelas', 'kenaikan_kelas', 'kelulusan')");
    }
};