@extends('adminlte::page')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
<div class="container">
    <div class="alert alert-danger">
        <h4><i class="icon fas fa-ban"></i> <PERSON><PERSON><PERSON></h4>
        <p>Anda tidak memiliki hak akses untuk halaman ini.</p>
        <a href="{{ route('dashboard') }}" class="btn btn-primary">
            <i class="fas fa-home"></i> Kembali ke Dashboard
        </a>
    </div>

    @if(config('app.debug'))
    <div class="card card-info">
        <div class="card-header">
            <h3 class="card-title">Debug Information</h3>
        </div>
        <div class="card-body">
            <dl class="row">
                <dt class="col-sm-3">User ID</dt>
                <dd class="col-sm-9">{{ auth()->id() }}</dd>

                <dt class="col-sm-3">Name</dt>
                <dd class="col-sm-9">{{ auth()->user()->name ?? 'Not authenticated' }}</dd>

                <dt class="col-sm-3">Role</dt>
                <dd class="col-sm-9">{{ auth()->user()->role ?? 'No role' }}</dd>

                <dt class="col-sm-3">URL</dt>
                <dd class="col-sm-9">{{ request()->url() }}</dd>

                <dt class="col-sm-3">Method</dt>
                <dd class="col-sm-9">{{ request()->method() }}</dd>
            </dl>

            @if(isset($debug_info))
            <h6>Exception Info:</h6>
            <pre>{{ print_r($debug_info, true) }}</pre>
            @endif

            @if(auth()->check())
            <h6>Permissions:</h6>
            <ul class="list-group">
                @foreach(auth()->user()->getAllPermissions() as $permission)
                <li class="list-group-item">{{ $permission->name }}</li>
                @endforeach
            </ul>
            @endif
        </div>
    </div>
    @endif
</div>
@stop

@section('css')
<style>
    .alert {
        margin-top: 20px;
    }
    pre {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
    }
</style>
@stop
