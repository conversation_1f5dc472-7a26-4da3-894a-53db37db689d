<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('penilaian_proyeks', function (Blueprint $table) {
            $table->id();
            $table->string('nama_proyek');
            $table->text('deskripsi_proyek');
            $table->foreignId('kelas_id')->constrained('kelas')->onDelete('cascade');
            $table->date('tanggal_mulai');
            $table->date('tanggal_selesai');
            $table->enum('dimensi_penilaian', [
                'beriman_bertakwa_berakhlak_mulia',
                'berkebinekaan_global',
                'bergotong_royong',
                'mandiri',
                'bernalar_kritis',
                'kreatif'
            ]); // Dimensi Profil Pelajar Pancasila
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });

        // Tabel pivot untuk nilai proyek per siswa
        Schema::create('penilaian_proyek_siswa', function (Blueprint $table) {
            $table->id();
            $table->foreignId('penilaian_proyek_id')->constrained('penilaian_proyeks')->onDelete('cascade');
            $table->foreignId('siswa_id')->constrained('peserta_didiks')->onDelete('cascade');
            $table->enum('nilai', ['BP', 'MB', 'BSH', 'SB']); // Skala capaian kompetensi
            $table->text('deskripsi')->nullable();
            $table->text('refleksi_siswa')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('penilaian_proyek_siswa');
        Schema::dropIfExists('penilaian_proyeks');
    }
};