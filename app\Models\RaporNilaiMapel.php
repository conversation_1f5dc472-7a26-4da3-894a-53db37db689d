<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RaporNilaiMapel extends Model
{
    use HasFactory;

    protected $table = 'rapor_nilai_mapels';

    protected $fillable = [
        'rapor_siswa_id',
        'mata_pelajaran_id',
        'nilai_pengetahuan',
        'nilai_keterampilan',
        'deskripsi_pengetahuan',
        'deskripsi_keterampilan',
    ];

    public function raporSiswa()
    {
        return $this->belongsTo(RaporSiswa::class);
    }

    public function mataPelajaran()
    {
        return $this->belongsTo(MataPelajaran::class);
    }
}