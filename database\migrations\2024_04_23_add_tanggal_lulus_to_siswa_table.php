<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('peserta_didik', function (Blueprint $table) {
            // Tambahkan kolom tanggal_lulus jika belum ada
            if (!Schema::hasColumn('peserta_didik', 'tanggal_lulus')) {
                $table->date('tanggal_lulus')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('peserta_didik', function (Blueprint $table) {
            $table->dropColumn('tanggal_lulus');
        });
    }
};