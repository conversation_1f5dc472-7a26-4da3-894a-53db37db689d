<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Kelas;
use Illuminate\Support\Facades\DB;

class UpdateKelasSeeder extends Seeder
{
    public function run()
    {
        // Update kelas records with IDs 5, 6, 7, 8, 9
        // Set unit_id to 1 and gedung_id to 1
        DB::table('kelas')
            ->whereIn('id', [5, 6, 7, 8, 9])
            ->update([
                'unit_id' => 1,
                'gedung_id' => 1
            ]);
            
        $this->command->info('Updated unit_id and gedung_id for kelas records with IDs 5, 6, 7, 8, 9.');
    }
}