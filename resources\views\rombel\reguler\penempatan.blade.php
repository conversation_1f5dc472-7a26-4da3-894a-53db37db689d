@extends('adminlte::page')

@section('title', 'Penempatan Siswa')

@section('content_header')
    <h1>Penempatan Siswa</h1>
@stop

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Penempatan Siswa Tahun Ajaran {{ $tahunAjaran->nama }}</h3>
                <div class="float-right">
                    <a href="{{ route('rombel.reguler.pindah-kelas') }}" class="btn btn-warning">
                        <i class="fas fa-exchange-alt"></i> Pindah Kelas
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                
                @if(session('error'))
                    <div class="alert alert-danger">{{ session('error') }}</div>
                @endif

                <!-- Filter Unit -->
                @if(auth()->user()->hasRole('Administrator'))
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Filter Berdasarkan Unit:</label>
                            <select id="filter-unit" class="form-control select2">
                                <option value="">Semua Unit</option>
                                @foreach($units as $unit)
                                    <option value="{{ $unit->nama_unit }}">{{ $unit->nama_unit }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Penempatan Individu -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary">
                            <h3 class="card-title">Penempatan Individu</h3>
                        </div>
                        <div class="card-body">
                            @if($siswa->isEmpty())
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> Tidak ada siswa yang tersedia untuk ditempatkan.
                                </div>
                            @else
                                <form action="{{ route('rombel.reguler.penempatan.individu') }}" method="POST">
                                    @csrf
                                    <div class="form-group">
                                        <label>Pilih Siswa</label>
                                        <select name="siswa_id" class="form-control select2 siswa-select" required>
                                            <option value="">-- Pilih Siswa --</option>
                                            @foreach($siswa as $s)
                                                <option value="{{ $s->id }}" data-unit="{{ $s->tingkat }}">
                                                    {{ $s->nama }} ({{ $s->nis }}) - {{ $s->tingkat }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>Pilih Kelas</label>
                                        <select name="kelas_id" class="form-control select2" required>
                                            <option value="">-- Pilih Kelas --</option>
                                            @foreach($kelas as $k)
                                                <option value="{{ $k->id }}">{{ $k->nama }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Tempatkan Siswa</button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
                
                <!-- Penempatan Massal -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success">
                            <h3 class="card-title">Penempatan Massal</h3>
                        </div>
                        <div class="card-body">
                            @if($siswa->isEmpty())
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> Tidak ada siswa yang tersedia untuk ditempatkan.
                                </div>
                            @else
                                <form action="{{ route('rombel.reguler.penempatan.kelas') }}" method="POST">
                                    @csrf
                                    <div class="form-group">
                                        <label>Pilih Siswa (multiple)</label>
                                        <select name="siswa_ids[]" class="form-control select2 siswa-select" multiple required>
                                            @foreach($siswa as $s)
                                                <option value="{{ $s->id }}" data-unit="{{ $s->tingkat }}">
                                                    {{ $s->nama }} ({{ $s->nis }}) - {{ $s->tingkat }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>Pilih Kelas</label>
                                        <select name="kelas_id" class="form-control select2" required>
                                            <option value="">-- Pilih Kelas --</option>
                                            @foreach($kelas as $k)
                                                <option value="{{ $k->id }}">{{ $k->nama }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-success">Tempatkan Siswa Massal</button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css" />
@stop

@section('js')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2({
            theme: 'bootstrap4',
        });
        
        // Filter siswa berdasarkan unit
        $('#filter-unit').on('change', function() {
            var unit = $(this).val();
            
            $('.siswa-select').each(function() {
                $(this).val(null).trigger('change');
                
                if (unit === '') {
                    // Tampilkan semua siswa
                    $(this).find('option').each(function() {
                        $(this).show();
                    });
                } else {
                    // Filter berdasarkan unit
                    $(this).find('option').each(function() {
                        if ($(this).data('unit') === unit || $(this).val() === '') {
                            $(this).show();
                        } else {
                            $(this).hide();
                        }
                    });
                }
            });
        });
    });
</script>
@stop



