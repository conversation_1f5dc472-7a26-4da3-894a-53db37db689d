<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckAdmKtspAccess
{
    public function handle(Request $request, Closure $next)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        
        // Cek apakah user memiliki role yang sesuai
        if (!$user->hasAnyRole(['Kepala Sekolah', '<PERSON><PERSON><PERSON>', 'Administrator', 'Pengawa<PERSON>', 'Waka Kurikulum', '<PERSON><PERSON>', '<PERSON><PERSON> Sarpras'])) {
            return redirect()->route('dashboard')
                ->with('error', 'Anda tidak memiliki izin untuk mengakses KTSP.');
        }
        
        // Cek khusus untuk approve/reject
        if ($request->is('*/approve') || $request->is('*/reject')) {
            if (!$user->hasPermissionTo('manage-adm-ktsp')) {
                return redirect()->route('dashboard')
                    ->with('error', 'Anda tidak memiliki izin untuk menyetujui/menolak KTSP.');
            }
        }

        return $next($request);
    }
}