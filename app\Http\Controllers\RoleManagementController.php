<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleManagementController extends Controller
{
    public function __construct()
    {
       // $this->middleware('permission:manage-users');
    }
    
    public function index()
    {
        $roles = Role::with('permissions')->get();
        return view('admin.roles.index', compact('roles'));
    }
    
    public function create()
    {
        $permissions = Permission::all();
        return view('admin.roles.create', compact('permissions'));
    }
    
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name',
            'permissions' => 'array',
        ]);
        
        $role = Role::create(['name' => $request->name]);
        $role->syncPermissions($request->permissions);
        
        return redirect()->route('admin.roles.index')
            ->with('success', 'Role created successfully');
    }
    
    public function edit($id)
    {
        $role = Role::findById($id);
        $permissions = Permission::all();
        return view('admin.roles.edit', compact('role', 'permissions'));
    }
    
    public function update(Request $request, $id)
    {
        $role = Role::findById($id);
        
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,'.$id,
            'permissions' => 'array',
        ]);
        
        $role->update(['name' => $request->name]);
        
        // Perbaikan: Ambil objek Permission berdasarkan ID
        if ($request->has('permissions')) {
            $permissions = Permission::whereIn('id', $request->permissions)->get();
            $role->syncPermissions($permissions);
        } else {
            $role->syncPermissions([]);
        }
        
        return redirect()->route('admin.roles.index')
            ->with('success', 'Role updated successfully');
    }
}

