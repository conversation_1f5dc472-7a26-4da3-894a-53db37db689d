<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AbsensiSiswa;
use App\Models\PesertaDidik;
use App\Models\TahunAjaran;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class AbsensiController extends Controller
{
    public function __construct()
    {
       // $this->middleware('auth');
        // Tambahkan middleware lain di sini jika diperlukan
        // $this->middleware('middleware-lain')->only(['metodeTertentu']);
    }

    public function harian()
    {
        $user = auth()->user();
        $isGuru = $user->hasRole('Guru');
        $isAdmin = $user->hasRole(['Administrator', 'Yayasan', 'Pengawas']);
        
        // Dapatkan tanggal saat ini dan tahun ajaran aktif
        $today = now()->format('Y-m-d');
        $tahunAjaran = $this->getTahunAjaranAktif();
        
        // Inisialisasi kelasOptions
        $kelasOptions = [];
        
        // Untuk guru, dapatkan kelas mereka dari jadwal
        if ($isGuru) {
            $namaHari = $this->getNamaHari(now()->dayOfWeek);
            
            // Tambahkan logging untuk debugging
            \Log::info('User ID: ' . $user->id);
            \Log::info('Nama Hari: ' . $namaHari);
            \Log::info('Tahun Ajaran: ' . $tahunAjaran);
            
            // Coba dapatkan kelas dari jadwal guru
            $kelasOptions = $this->getKelasFromJadwalGuru($user->id, $namaHari, $tahunAjaran);
            
            // Jika tidak ada kelas yang ditemukan, coba ambil semua kelas yang diajar guru
            if (empty($kelasOptions)) {
                \Log::info('Tidak ada kelas untuk hari ini, mencoba ambil semua kelas yang diajar');
                $kelasOptions = DB::table('detail_jadwal')
                    ->join('jadwal_pelajaran', 'detail_jadwal.jadwal_pelajaran_id', '=', 'jadwal_pelajaran.id')
                    ->join('mata_pelajaran', 'detail_jadwal.mata_pelajaran_id', '=', 'mata_pelajaran.id')
                    ->where('mata_pelajaran.pengajar_id', $user->id)
                    ->where('jadwal_pelajaran.tahun_ajaran', $tahunAjaran)
                    ->where('detail_jadwal.is_istirahat', false)
                    ->select('jadwal_pelajaran.id as id', 'jadwal_pelajaran.nama_kelas_text as nama_kelas')
                    ->distinct()
                    ->get()
                    ->pluck('nama_kelas', 'id')
                    ->toArray();
            }
            
            \Log::info('Jumlah kelas yang ditemukan: ' . count($kelasOptions));
        }
        
        return view('absensi.harian', compact('isGuru', 'isAdmin', 'kelasOptions', 'today'));
    }

    public function rekap()
    {
        $user = auth()->user();
        $isGuru = $user->hasRole('Guru');
        $isAdmin = $user->hasRole('Admin');
        
        // Dapatkan tahun ajaran aktif
        $tahunAjaran = $this->getTahunAjaranAktif();
        
        // Dapatkan kelas berdasarkan peran pengguna
        $kelasOptions = [];
        if ($isGuru) {
            // Untuk guru, dapatkan semua kelas mereka
            $kelasOptions = DB::table('detail_jadwal')
                ->join('jadwal_pelajaran', 'detail_jadwal.jadwal_pelajaran_id', '=', 'jadwal_pelajaran.id')
                ->join('mata_pelajaran', 'detail_jadwal.mata_pelajaran_id', '=', 'mata_pelajaran.id')
                ->where('mata_pelajaran.pengajar_id', $user->id)
                ->where('jadwal_pelajaran.tahun_ajaran', $tahunAjaran)
                ->where('detail_jadwal.is_istirahat', false)
                ->select('jadwal_pelajaran.kelas_id as id', 'jadwal_pelajaran.nama_kelas_text as nama_kelas')
                ->distinct()
                ->get()
                ->pluck('nama_kelas', 'id')
                ->toArray();
        } elseif ($isAdmin) {
            // Untuk admin, dapatkan semua kelas
            $kelasOptions = DB::table('kelas')
                ->where('tahun_ajaran', $tahunAjaran)
                ->pluck('nama', 'id')
                ->toArray();
        }
        
        // Definisikan nama-nama bulan
        $namaBulan = [
            1 => 'Januari',
            2 => 'Februari',
            3 => 'Maret',
            4 => 'April',
            5 => 'Mei',
            6 => 'Juni',
            7 => 'Juli',
            8 => 'Agustus',
            9 => 'September',
            10 => 'Oktober',
            11 => 'November',
            12 => 'Desember'
        ];
        
        // Ambil parameter dari request jika ada
        $kelasId = request('kelas_id', '');
        $bulan = request('bulan', date('n'));
        $tahun = request('tahun', date('Y'));
        
        // Data rekap kosong secara default
        $rekapData = [];
        $totalDays = 0;
        
        // Jika ada filter yang dipilih, ambil data rekap
        if ($kelasId) {
            // Dapatkan semua hari dalam bulan yang dipilih
            $totalDays = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);
            
            // Dapatkan siswa di kelas
            $siswa = DB::table('peserta_didik')
                ->join('riwayat_kelas', 'peserta_didik.id', '=', 'riwayat_kelas.siswa_id')
                ->where('riwayat_kelas.kelas_baru_id', $kelasId)
                ->where('riwayat_kelas.tahun_ajaran', $tahunAjaran)
                ->where('peserta_didik.status', 'aktif')
                ->select('peserta_didik.id', 'peserta_didik.nama', 'peserta_didik.nisn')
                ->orderBy('peserta_didik.nama')
                ->distinct()
                ->get();
            
            // Dapatkan data absensi untuk setiap siswa
            foreach ($siswa as $s) {
                $absensi = [];
                for ($i = 1; $i <= $totalDays; $i++) {
                    $tanggal = sprintf('%04d-%02d-%02d', $tahun, $bulan, $i);
                    $data = DB::table('absensi_siswa')
                        ->where('siswa_id', $s->id)
                        ->where('tanggal', $tanggal)
                        ->first();
                    
                    $absensi[$i] = $data ? $data->status : null;
                }
                $s->absensi = $absensi;
                
                // Hitung ringkasan
                $s->hadir = count(array_filter($absensi, function($status) { return $status === 'hadir'; }));
                $s->sakit = count(array_filter($absensi, function($status) { return $status === 'sakit'; }));
                $s->izin = count(array_filter($absensi, function($status) { return $status === 'izin'; }));
                $s->alpa = count(array_filter($absensi, function($status) { return $status === 'alpa'; }));
            }
            
            $rekapData = $siswa;
        }
        
        return view('absensi.rekap', compact('isGuru', 'isAdmin', 'kelasOptions', 'kelasId', 'bulan', 'tahun', 'rekapData', 'namaBulan', 'totalDays'));
    }

    public function getStudentsByClass(Request $request)
    {
        $request->validate([
            'kelas_id' => 'required',
            'tanggal' => 'required|date',
        ]);
        
        $jadwalId = $request->kelas_id;
        $tanggal = $request->tanggal;
        
        // Tambahkan logging untuk debugging
        \Log::info('Mendapatkan siswa untuk ID jadwal: ' . $jadwalId . ' pada tanggal: ' . $tanggal);
        
        // Dapatkan tahun ajaran aktif
        $tahunAjaranAktif = $this->getTahunAjaranAktif();
        \Log::info('Tahun ajaran aktif: ' . $tahunAjaranAktif);
        
        // Dapatkan kelas_id dari jadwal_pelajaran
        $kelasId = DB::table('jadwal_pelajaran')
            ->where('id', $jadwalId)
            ->value('kelas_id');
        
        \Log::info('ID Kelas dari jadwal: ' . $kelasId);
        
        if (!$kelasId) {
            return response()->json([], 404);
        }
        
        // Dapatkan siswa di kelas menggunakan tabel riwayat_kelas
        $siswa = DB::table('peserta_didik')
            ->join('riwayat_kelas', 'peserta_didik.id', '=', 'riwayat_kelas.siswa_id')
            ->where('riwayat_kelas.kelas_baru_id', $kelasId)
            ->where('riwayat_kelas.tahun_ajaran', $tahunAjaranAktif)
            ->where('peserta_didik.status', 'aktif')
            ->select('peserta_didik.id', 'peserta_didik.nama', 'peserta_didik.nisn')
            ->orderBy('peserta_didik.nama')
            ->distinct()
            ->get();
        
        \Log::info('Found ' . count($siswa) . ' students');
        
        // Periksa apakah absensi sudah ada untuk hari ini
        foreach ($siswa as $s) {
            $absensi = DB::table('absensi_siswa')
                ->where('siswa_id', $s->id)
                ->where('tanggal', $tanggal)
                ->first();
            
            $s->status = $absensi->status ?? null;
            $s->keterangan = $absensi->keterangan ?? null;
        }
        
        return response()->json($siswa);
    }

    public function saveAttendance(Request $request)
    {
        $request->validate([
            'kelas_id' => 'required',
            'tanggal' => 'required|date',
            'absensi' => 'required|array',
            'absensi.*.siswa_id' => 'required|exists:peserta_didik,id',
            'absensi.*.status' => 'required|in:hadir,sakit,izin,alpa',
            'absensi.*.keterangan' => 'nullable|string'
        ]);
        
        try {
            DB::beginTransaction();
            
            foreach ($request->absensi as $data) {
                DB::table('absensi_siswa')
                    ->updateOrInsert(
                        [
                            'siswa_id' => $data['siswa_id'],
                            'tanggal' => $request->tanggal,
                        ],
                        [
                            'kelas_id' => $request->kelas_id,
                            'status' => $data['status'],
                            'keterangan' => $data['keterangan'] ?? null,
                            'created_by' => auth()->id(),
                            'updated_at' => now(),
                        ]
                    );
            }
            
            DB::commit();
            return response()->json(['success' => true, 'message' => 'Absensi berhasil disimpan']);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['success' => false, 'message' => 'Gagal menyimpan absensi: ' . $e->getMessage()], 500);
        }
    }

    public function getRekapAbsensi(Request $request)
    {
        $request->validate([
            'kelas_id' => 'required',
            'bulan' => 'required|numeric|min:1|max:12',
            'tahun' => 'required|numeric|min:2000|max:2100',
        ]);
        
        $kelasId = $request->kelas_id;
        $bulan = $request->bulan;
        $tahun = $request->tahun;
        
        // Dapatkan semua hari dalam bulan yang dipilih
        $totalDays = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);
        
        // Dapatkan tahun ajaran aktif
        $tahunAjaranAktif = $this->getTahunAjaranAktif();
        
        // Dapatkan siswa di kelas
        $siswa = DB::table('peserta_didik')
            ->join('riwayat_kelas', 'peserta_didik.id', '=', 'riwayat_kelas.siswa_id')
            ->where('riwayat_kelas.kelas_baru_id', $kelasId)
            ->where('riwayat_kelas.tahun_ajaran', $tahunAjaranAktif)
            ->where('peserta_didik.status', 'aktif')
            ->select('peserta_didik.id', 'peserta_didik.nama', 'peserta_didik.nisn')
            ->orderBy('peserta_didik.nama')
            ->distinct()
            ->get();
        
        // Dapatkan data absensi untuk setiap siswa
        foreach ($siswa as $s) {
            $absensi = [];
            for ($i = 1; $i <= $totalDays; $i++) {
                $tanggal = sprintf('%04d-%02d-%02d', $tahun, $bulan, $i);
                $data = DB::table('absensi_siswa')
                    ->where('siswa_id', $s->id)
                    ->where('tanggal', $tanggal)
                    ->first();
                
                $absensi[$i] = $data ? $data->status : null;
            }
            $s->absensi = $absensi;
            
            // Hitung ringkasan
            $s->hadir = count(array_filter($absensi, function($status) { return $status === 'hadir'; }));
            $s->sakit = count(array_filter($absensi, function($status) { return $status === 'sakit'; }));
            $s->izin = count(array_filter($absensi, function($status) { return $status === 'izin'; }));
            $s->alpa = count(array_filter($absensi, function($status) { return $status === 'alpa'; }));
        }
        
        return response()->json($siswa);
    }

    /**
     * Export data absensi ke Excel
     */
    public function exportExcel(Request $request)
    {
        $request->validate([
            'kelas_id' => 'required',
            'bulan' => 'required|numeric|min:1|max:12',
            'tahun' => 'required|numeric|min:2000|max:2100',
        ]);
        
        $kelasId = $request->kelas_id;
        $bulan = $request->bulan;
        $tahun = $request->tahun;
        
        // Dapatkan nama kelas
        $kelasNama = DB::table('kelas')
            ->where('id', $kelasId)
            ->value('nama');
        
        if (!$kelasNama) {
            return redirect()->back()->with('error', 'Kelas tidak ditemukan');
        }
        
        // Dapatkan semua hari dalam bulan yang dipilih
        $totalDays = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);
        
        // Dapatkan tahun ajaran aktif
        $tahunAjaranAktif = $this->getTahunAjaranAktif();
        
        // Dapatkan siswa di kelas
        $siswa = DB::table('peserta_didik')
            ->join('riwayat_kelas', 'peserta_didik.id', '=', 'riwayat_kelas.siswa_id')
            ->where('riwayat_kelas.kelas_baru_id', $kelasId)
            ->where('riwayat_kelas.tahun_ajaran', $tahunAjaranAktif)
            ->where('peserta_didik.status', 'aktif')
            ->select('peserta_didik.id', 'peserta_didik.nama', 'peserta_didik.nisn')
            ->orderBy('peserta_didik.nama')
            ->distinct()
            ->get();
        
        // Dapatkan data absensi untuk setiap siswa
        foreach ($siswa as &$s) {
            $absensi = [];
            for ($i = 1; $i <= $totalDays; $i++) {
                $tanggal = sprintf('%04d-%02d-%02d', $tahun, $bulan, $i);
                $data = DB::table('absensi_siswa')
                    ->where('siswa_id', $s->id)
                    ->where('tanggal', $tanggal)
                    ->first();
                
                $absensi[$i] = $data ? $data->status : null;
            }
            $s->absensi = $absensi;
            
            // Hitung ringkasan
            $s->hadir = count(array_filter($absensi, function($status) { return $status === 'hadir'; }));
            $s->sakit = count(array_filter($absensi, function($status) { return $status === 'sakit'; }));
            $s->izin = count(array_filter($absensi, function($status) { return $status === 'izin'; }));
            $s->alpa = count(array_filter($absensi, function($status) { return $status === 'alpa'; }));
        }
        
        // Konversi objek ke array untuk export
        $rekapData = json_decode(json_encode($siswa), true);
        
        // Nama bulan untuk judul file
        $namaBulan = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];
        
        $fileName = "Rekap_Absensi_{$kelasNama}_{$namaBulan[$bulan]}_{$tahun}.xlsx";
        
        // Sebelum mengirim data ke AbsensiExport, pastikan format data sudah benar
        \Illuminate\Support\Facades\Log::debug("Data rekap yang akan diekspor: " . json_encode(array_slice($rekapData, 0, 1)));

        return Excel::download(
            new \App\Exports\AbsensiExport($rekapData, $bulan, $tahun, $kelasNama, $totalDays),
            $fileName
        );
    }

    private function formatStatus($status)
    {
        switch ($status) {
            case 'hadir':
                return 'H';
            case 'sakit':
                return 'S';
            case 'izin':
                return 'I';
            case 'alpa':
                return 'A';
            default:
                return '';
        }
    }

    private function getNamaHari($dayOfWeek)
    {
        $namaHari = [
            0 => 'Minggu',
            1 => 'Senin',
            2 => 'Selasa',
            3 => 'Rabu',
            4 => 'Kamis',
            5 => 'Jumat',
            6 => 'Sabtu',
        ];
        
        return $namaHari[$dayOfWeek] ?? '';
    }

    private function getTahunAjaranAktif()
    {
        // Get active school year from tahun_ajaran table
        $tahunAjaran = DB::table('tahun_ajaran')
            ->where('aktif', true)
            ->first();
        
        return $tahunAjaran ? $tahunAjaran->nama : date('Y') . '/' . (date('Y') + 1);
    }

    private function getKelasFromJadwalGuru($userId, $hari, $tahunAjaran)
    {
        // Tambahkan logging untuk debugging
        \Log::info("Mencari kelas untuk guru ID: $userId, hari: $hari, tahun ajaran: $tahunAjaran");
        
        $query = DB::table('detail_jadwal')
            ->join('jadwal_pelajaran', 'detail_jadwal.jadwal_pelajaran_id', '=', 'jadwal_pelajaran.id')
            ->join('mata_pelajaran', 'detail_jadwal.mata_pelajaran_id', '=', 'mata_pelajaran.id')
            ->where('mata_pelajaran.pengajar_id', $userId)
            ->where('detail_jadwal.hari', $hari)
            ->where('jadwal_pelajaran.tahun_ajaran', $tahunAjaran)
            ->where('detail_jadwal.is_istirahat', false)
            ->select('jadwal_pelajaran.id as id', 'jadwal_pelajaran.nama_kelas_text as nama_kelas')
            ->distinct();
        
        // Log SQL query untuk debugging
        \Log::info("SQL Query: " . $query->toSql());
        \Log::info("SQL Bindings: " . json_encode($query->getBindings()));
        
        $result = $query->get()->pluck('nama_kelas', 'id')->toArray();
        \Log::info("Hasil query: " . json_encode($result));
        
        return $result;
    }
        //controller rekap untuk pengurus
    public function rekapAdmin()
    {
        // Cek apakah user memiliki role yang diizinkan
        $user = auth()->user();
        if (!$user->hasRole(['Administrator', 'Yayasan', 'Pengawas', 'Kepala Sekolah', 'Waka Kesiswaan', 'Waka Kurikulum'])) {
            return redirect()->back()->with('error', 'Anda tidak memiliki hak akses untuk melihat rekap absensi.');
        }

        // Dapatkan tahun ajaran aktif
        $tahunAjaran = $this->getTahunAjaranAktif();

        // Dapatkan semua kelas menggunakan pendekatan yang sama seperti di guru
        $kelasOptions = DB::table('jadwal_pelajaran')
            ->where('tahun_ajaran', $tahunAjaran)
            ->select('kelas_id as id', 'nama_kelas_text as nama_kelas')
            ->distinct()
            ->orderBy('nama_kelas_text')
            ->pluck('nama_kelas', 'id')
            ->toArray();

        // Dapatkan semua guru untuk filter, dengan filter berdasarkan unit
        $guruQuery = DB::table('users')
            ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('roles.name', 'Guru');
    
        // Filter guru berdasarkan unit pengguna yang login
        if (!$user->hasRole(['Administrator', 'Yayasan', 'Pengawas'])) {
            // Jika bukan admin global, filter berdasarkan unit
            $userUnit = $user->unit_id;
            $guruQuery->where('users.unit_id', $userUnit);
        }
    
        $guruOptions = $guruQuery->select('users.id', 'users.name')
            ->orderBy('users.name')
            ->pluck('name', 'id')
            ->toArray();

        // Definisikan nama-nama bulan
        $namaBulan = [
            1 => 'Januari',
            2 => 'Februari',
            3 => 'Maret',
            4 => 'April',
            5 => 'Mei',
            6 => 'Juni',
            7 => 'Juli',
            8 => 'Agustus',
            9 => 'September',
            10 => 'Oktober',
            11 => 'November',
            12 => 'Desember'
        ];

        // Ambil parameter dari request jika ada
        $kelasId = request('kelas_id', '');
        $guruId = request('guru_id', '');
        $bulan = request('bulan', date('n'));
        $tahun = request('tahun', date('Y'));

        // Data rekap kosong secara default
        $rekapData = [];
        $totalDays = 0;

        return view('absensi.rekap-admin', compact('kelasOptions', 'guruOptions', 'kelasId', 'guruId', 'bulan', 'tahun', 'rekapData', 'namaBulan', 'totalDays'));
    }

    public function getRekapAdmin(Request $request)
    {
        $request->validate([
            'kelas_id' => 'required',
            'bulan' => 'required|numeric|min:1|max:12',
            'tahun' => 'required|numeric|min:2000|max:2100',
        ]);
        
        $kelasId = $request->kelas_id;
        $guruId = $request->guru_id;
        $bulan = $request->bulan;
        $tahun = $request->tahun;
        
        // Dapatkan semua hari dalam bulan yang dipilih
        $totalDays = cal_days_in_month(CAL_GREGORIAN, $bulan, $tahun);
        
        // Dapatkan tahun ajaran aktif
        $tahunAjaranAktif = $this->getTahunAjaranAktif();
        
        // Dapatkan siswa di kelas menggunakan pendekatan yang sama seperti di guru
        $siswa = DB::table('peserta_didik')
            ->join('riwayat_kelas', 'peserta_didik.id', '=', 'riwayat_kelas.siswa_id')
            ->join('jadwal_pelajaran', 'riwayat_kelas.kelas_baru_id', '=', 'jadwal_pelajaran.kelas_id')
            ->where('jadwal_pelajaran.kelas_id', $kelasId)
            ->where('jadwal_pelajaran.tahun_ajaran', $tahunAjaranAktif)
            ->where('peserta_didik.status', 'aktif')
            ->select('peserta_didik.id', 'peserta_didik.nama', 'peserta_didik.nisn')
            ->orderBy('peserta_didik.nama')
            ->distinct()
            ->get();
        
        // Dapatkan data absensi untuk setiap siswa
        foreach ($siswa as $s) {
            $s->absensi = [];
            $s->hadir = 0;
            $s->sakit = 0;
            $s->izin = 0;
            $s->alpa = 0;
            
            // Query untuk mendapatkan absensi siswa
            $absensiQuery = DB::table('absensi_siswa')
                ->where('siswa_id', $s->id)
                ->whereYear('tanggal', $tahun)
                ->whereMonth('tanggal', $bulan);
            
            // Filter berdasarkan guru jika dipilih
            if ($guruId) {
                $absensiQuery->where('created_by', $guruId);
            }
            
            $absensiData = $absensiQuery->get();
            
            foreach ($absensiData as $absensi) {
                $tanggal = (int)date('j', strtotime($absensi->tanggal));
                $s->absensi[$tanggal] = $absensi->status;
                
                // Hitung total untuk setiap status
                if ($absensi->status == 'hadir') {
                    $s->hadir++;
                } elseif ($absensi->status == 'sakit') {
                    $s->sakit++;
                } elseif ($absensi->status == 'izin') {
                    $s->izin++;
                } elseif ($absensi->status == 'alpa') {
                    $s->alpa++;
                }
            }
        }
        
        return response()->json($siswa);
    }

    public function getKelasByGuru(Request $request)
    {
        $guruId = $request->guru_id;
        $tahunAjaran = $this->getTahunAjaranAktif();
        
        if (!$guruId) {
            // Jika tidak ada guru yang dipilih, kembalikan semua kelas
            $kelasOptions = DB::table('jadwal_pelajaran')
                ->where('tahun_ajaran', $tahunAjaran)
                ->select('kelas_id as id', 'nama_kelas_text as nama_kelas')
                ->distinct()
                ->orderBy('nama_kelas_text')
                ->get();
        } else {
            // Jika ada guru yang dipilih, kembalikan kelas yang diajar oleh guru tersebut
            $kelasOptions = DB::table('detail_jadwal')
                ->join('jadwal_pelajaran', 'detail_jadwal.jadwal_pelajaran_id', '=', 'jadwal_pelajaran.id')
                ->join('mata_pelajaran', 'detail_jadwal.mata_pelajaran_id', '=', 'mata_pelajaran.id')
                ->where('mata_pelajaran.pengajar_id', $guruId)
                ->where('jadwal_pelajaran.tahun_ajaran', $tahunAjaran)
                ->where('detail_jadwal.is_istirahat', false)
                ->select('jadwal_pelajaran.kelas_id as id', 'jadwal_pelajaran.nama_kelas_text as nama_kelas')
                ->distinct()
                ->orderBy('jadwal_pelajaran.nama_kelas_text')
                ->get();
        }
        
        return response()->json($kelasOptions);
    }
}






























