<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap">

    <!-- Styles -->
    <link rel="stylesheet" href="{{ asset('css/app.css') }}">
    <link rel="stylesheet" href="{{ asset('css/custom.css') }}">
    
    @yield('css')

    <!-- Scripts -->
    <script src="{{ asset('js/app.js') }}" defer></script>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">
        @include('layouts.navigation')

        <!-- Page Heading -->
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                @yield('header')
            </div>
        </header>

        <!-- Page Content -->
        <main>
            @yield('content')
        </main>
    </div>
    
    <!-- Notifikasi Modal -->
    @auth
    <div class="modal fade" id="notifikasiModal" tabindex="-1" role="dialog" aria-labelledby="notifikasiModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="notifikasiModalLabel">Notifikasi Baru</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="notifikasi-list-popup" class="list-group">
                        <!-- Notifikasi akan dimuat di sini -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <a href="{{ route('notifikasi.mark-all-as-read') }}" class="btn btn-primary">Tandai Semua Dibaca</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Fungsi untuk memeriksa notifikasi yang belum dibaca
            function checkUnreadNotifications() {
                fetch('{{ route("notifikasi.count") }}')
                    .then(response => response.json())
                    .then(data => {
                        // Update badge di navbar
                        const badge = document.getElementById('notification-badge');
                        if (badge) {
                            badge.textContent = data.count;
                            badge.style.display = data.count > 0 ? 'inline-block' : 'none';
                        }
                        
                        // Jika ada notifikasi yang belum dibaca, tampilkan pop-up
                        if (data.count > 0) {
                            loadNotifications();
                        }
                    })
                    .catch(error => console.error('Error checking notifications:', error));
            }

            // Fungsi untuk memuat notifikasi ke dalam pop-up
            function loadNotifications() {
                fetch('{{ route("notifikasi.get-unread") }}')
                    .then(response => response.json())
                    .then(data => {
                        const notifikasiList = document.getElementById('notifikasi-list-popup');
                        if (!notifikasiList) return;
                        
                        notifikasiList.innerHTML = '';

                        if (data.notifikasi.length > 0) {
                            data.notifikasi.forEach(item => {
                                let icon = '';
                                if (item.jenis === 'warning') {
                                    icon = '<i class="fas fa-exclamation-triangle text-warning"></i>';
                                } else if (item.jenis === 'success') {
                                    icon = '<i class="fas fa-check-circle text-success"></i>';
                                } else if (item.jenis === 'danger') {
                                    icon = '<i class="fas fa-times-circle text-danger"></i>';
                                } else {
                                    icon = '<i class="fas fa-info-circle text-info"></i>';
                                }

                                const notifikasiItem = document.createElement('div');
                                notifikasiItem.className = 'list-group-item list-group-item-action';
                                notifikasiItem.innerHTML = `
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1">${icon} ${item.judul}</h5>
                                        <small>${item.created_at_formatted}</small>
                                    </div>
                                    <p class="mb-1">${item.pesan}</p>
                                    <div class="d-flex justify-content-end mt-2">
                                        <a href="${item.mark_as_read_url}" class="btn btn-sm btn-primary mr-2">
                                            <i class="fas fa-eye"></i> Lihat
                                        </a>
                                        <a href="${item.mark_as_read_url}" class="btn btn-sm btn-success">
                                            <i class="fas fa-check"></i> Tandai Dibaca
                                        </a>
                                    </div>
                                `;
                                notifikasiList.appendChild(notifikasiItem);
                            });

                            // Tampilkan modal
                            $('#notifikasiModal').modal('show');
                        }
                    })
                    .catch(error => console.error('Error loading notifications:', error));
            }

            // Periksa notifikasi saat halaman dimuat
            checkUnreadNotifications();

            // Periksa notifikasi setiap 5 menit (300000 ms)
            setInterval(checkUnreadNotifications, 300000);
        });
    </script>
    @endauth
    
    @yield('js')
</body>
</html>

<!-- AdminLTE App -->
<script src="{{ asset('vendor/adminlte/dist/js/adminlte.min.js') }}"></script>


