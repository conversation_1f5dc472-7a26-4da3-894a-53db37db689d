<?php $__env->startSection('title', 'Artikeljj ' . $unit->nama_unit); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-5">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <?php echo $__env->make('website.partials._sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        </div>

        <!-- Content -->
        <div class="col-md-9">
            <h2>Artikel <?php echo e($unit->nama_unit); ?></h2>
            <div class="row mt-4">
                <?php $__empty_1 = true; $__currentLoopData = $articles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <?php if($article->image): ?>
                                <img src="<?php echo e(asset('storage/' . $article->image)); ?>" 
                                     class="card-img-top" 
                                     alt="<?php echo e($article->title); ?>">
                            <?php endif; ?>
                            <div class="card-body">
                                <h5 class="card-title"><?php echo e($article->title); ?></h5>
                                <p class="card-text"><?php echo e($article->excerpt); ?></p>
                                <a href="<?php echo e(route('website.artikel.show', $article->slug)); ?>" 
                                   class="btn btn-primary">
                                    ttBaca Selengkapnya
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-12">
                        <p class="text-center">Belum ada artikel.</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($articles->links()); ?>

            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.website', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/website/jenjang/artikel.blade.php ENDPATH**/ ?>