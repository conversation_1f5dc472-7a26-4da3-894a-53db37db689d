@extends('adminlte::page')

@section('title', 'Daftar Laporan Kerja')

@section('content_header')
    <h1>Daftar Laporan Kerja</h1>
@stop

@section('content')
<div class="container">
    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    @can('manage-laporan-kerja')
    <div class="mb-3">
        <a href="{{ route('laporan-kerja.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Tambah Laporan Kerja
        </a>
    </div>
    @endcan

    <div class="card">
        <div class="card-body">
            <table class="table table-bordered table-striped" id="laporanTable">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Uraian <PERSON>r<PERSON></th>
                        <th>Waktu <PERSON></th>
                        <th>W<PERSON><PERSON></th>
                        <th>Status</th>
                        <th>Keterangan</th>
                        <th>Dibuat <PERSON>h</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($laporanKerja as $index => $laporan)
                    <tr>
                        <td>{{ $index + 1 }}</td>
                        <td>{{ $laporan->uraian_kerja }}</td>
                        <td>{{ \Carbon\Carbon::parse($laporan->waktu_mulai)->format('d/m/Y H:i') }}</td>
                        <td>
                            @if($laporan->waktu_selesai)
                                {{ \Carbon\Carbon::parse($laporan->waktu_selesai)->format('d/m/Y H:i') }}
                            @else
                                -
                            @endif
                        </td>
                        <td>
                            @if($laporan->status == 'selesai')
                                <span class="badge badge-success">Selesai</span>
                            @elseif($laporan->status == 'proses')
                                <span class="badge badge-primary">Proses</span>
                            @else
                                <span class="badge badge-warning">Tertunda</span>
                            @endif
                        </td>
                        <td>{{ $laporan->keterangan ?? '-' }}</td>
                        <td>{{ $laporan->user->name }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ route('laporan-kerja.show', $laporan->id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @can('manage-laporan-kerja')
                                <a href="{{ route('laporan-kerja.edit', $laporan->id) }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('laporan-kerja.destroy', $laporan->id) }}" method="POST" onsubmit="return confirm('Apakah Anda yakin ingin menghapus laporan ini?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                @endcan
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@stop

@section('css')
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<style>
    .btn-group form {
        display: inline-block;
    }
</style>
@stop

@section('js')
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
<script>
    $(document).ready(function() {
        $('#laporanTable').DataTable({
            "language": {
                "search": "Pencarian:",
                "lengthMenu": "Tampilkan _MENU_ data per halaman",
                "zeroRecords": "Data tidak ditemukan",
                "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
                "infoEmpty": "Tidak ada data yang tersedia",
                "infoFiltered": "(difilter dari _MAX_ total data)",
                "paginate": {
                    "first": "Pertama",
                    "last": "Terakhir",
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                }
            }
        });
    });
</script>
@stop
