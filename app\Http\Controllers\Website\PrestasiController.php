<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Achievement;
use App\Models\Unit;
use Illuminate\Http\Request;

class PrestasiController extends Controller
{
    public function index()
    {
        $prestasis = Achievement::with('unit')
            ->latest()
            ->paginate(10);
        return view('admin.website.prestasi.index', compact('prestasis'));
    }

    public function create()
    {
        $units = Unit::all();
        return view('admin.website.prestasi.create', compact('units'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'level' => 'required|string',
            'unit_id' => 'required|exists:units,id',
            'participant' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048'
        ]);

        // Buat array data yang akan disimpan
        $data = [
            'title' => $request->title,
            'level' => $request->level,
            'unit_id' => $request->unit_id,
            'participant' => $request->participant,
            'description' => $request->description,
        ];

        // Handle upload gambar jika ada
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('storage/prestasi'), $filename);
            $data['image'] = $filename;
        }

        // Simpan ke database
        Achievement::create($data);

        return redirect()->route('admin.website.prestasi.index')
            ->with('success', 'Prestasi berhasil ditambahkan');
    }

    public function edit(Achievement $prestasi)
    {
        $units = Unit::all();
        return view('admin.website.prestasi.edit', compact('prestasi', 'units'));
    }

    public function update(Request $request, Achievement $prestasi)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'level' => 'required|string',
            'unit_id' => 'required|exists:units,id',
            'participant' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048'
        ]);

        if ($request->hasFile('image')) {
            if ($prestasi->image && file_exists(public_path('storage/prestasi/' . $prestasi->image))) {
                unlink(public_path('storage/prestasi/' . $prestasi->image));
            }
            
            $image = $request->file('image');
            $filename = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('storage/prestasi'), $filename);
            $validated['image'] = $filename;
        }

        $prestasi->update($validated);
        return redirect()->route('admin.website.prestasi.index')->with('success', 'Prestasi berhasil diperbarui');
    }

    public function destroy(Achievement $prestasi)
    {
        // Hapus gambar jika ada
        if ($prestasi->image && file_exists(public_path('storage/prestasi/' . $prestasi->image))) {
            unlink(public_path('storage/prestasi/' . $prestasi->image));
        }

        $prestasi->delete();
        return redirect()->route('admin.website.prestasi.index')->with('success', 'Prestasi berhasil dihapus');
    }
}
