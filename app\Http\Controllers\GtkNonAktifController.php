<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Guru;
use App\Traits\FiltersByUserUnit;
use Illuminate\Support\Facades\Log;

class GtkNonAktifController extends Controller
{
    use FiltersByUserUnit;
    
    public function index()
    {
        $user = auth()->user();
        $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
        
        // Query dasar untuk GTK non-aktif
        $query = Guru::with('unit')
                    ->where('status', 'Non-Aktif');
        
        // Terapkan filter unit jika user tidak memiliki akses ke semua unit
        if (!$canViewAllUnits && $user->unit_id) {
            $query->where('unit_id', $user->unit_id);
            
            // Log untuk debugging
            Log::info('Filtering GTK non-aktif by unit', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'unit_id' => $user->unit_id,
                'roles' => $user->getRoleNames()
            ]);
        }
        
        $gtkNonAktif = $query->orderBy('updated_at', 'desc')->get();
        
        // Ambil daftar unit untuk filter (jika diperlukan)
        $units = \App\Models\Unit::orderBy('nama_unit')->get();
        
        return view('gtk.non-aktif.index', compact('gtkNonAktif', 'units', 'canViewAllUnits'));
    }
    
    public function aktivasi($id)
    {
        try {
            $gtk = Guru::findOrFail($id);
            
            // Cek apakah user memiliki akses ke data ini
            $user = auth()->user();
            $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
            
            if (!$canViewAllUnits && $user->unit_id != $gtk->unit_id) {
                return redirect()->route('gtk.nonaktif.index')
                                ->with('error', 'Anda tidak memiliki akses untuk mengaktifkan GTK ini');
            }
            
            $gtk->status = 'Aktif';
            $gtk->save();
            
            return redirect()->route('gtk.nonaktif.index')
                            ->with('success', 'GTK berhasil diaktifkan kembali');
        } catch (\Exception $e) {
            return redirect()->back()
                            ->with('error', 'Gagal mengaktifkan GTK: ' . $e->getMessage());
        }
    }
}
