@extends('adminlte::page')

@section('title', 'Dashboard')

@section('content_header')
    <h1>@yield('page_title')</h1>
    @if(config('app.debug'))
        <div class="d-none">
            Current User: {{ auth()->user()->name }}
            Roles: {{ auth()->user()->getRoleNames() }}
            Permissions: {{ auth()->user()->getAllPermissions()->pluck('name') }}
        </div>
    @endif
@stop

@section('content')
    @yield('content')
@stop

@section('css')
    <link rel="stylesheet" href="css/admin_custom.css">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
    @stack('styles')
@stop

@section('js')
   <!-- <script src="https://cdn.ckeditor.com/ckeditor5/27.1.0/classic/ckeditor.js"></script> -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <script> console.log('Hi!'); </script>
@stop

<!-- Sidebar atau menu admin -->
<ul class="nav">
    <!-- ... other menu items ... -->
    
   
</ul>







