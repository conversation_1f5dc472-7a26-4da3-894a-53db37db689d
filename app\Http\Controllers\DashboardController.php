<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PesertaDidik;
use App\Models\Event;
use App\Models\Achievement;
use App\Models\Facility;
use App\Models\Ekstrakurikuler;
use App\Models\Slide;
use App\Models\Unit;
use Illuminate\Support\Facades\DB;
use App\Traits\FiltersByUserUnit;

class DashboardController extends Controller
{
    use FiltersByUserUnit;
    
    public function index()
    {
        $user = auth()->user();
        $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
        
        // Jika user memiliki unit_id dan bukan admin
        if ($user->unit_id && !$canViewAllUnits) {
            // Pastikan query menghitung jenis kelamin dengan benar
            $totalSiswa = PesertaDidik::byUnit($user->unit_id)->aktif()->count();
            $siswaPerKelas = PesertaDidik::byUnit($user->unit_id)->aktif()
                ->select(
                    'kelas_id',
                    DB::raw('count(*) as total'),
                    DB::raw('SUM(CASE WHEN jenis_kelamin = "L" THEN 1 ELSE 0 END) as laki_laki'),
                    DB::raw('SUM(CASE WHEN jenis_kelamin = "P" THEN 1 ELSE 0 END) as perempuan')
                )
                ->groupBy('kelas_id')
                ->with('kelas')
                ->get()
                ->map(function($item) use ($user) {
                    return [
                        'kelas' => $item->kelas ? $item->kelas->nama : 'Tanpa Kelas',
                        'unit' => $user->unit ? $user->unit->nama_unit : 'Tanpa Unit', // Gunakan unit dari user
                        'total' => $item->total,
                        'laki_laki' => $item->laki_laki,
                        'perempuan' => $item->perempuan
                    ];
                });

            $totalGuru = \App\Models\User::where('unit_id', $user->unit_id)
                ->whereHas('roles', function($q) {
                    $q->where('name', 'Guru');
                })->count();

            // Filter online users by unit
            $onlineUsers = \App\Models\User::where('unit_id', $user->unit_id)
                ->where('last_activity', '>=', now()->subMinutes(5))
                ->get();

            // Statistik Website untuk unit tertentu
            $totalEvents = Event::where('unit_id', $user->unit_id)->count();
            $totalAchievements = Achievement::where('unit_id', $user->unit_id)->count();
            $totalFacilities = Facility::count(); // Facilities tidak memiliki unit_id
            $totalExtracurriculars = Ekstrakurikuler::where('unit_id', $user->unit_id)->count();
            $totalSlides = Slide::where('status', 1)->count(); // Slides aktif saja

            return view('admin.dashboard', compact(
                'totalSiswa', 'siswaPerKelas', 'totalGuru', 'onlineUsers',
                'totalEvents', 'totalAchievements', 'totalFacilities', 'totalExtracurriculars', 'totalSlides'
            ));
        } else {
            // Statistik untuk semua unit
            $totalSiswa = PesertaDidik::aktif()->count();
            
            // Hitung total guru untuk semua unit
            $totalGuru = \App\Models\User::whereHas('roles', function($q) {
                $q->where('name', 'Guru');
            })->count();
            
            $siswaPerUnit = PesertaDidik::aktif()
                ->select('unit_id', DB::raw('count(*) as total'))
                ->groupBy('unit_id')
                ->with('unit')
                ->get()
                ->map(function($item) {
                    return [
                        'unit' => $item->unit ? $item->unit->nama_unit : 'Tanpa Unit',
                        'total' => $item->total
                    ];
                });
            
            // Pastikan query menghitung jenis kelamin dengan benar
            $siswaPerKelas = PesertaDidik::aktif()
                ->select(
                    'kelas_id', 
                    'unit_id', 
                    DB::raw('count(*) as total'),
                    DB::raw('SUM(CASE WHEN jenis_kelamin = "L" THEN 1 ELSE 0 END) as laki_laki'),
                    DB::raw('SUM(CASE WHEN jenis_kelamin = "P" THEN 1 ELSE 0 END) as perempuan')
                )
                ->groupBy(['kelas_id', 'unit_id'])
                ->with(['kelas', 'unit'])
                ->get()
                ->map(function($item) {
                    return [
                        'kelas' => $item->kelas ? $item->kelas->nama : 'Tanpa Kelas',
                        'unit' => $item->unit ? $item->unit->nama_unit : 'Tanpa Unit',
                        'total' => $item->total,
                        'laki_laki' => $item->laki_laki,
                        'perempuan' => $item->perempuan
                    ];
                });
            
            $guruPerUnit = \App\Models\User::whereHas('roles', function($q) {
                    $q->where('name', 'Guru');
                })
                ->select('unit_id', DB::raw('count(*) as total'))
                ->groupBy('unit_id')
                ->with('unit')
                ->get()
                ->map(function($item) {
                    return [
                        'unit' => $item->unit ? $item->unit->nama_unit : 'Tanpa Unit',
                        'total' => $item->total
                    ];
                });
            
            // Get all online users for admin
            $onlineUsers = \App\Models\User::where('last_activity', '>=', now()->subMinutes(5))
                ->with('unit')
                ->get();
            
            // Get all units for filter
            $units = Unit::orderBy('nama_unit')->get();

            // Statistik Website untuk semua unit
            $totalEvents = Event::count();
            $totalAchievements = Achievement::count();
            $totalFacilities = Facility::count();
            $totalExtracurriculars = Ekstrakurikuler::count();
            $totalSlides = Slide::where('status', 1)->count();

            // Statistik per unit untuk website
            $eventsPerUnit = Event::select('unit_id', DB::raw('count(*) as total'))
                ->groupBy('unit_id')
                ->with('unit')
                ->get()
                ->map(function($item) {
                    return [
                        'unit' => $item->unit ? $item->unit->nama_unit : 'Tanpa Unit',
                        'total' => $item->total
                    ];
                });

            $achievementsPerUnit = Achievement::select('unit_id', DB::raw('count(*) as total'))
                ->groupBy('unit_id')
                ->with('unit')
                ->get()
                ->map(function($item) {
                    return [
                        'unit' => $item->unit ? $item->unit->nama_unit : 'Tanpa Unit',
                        'total' => $item->total
                    ];
                });

            $extracurricularsPerUnit = Ekstrakurikuler::select('unit_id', DB::raw('count(*) as total'))
                ->groupBy('unit_id')
                ->with('unit')
                ->get()
                ->map(function($item) {
                    return [
                        'unit' => $item->unit ? $item->unit->nama_unit : 'Tanpa Unit',
                        'total' => $item->total
                    ];
                });

            return view('admin.dashboard', compact(
                'totalSiswa', 'totalGuru', 'siswaPerUnit', 'siswaPerKelas', 'guruPerUnit', 'onlineUsers', 'units',
                'totalEvents', 'totalAchievements', 'totalFacilities', 'totalExtracurriculars', 'totalSlides',
                'eventsPerUnit', 'achievementsPerUnit', 'extracurricularsPerUnit'
            ));
        }
    }
}







