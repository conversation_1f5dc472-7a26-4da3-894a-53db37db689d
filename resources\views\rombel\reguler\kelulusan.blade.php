@extends('adminlte::page')

@section('title', 'Kelulus<PERSON> Siswa')

@section('content_header')
    <h1><PERSON><PERSON><PERSON><PERSON> Si<PERSON>wa</h1>
@stop

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Ke<PERSON><PERSON>an <PERSON> A<PERSON> {{ $tahunAjaranAktif->nama }}</h3>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                
                @if(session('error'))
                    <div class="alert alert-danger">{{ session('error') }}</div>
                @endif

                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-primary">
                                <h3 class="card-title"><PERSON><PERSON> Tingkat Akhir</h3>
                            </div>
                            <div class="card-body">
                                <form method="GET" action="{{ route('rombel.reguler.kelulusan') }}">
                                    <div class="form-group">
                                        <label>Pilih Kelas:</label>
                                        <select id="kelasAkhir" name="kelas_id" class="form-control" onchange="this.form.submit()">
                                            <option value="">-- Pilih Kelas --</option>
                                            @foreach($kelas as $k)
                                                <option value="{{ $k->id }}" {{ request('kelas_id') == $k->id ? 'selected' : '' }}>
                                                    {{ $k->nama }} ({{ $k->siswa->count() }} siswa)
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </form>
                                
                                @if(request('kelas_id'))
                                <div id="daftarSiswaAkhir" class="mt-3">
                                    <h5>Daftar Siswa</h5>
                                    <form id="formKelulusanKelas" action="{{ route('rombel.reguler.kelulusan.kelas') }}" method="POST">
                                        @csrf
                                        <input type="hidden" name="kelas_id" value="{{ request('kelas_id') }}">
                                        <button type="submit" class="btn btn-success mb-3" id="btnLuluskanSemua">
                                            <i class="fas fa-graduation-cap"></i> Luluskan Semua Siswa
                                        </button>
                                    </form>
                                    
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>No</th>
                                                    <th>NIS</th>
                                                    <th>Nama</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @if(isset($siswaList) && count($siswaList) > 0)
                                                    @foreach($siswaList as $index => $siswa)
                                                    <tr>
                                                        <td>{{ $index + 1 }}</td>
                                                        <td>{{ $siswa->nis }}</td>
                                                        <td>{{ $siswa->nama }}</td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-primary btn-kelulusan-individu" 
                                                                    data-toggle="modal" 
                                                                    data-target="#modalKelulusanIndividu"
                                                                    data-id="{{ $siswa->id }}" 
                                                                    data-nama="{{ $siswa->nama }}">
                                                                <i class="fas fa-graduation-cap"></i> Luluskan
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                @else
                                                    <tr>
                                                        <td colspan="4" class="text-center">Tidak ada siswa di kelas ini</td>
                                                    </tr>
                                                @endif
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Kelulusan Individu -->
<div class="modal fade" id="modalKelulusanIndividu" tabindex="-1" role="dialog" aria-labelledby="modalKelulusanIndividuLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalKelulusanIndividuLabel">Konfirmasi Kelulusan</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('rombel.reguler.kelulusan.individu') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="siswa_id" id="siswaId">
                    <p>Apakah Anda yakin ingin meluluskan siswa <strong id="namaSiswa"></strong>?</p>
                    <p>Status siswa akan berubah menjadi alumni dan akan dikeluarkan dari kelas saat ini.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Luluskan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
<script>
    $(document).ready(function() {
        // Handler untuk tombol kelulusan individu
        $('.btn-kelulusan-individu').click(function() {
            const siswaId = $(this).data('id');
            const namaSiswa = $(this).data('nama');
            
            $('#siswaId').val(siswaId);
            $('#namaSiswa').text(namaSiswa);
        });
    });
</script>
@stop

