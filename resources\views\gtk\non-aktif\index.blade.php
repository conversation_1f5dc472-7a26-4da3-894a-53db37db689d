@extends('layouts.admin')

@section('title', 'GTK Non-Aktif')

@section('page_title', 'GTK Non-Aktif')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar GTK Non-Aktif</h3>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        @if($canViewAllUnits)
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="filter-unit">Filter Unit:</label>
                    <select id="filter-unit" class="form-control">
                        <option value="">Semua Unit</option>
                        @foreach($units as $unit)
                            <option value="{{ $unit->nama_unit }}">{{ $unit->nama_unit }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
        @else
        <div class="alert alert-info">
            <h5><i class="icon fas fa-info"></i> Informasi Unit</h5>
            <p>Anda melihat data GTK non-aktif dari unit: <strong>{{ auth()->user()->unit->nama_unit ?? 'Tidak ada unit' }}</strong></p>
        </div>
        @endif

        <table class="table table-bordered table-striped" id="gtkNonAktifTable">
            <thead>
                <tr>
                    <th>No</th>
                    <th>NIP/NIY</th>
                    <th>Nama</th>
                    <th>Unit</th>
                    <th>Jenis PTK</th>
                    <th>Tanggal Non-Aktif</th>
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
                @forelse($gtkNonAktif as $index => $gtk)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $gtk->nip ?? $gtk->niy ?? '-' }}</td>
                    <td>{{ $gtk->nama }}</td>
                    <td>{{ $gtk->unit->nama_unit ?? '-' }}</td>
                    <td>{{ $gtk->jenis_ptk ?? '-' }}</td>
                    <td>{{ $gtk->updated_at ? \Carbon\Carbon::parse($gtk->updated_at)->format('d/m/Y') : '-' }}</td>
                    <td>
                        @if(auth()->user()->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']) || auth()->user()->unit_id == $gtk->unit_id)
                        
                        <form action="{{ route('gtk.nonaktif.aktivasi', $gtk->id) }}" method="POST" class="d-inline">
                            @csrf
                            @method('PUT')
                            <!--
                            <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Apakah Anda yakin ingin mengaktifkan kembali GTK ini?')">
                                <i class="fas fa-check"></i> 1Aktifkan
                            </button>
                            -->                            
                        </form>
                        @endif
                        <a href="{{ route('gtk.guru.show', $gtk->id) }}" class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i> Detail
                        </a>
                        <button type="button" class="btn btn-sm btn-success" data-toggle="modal" data-target="#aktivasiModal{{ $gtk->id }}">
                            <i class="fas fa-user-check"></i> Aktifkan
                        </button>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<!-- Modal Aktivasi GTK -->
@foreach($gtkNonAktif as $gtk)
<div class="modal fade" id="aktivasiModal{{ $gtk->id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aktivasi GTK</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('gtk.nonaktif.aktivasi', $gtk->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin mengaktifkan kembali <strong>{{ $gtk->nama }}</strong>?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">Aktifkan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endforeach
@stop

@section('css')
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
@stop

@section('js')
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
<script>
    $(document).ready(function() {
        var table = $('#gtkNonAktifTable').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
            }
        });
        
        // Filter berdasarkan unit (untuk admin, yayasan, pengawas)
        $('#filter-unit').on('change', function() {
            var unitName = $(this).val();
            table.column(3) // Kolom unit (indeks 3)
                .search(unitName)
                .draw();
        });
    });
</script>
@endsection

