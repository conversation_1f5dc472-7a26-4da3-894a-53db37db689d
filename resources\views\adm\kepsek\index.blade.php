@extends('adminlte::page')

@section('title', 'ADM Kepala Sekolah')

@section('content_header')
    <h1>ADM Kepala Sekolah</h1>
@stop

@section('content')
<div class="container">
    @if(auth()->user()->hasRole('Kepala Sekolah'))
        <button type="button" class="btn btn-primary mb-3" data-toggle="modal" data-target="#uploadModal">
            <i class="fas fa-upload"></i> Upload ADM
        </button>
    @endif

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Daftar ADM Kepala Sekolah</h3>
        </div>
        <div class="card-body">
            <table class="table table-bordered table-striped datatable">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Judul</th>
                        <th>Keterangan</th>
                        <th>Diupload Oleh</th>
                        <th>Tanggal Upload</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($admKepsek as $index => $adm)
                    <tr>
                        <td>{{ $index + 1 }}</td>
                        <td>{{ $adm->judul }}</td>
                        <td>{{ $adm->keterangan ?? '-' }}</td>
                        <td>{{ $adm->user->name }}</td>
                        <td>{{ $adm->created_at->format('d-m-Y H:i') }}</td>
                        <td>
                            @if($adm->status == 'pending')
                                <span class="badge badge-warning">Pending</span>
                            @elseif($adm->status == 'approved')
                                <span class="badge badge-success" 
                                      data-toggle="tooltip" 
                                      data-html="true"
                                      title="<strong>Disetujui oleh:</strong> {{ $adm->approver->name ?? 'N/A' }}<br>
                                             <strong>Pada:</strong> {{ $adm->approved_at instanceof \DateTime ? $adm->approved_at->format('d/m/Y H:i') : ($adm->approved_at ?? 'N/A') }}">
                                    Disetujui
                                </span>
                            @elseif($adm->status == 'ditangguhkan')
                                <span class="badge badge-danger" 
                                      data-toggle="tooltip" 
                                      data-html="true"
                                      title="<strong>Ditangguhkan oleh:</strong> {{ $adm->rejecter->name ?? 'N/A' }}<br>
                                             <strong>Alasan:</strong> {{ $adm->alasan_penolakan ?? 'N/A' }}<br>
                                             <strong>Pada:</strong> {{ $adm->rejected_at instanceof \DateTime ? $adm->rejected_at->format('d/m/Y H:i') : ($adm->rejected_at ?? 'N/A') }}">
                                    Ditangguhkan
                                </span>
                            @endif
                        </td>
                        <td>
                            <a href="{{ route('adm.kepsek.view-page', basename($adm->file_path)) }}" class="btn btn-sm btn-info" target="_blank">
                                <i class="fas fa-eye"></i> Lihat
                            </a>
                            
                            @if($adm->status == 'pending' && auth()->user()->hasRole('Pengawas'))
                                <button type="button" class="btn btn-sm btn-success" data-toggle="modal" data-target="#approveModal{{ $adm->id }}">
                                    <i class="fas fa-check"></i> Setujui
                                </button>
                                <button type="button" class="btn btn-sm btn-danger" data-toggle="modal" data-target="#rejectModal{{ $adm->id }}">
                                    <i class="fas fa-times"></i> Tolak
                                </button>
                            @endif
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

@if(auth()->user()->hasRole('Kepala Sekolah'))
    @include('adm.kepsek.modals.upload')
@endif

@foreach($admKepsek as $adm)
    @if($adm->status == 'pending' && auth()->user()->hasRole('Pengawas'))
        @include('adm.kepsek.modals.approve', ['adm' => $adm])
        @include('adm.kepsek.modals.reject', ['adm' => $adm])
    @endif
@endforeach
@stop

@section('css')
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.25/css/dataTables.bootstrap4.min.css">
@stop

@section('js')
    <script src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.25/js/dataTables.bootstrap4.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.datatable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Indonesian.json"
                }
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            // Inisialisasi DataTable jika diperlukan
            $('#admTable').DataTable();
            
            // Inisialisasi tooltip dengan dukungan HTML
            $('[data-toggle="tooltip"]').tooltip({
                html: true,
                container: 'body'
            });
            
            // Kode JavaScript lainnya...
        });
    </script>
@stop

