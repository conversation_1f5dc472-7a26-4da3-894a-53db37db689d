@extends('adminlte::page')

@section('title', 'Daftar Kompetensi Dasar')

@section('content_header')
    <h1>Daftar Kompetensi Dasar</h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Kompetensi Dasar</h3>
            <div class="card-tools">
                <a href="{{ route('penilaian.kompetensi.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Tambah Kompetensi Dasar
                </a>
            </div>
        </div>
        <div class="card-body">
            <!-- Pesan Sukses -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h5><i class="icon fas fa-check"></i> Sukses!</h5>
                    {{ session('success') }}
                </div>
            @endif

            <!-- Filter -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <form action="{{ route('penilaian.kompetensi.index') }}" method="GET" class="form-inline">
                        <select name="mata_pelajaran_id" class="form-control mr-2">
                            <option value="">Semua Mata Pelajaran</option>
                            @foreach($mataPelajaran as $mp)
                                <option value="{{ $mp->id }}" {{ request('mata_pelajaran_id') == $mp->id ? 'selected' : '' }}>
                                    {{ $mp->nama_mapel }}
                                </option>
                            @endforeach
                        </select>
                        <button type="submit" class="btn btn-primary">Filter</button>
                    </form>
                </div>
            </div>

            <!-- Tabel Kompetensi Dasar -->
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="kompetensiTable">
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th width="15%">Kode</th>
                            <th width="20%">Mata Pelajaran</th>
                            <th>Deskripsi</th>
                            <th width="15%">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($kompetensiDasar as $index => $kd)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $kd->kode }}</td>
                                <td>{{ $kd->mataPelajaran->nama_mapel }}</td>
                                <td>{{ $kd->deskripsi }}</td>
                                <td>
                                    <a href="{{ route('penilaian.kompetensi.edit', $kd->id) }}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('penilaian.kompetensi.destroy', $kd->id) }}" method="POST" style="display: inline-block;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center">Tidak ada data kompetensi dasar</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-3">
                {{ $kompetensiDasar->links() }}
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            // Inisialisasi DataTables
            $('#kompetensiTable').DataTable({
                "paging": false, // Nonaktifkan paging karena sudah menggunakan pagination Laravel
                "lengthChange": false,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                }
            });
        });
    </script>
@stop