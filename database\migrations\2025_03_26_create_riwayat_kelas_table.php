<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('riwayat_kelas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('siswa_id')->constrained('peserta_didik');
            $table->foreignId('kelas_lama_id')->nullable()->constrained('kelas');
            $table->foreignId('kelas_baru_id')->nullable()->constrained('kelas');
            $table->string('tahun_ajaran');
            $table->enum('jenis_perpindahan', ['pindah_kelas', 'kenaikan_kelas', 'kelulusan', 'mutasi_keluar']);
            $table->date('tanggal_pindah');
            $table->string('alasan')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('riwayat_kelas');
    }
};



