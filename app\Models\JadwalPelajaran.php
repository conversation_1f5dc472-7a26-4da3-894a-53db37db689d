<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JadwalPelajaran extends Model
{
    protected $table = 'jadwal_pelajaran';
    
    protected $fillable = [
        'layout_type',
        'kelas_id',
        'nama_kelas_text',
        'wali_kelas',
        'tahun_ajaran'
    ];
    
    public function kelas()
    {
        return $this->belongsTo(Kelas::class, 'kelas_id');
    }
    
    public function detailJadwal()
    {
        return $this->hasMany(DetailJadwal::class, 'jadwal_pelajaran_id');
    }
}

