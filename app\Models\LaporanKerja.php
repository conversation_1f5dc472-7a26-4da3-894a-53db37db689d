<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LaporanKerja extends Model
{
    use HasFactory;

    protected $table = 'laporan_kerja';
    
    protected $fillable = [
        'uraian_kerja',
        'waktu_mulai',
        'waktu_selesai',
        'status',
        'keterangan',
        'user_id',
    ];
    
    // Pastikan waktu_selesai bisa null
    protected $casts = [
        'waktu_mulai' => 'datetime',
        'waktu_selesai' => 'datetime',
    ];
    
    // Hapus jika ada mutator yang mengisi waktu_selesai secara otomatis
    
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
