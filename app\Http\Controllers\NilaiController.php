<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PenilaianFormatif;
use App\Models\PenilaianSumatif;
use App\Models\PenilaianProyek;
use App\Models\Kelas;
use App\Models\MataPelajaran;
use App\Models\PesertaDidik;
use Illuminate\Support\Facades\Auth;

class NilaiController extends Controller
{
    /**
     * Menampilkan dashboard nilai dengan ringkasan data penilaian
     */
    public function index()
    {
        // Ambil statistik penilaian
        $totalFormatif = PenilaianFormatif::count();
        $totalSumatif = PenilaianSumatif::count();
        $totalProyek = PenilaianProyek::count();
        
        // Ambil data kelas untuk filter
        $kelas = Kelas::all();
        $mataPelajaran = MataPelajaran::all();
        
        return view('nilai.index', compact(
            'totalFormatif',
            'totalSumatif',
            'totalProyek',
            'kelas',
            'mataPelajaran'
        ));
    }
    
    /**
     * Menampilkan laporan nilai per kelas
     */
    public function laporanKelas(Request $request)
    {
        $request->validate([
            'kelas_id' => 'required|exists:kelas,id',
            'mata_pelajaran_id' => 'required|exists:mata_pelajarans,id'
        ]);
        
        $kelas = Kelas::findOrFail($request->kelas_id);
        $mataPelajaran = MataPelajaran::findOrFail($request->mata_pelajaran_id);
        
        // Ambil semua siswa di kelas tersebut
        $siswa = PesertaDidik::where('kelas_id', $kelas->id)->get();
        
        // Ambil data penilaian untuk kelas dan mata pelajaran yang dipilih
        $nilaiFormatif = PenilaianFormatif::where('kelas_id', $kelas->id)
            ->where('mata_pelajaran_id', $mataPelajaran->id)
            ->get()
            ->groupBy('siswa_id');
            
        $nilaiSumatif = PenilaianSumatif::where('kelas_id', $kelas->id)
            ->where('mata_pelajaran_id', $mataPelajaran->id)
            ->get()
            ->groupBy('siswa_id');
        
        return view('nilai.laporan-kelas', compact(
            'kelas',
            'mataPelajaran',
            'siswa',
            'nilaiFormatif',
            'nilaiSumatif'
        ));
    }
    
    /**
     * Menampilkan laporan nilai per siswa
     */
    public function laporanSiswa(Request $request)
    {
        $request->validate([
            'siswa_id' => 'required|exists:peserta_didiks,id'
        ]);
        
        $siswa = PesertaDidik::findOrFail($request->siswa_id);
        
        // Ambil semua penilaian untuk siswa tersebut
        $nilaiFormatif = PenilaianFormatif::where('siswa_id', $siswa->id)
            ->with(['mataPelajaran', 'tujuanPembelajaran'])
            ->get()
            ->groupBy('mata_pelajaran_id');
            
        $nilaiSumatif = PenilaianSumatif::where('siswa_id', $siswa->id)
            ->with(['mataPelajaran', 'kompetensiDasar'])
            ->get()
            ->groupBy('mata_pelajaran_id');
            
        $nilaiProyek = PenilaianProyek::whereHas('penilaianSiswa', function($query) use ($siswa) {
                $query->where('siswa_id', $siswa->id);
            })
            ->with(['penilaianSiswa' => function($query) use ($siswa) {
                $query->where('siswa_id', $siswa->id);
            }])
            ->get();
        
        return view('nilai.laporan-siswa', compact(
            'siswa',
            'nilaiFormatif',
            'nilaiSumatif',
            'nilaiProyek'
        ));
    }
    
    /**
     * Menampilkan form pencarian siswa untuk laporan
     */
    public function cariSiswa()
    {
        $kelas = Kelas::all();
        return view('nilai.cari-siswa', compact('kelas'));
    }
    
    /**
     * Mendapatkan daftar siswa berdasarkan kelas (untuk AJAX)
     */
    public function getSiswaByKelas($kelasId)
    {
        $siswa = PesertaDidik::where('kelas_id', $kelasId)
            ->select('id', 'nama', 'nis')
            ->orderBy('nama')
            ->get();
            
        return response()->json($siswa);
    }
}