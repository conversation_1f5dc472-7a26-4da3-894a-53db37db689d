<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        // Hapus foreign key constraint lama
        Schema::table('cakapan_pembelajarans', function (Blueprint $table) {
            $table->dropForeign(['kompetensi_dasar_id']);
        });
        
        // Tambahkan foreign key constraint baru ke tabel kompetensi_dasar
        Schema::table('cakapan_pembelajarans', function (Blueprint $table) {
            $table->foreign('kompetensi_dasar_id')
                  ->references('id')
                  ->on('kompetensi_dasar')
                  ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        // Hapus foreign key constraint baru
        Schema::table('cakapan_pembelajarans', function (Blueprint $table) {
            $table->dropForeign(['kompetensi_dasar_id']);
        });
        
        // Kembalikan foreign key constraint lama
        Schema::table('cakapan_pembelajarans', function (Blueprint $table) {
            $table->foreign('kompetensi_dasar_id')
                  ->references('id')
                  ->on('kompetensi_dasars')
                  ->onDelete('cascade');
        });
    }
};