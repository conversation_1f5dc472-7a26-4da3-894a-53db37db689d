<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AdmWakaPermissionSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Daftar permission baru yang akan ditambahkan
        $newPermissions = [
            'view-adm-waka',
            'upload-adm-waka',
            'manage-adm-waka'
        ];

        // Buat permission baru jika belum ada
        foreach ($newPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permission ke role yang sesuai
        $rolePermissions = [
            'Kepala Sekolah' => [
                'view-adm-waka',
                'upload-adm-waka',
                'manage-adm-waka'
            ],
            'Waka Kurikulum' => [
                'view-adm-waka',
                'upload-adm-waka'
            ],
            'Waka <PERSON>' => [
                'view-adm-waka',
                'upload-adm-waka'
            ],
            'Waka Sarpras' => [
                'view-adm-waka',
                'upload-adm-waka'
            ]
        ];

        // Assign permission ke role tanpa menghapus permission yang sudah ada
        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Role::firstOrCreate(['name' => $roleName]);
            foreach ($permissions as $permission) {
                if (!$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                }
            }
        }
    }
}