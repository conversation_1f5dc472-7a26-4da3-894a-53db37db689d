@extends('adminlte::page')

@section('title', 'Data Peserta Didik Mutasi Keluar')

@section('content_header')
    <h1>Data Peserta Didik Mutasi Keluar</h1>
@stop

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Data Peserta Didik Mutasi Keluar</h3>
                        <div class="card-tools">
                            <div class="d-flex">
                                <select id="filter-unit" class="form-control mr-2">
                                    <option value="">Semua Unit</option>
                                    @foreach(\App\Models\Unit::orderBy('nama_unit')->get() as $unit)
                                        <option value="{{ $unit->nama_unit }}">{{ $unit->nama_unit }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if(session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                        
                        <table id="tabel-mutasi" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>NIS</th>
                                    <th>NISN</th>
                                    <th>Nama</th>
                                    <th>Jenis Kelamin</th>
                                    <th>Tanggal Mutasi</th>
                                    <th>Kelas Terakhir</th>
                                    <th>Sekolah Tujuan</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($mutasiKeluar as $m)
                                <tr>
                                    <td>{{ $m->nis }}</td>
                                    <td>{{ $m->nisn }}</td>
                                    <td>{{ $m->nama }}</td>
                                    <td>{{ $m->jenis_kelamin == 'L' ? 'Laki-laki' : 'Perempuan' }}</td>
                                    <td>{{ $m->tanggal_mutasi ? date('d-m-Y', strtotime($m->tanggal_mutasi)) : '-' }}</td>
                                    <td>{{ $m->kelas_terakhir ?? '-' }}</td>
                                    <td>{{ $m->sekolah_tujuan ?? '-' }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" data-toggle="modal" data-target="#detailMutasi{{ $m->id }}" data-toggle="tooltip" title="Detail">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        @can('manage-peserta-didik')
                                        <a href="{{ route('peserta-didik.mutasi.edit', $m->id) }}" class="btn btn-sm btn-warning" data-toggle="tooltip" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('peserta-didik.mutasi.destroy', $m->id) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')" data-toggle="tooltip" title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endcan
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detail Modal will be included here -->
@stop

@section('css')
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
@stop

@section('js')
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script>
        $(document).ready(function() {
            var table = $('#tabel-mutasi').DataTable({
                "responsive": true,
                "lengthChange": true,
                "autoWidth": false,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                }
            });

            // Filter based on unit
            $('#filter-unit').on('change', function() {
                var unit = $(this).val();
                table.column(5) // Adjust with the index of the class/unit column
                    .search(unit)
                    .draw();
            });
        });
    </script>
@stop