
@extends('adminlte::page')

@section('title', 'ADM Guru')

@section('content_header')
    <h1>ADM Guru</h1>
@stop

@section('css')
<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
<style>
    .keterangan-cell {
        max-width: 300px; /* Tetapkan lebar maksimum */
        min-width: 200px; /* Tetapkan lebar minimum */
        white-space: pre-wrap; /* Memungkinkan text wrap */
        overflow-y: auto; /* Scroll vertikal jika perlu */
        max-height: 75px; /* Tinggi maksimum sebelum scroll */
        padding: 10px; /* Padding untuk kenyamanan membaca */
        vertical-align: top !important; /* Tambahkan ini */
    }

    /* Style untuk parent td dari keterangan-cell */
    td .keterangan-cell {
        margin-top: 0; /* Memastikan tidak ada gap di atas */
    }

    /* Memastikan td yang berisi keterangan menggunakan vertical-align: top */
    table.table td:nth-child(3) {
        vertical-align: top !important;
    }

    .keterangan-cell::-webkit-scrollbar {
        width: 6px;
    }

    .keterangan-cell::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .keterangan-cell::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }

    .keterangan-cell::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* Tambahan style untuk search box */
    .dataTables_filter {
        margin-bottom: 15px;
    }
    
    .dataTables_filter input {
        padding: 6px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        width: 300px !important;
    }
</style>
@stop

@section('content')
<div class="container">
    <!-- Debug Info - bisa dihapus nanti -->
    <div class="alert alert-info">
        <p>Role Check: {{ auth()->user()->hasRole('Guru') ? 'Yes' : 'No' }}</p>
        <p>Current Role: {{ auth()->user()->role }}</p>
    </div>

    <!-- Gunakan satu metode pengecekan saja -->
    @if(auth()->user()->hasRole(['Guru', 'Administrator']))
        <button type="button" class="btn btn-primary mb-3" data-toggle="modal" data-target="#uploadModal">
            <i class="fas fa-upload"></i> Upload ADM
        </button>
    @endif

    <div class="card">
        <div class="card-body">
            <table class="table table-bordered" id="admTable">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Judul</th>
                        <th style="width: 300px; vertical-align: top;">Keterangan</th>
                        <th>Guru</th>
                        <th>Status</th>
                        <th>Tanggal</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @php $index = 1; @endphp
                    @foreach($admList as $adm)
                        @if(!auth()->user()->hasRole('Guru') || (auth()->user()->hasRole('Guru') && $adm->guru->id == auth()->user()->id))
                        <tr>
                            <td>{{ $index++ }}</td>
                            <td>{{ $adm->judul }}</td>
                            <td style="vertical-align: top;">
                                <div class="keterangan-cell">
                                    @if($adm->keterangan)
                                        {{ $adm->keterangan }}
                                    @else
                                        <span class="text-muted">-</span>
                                    @endif
                                </div>
                            </td>
                            <td>{{ $adm->guru->name ?? 'N/A' }}</td>
                            <td>
                                @if($adm->status == 'pending')
                                    <span class="badge badge-warning">Pending</span>
                                @elseif($adm->status == 'approved')
                                    <span class="badge badge-success">Disetujui</span>
                                @else
                                    <span class="badge badge-danger" 
                                          data-toggle="tooltip" 
                                          data-html="true"
                                          title="<strong>Ditangguhkan oleh:</strong> {{ $adm->rejector->name ?? 'N/A' }}<br>
                                                 <strong>Alasan:</strong> {{ $adm->alasan_penolakan ?? 'N/A' }}<br>
                                                 <strong>Pada:</strong> {{ $adm->rejected_at ? Carbon\Carbon::parse($adm->rejected_at)->format('d/m/Y H:i') : 'N/A' }}">
                                        Ditangguhkan
                                    </span>
                                @endif
                            </td>
                            <td>{{ $adm->created_at ? Carbon\Carbon::parse($adm->created_at)->format('d/m/Y H:i') : 'N/A' }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ route('adm.guru.view-page', ['filename' => basename($adm->file_path)]) }}" 
                                       class="btn btn-sm btn-info" 
                                       target="_blank">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    @if($adm->status == 'pending' && !auth()->user()->hasRole('Guru'))
                                        <button type="button" 
                                                class="btn btn-sm btn-success" 
                                                onclick="approveAdm('{{ route('adm.guru.approve', $adm->id) }}')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        
                                        <button type="button" 
                                                class="btn btn-sm btn-danger" 
                                                onclick="showRejectModal('{{ route('adm.guru.reject', $adm->id) }}')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endif
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Upload -->
<div class="modal fade" id="uploadModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form action="{{ route('adm.guru.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Upload ADM</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="judul">Judul ADM</label>
                        <input type="text" class="form-control" id="judul" name="judul" required>
                    </div>
                    <div class="form-group">
                        <label for="keterangan">Keterangan</label>
                        <textarea 
                            class="form-control" 
                            id="keterangan" 
                            name="keterangan" 
                            rows="3" 
                            placeholder="Masukkan keterangan tambahan tentang ADM ini..."
                        ></textarea>
                    </div>
                    <div class="form-group">
                        <label for="file">File PDF (Max: 10MB)</label>
                        <input type="file" class="form-control-file" id="file" name="file" accept="application/pdf" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Upload</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Reject -->
<div class="modal fade" id="rejectModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="rejectForm" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Tangguhkan ADM</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="alasan_penolakan">Alasan Penangguhan</label>
                        <textarea class="form-control" 
                                 id="alasan_penolakan" 
                                 name="alasan_penolakan" 
                                 rows="3" 
                                 required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Tangguhkan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('js')
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>

<script>
$(document).ready(function() {
    $('#admTable').DataTable({
        "language": {
            "search": "Pencarian:",
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total data)",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        },
        "order": [[5, "desc"]], // Urutkan berdasarkan tanggal (kolom ke-6) secara descending
        "pageLength": 10, // Jumlah data per halaman
        "columnDefs": [
            {
                "targets": [0], // Kolom nomor
                "orderable": false // Tidak bisa diurutkan
            },
            {
                "targets": [6], // Kolom aksi
                "orderable": false // Tidak bisa diurutkan
            }
        ]
    });

    // Kode JavaScript yang sudah ada (approveAdm, showRejectModal, dll)
    $('[data-toggle="tooltip"]').tooltip({
        html: true,
        container: 'body'
    });
});

function approveAdm(url) {
    if (confirm('Apakah Anda yakin ingin menyetujui ADM ini?')) {
        const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        fetch(url, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': token,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Terjadi kesalahan: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat memproses permintaan');
        });
    }
}

function showRejectModal(url) {
    $('#rejectForm').attr('action', url);
    $('#rejectModal').modal('show');
}
</script>
@stop
