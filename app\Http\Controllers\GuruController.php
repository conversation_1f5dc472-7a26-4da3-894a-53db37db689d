<?php

namespace App\Http\Controllers;

use App\Models\Guru;
use App\Models\Unit;
use App\Models\PengembanganDiri;
use App\Models\TugasTambahan;
use App\Models\TahunAjaran;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Traits\FiltersByUserUnit;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\GuruImport;
use App\Exports\GuruTemplateExport;

class GuruController extends Controller
{   use FiltersByUserUnit;

    /**
     * Menampilkan daftar guru
     */
    public function index()
    {
        $user = auth()->user();
        $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
        
        // Query dasar untuk guru
        $query = Guru::with('unit')
            ->where('status', 'Aktif')
            ->whereIn('jenis_ptk', [
                '<PERSON>',
                '<PERSON>',
                '<PERSON><PERSON>',
                '<PERSON>'
            ]);
        
        // Terapkan filter unit jika user tidak memiliki akses ke semua unit
        if (!$canViewAllUnits && $user->unit_id) {
            $query->where('unit_id', $user->unit_id);
            
            // Log untuk debugging
            Log::info('Filtering guru by unit', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'unit_id' => $user->unit_id,
                'roles' => $user->getRoleNames(),
                'query_sql' => $query->toSql(),
                'query_bindings' => $query->getBindings()
            ]);
        }
        
        $guru = $query->get();
        
        // Debug: log jumlah data yang ditemukan
        Log::info('Guru data retrieved', [
            'count' => $guru->count(),
            'user_unit_id' => $user->unit_id,
            'can_view_all_units' => $canViewAllUnits
        ]);
        
        // Ambil daftar unit untuk filter (jika diperlukan)
        $units = Unit::orderBy('nama_unit')->get();
        
        return view('gtk.guru.index', compact('guru', 'units'));
    }

    /**
     * Menyimpan data guru baru
     */
    public function store(Request $request)
    {
        //dd($request->all()); // Tempatkan di sini untuk debugging
        $validator = Validator::make($request->all(), [
            'unit_id' => 'required|exists:units,id',
            'nama' => 'required|string|max:255',
            'nik' => 'required|string|max:16|unique:guru,nik',
            'jenis_kelamin' => 'required|in:L,P',
            'tempat_lahir' => 'required|string|max:100',
            'tanggal_lahir' => 'required|date',
            'nama_ibu_kandung' => 'required|string|max:255',
            // Alamat
            'alamat' => 'required|string',
            'kelurahan' => 'required|string|max:100',
            'kecamatan' => 'required|string|max:100',
            'kabupaten' => 'required|string|max:100',
            'provinsi' => 'required|string|max:100',
            // Data pribadi
            'agama' => 'required|string|max:50',
            'npwp' => 'nullable|string|max:30',
            'nama_wajib_pajak' => 'nullable|string|max:255',
            'kewarganegaraan' => 'required|string|max:50',
            'status_kawin' => 'required|string|max:50',
            'nama_pasangan' => 'nullable|string|max:255',
            'pekerjaan_pasangan' => 'nullable|string|max:100',
            // Kepegawaian
            'status_pegawai' => 'required|string|max:50',
            'niy' => 'nullable|string|max:30',
            'nuptk' => 'nullable|string|max:30',
            'jenis_ptk' => 'required|string|max:50',
            'sk_pengangkatan' => 'nullable|string|max:50',
            'tmt_pengangkatan' => 'nullable|date',
            'lembaga_pengangkat' => 'nullable|string|max:100',
            'pangkat_golongan' => 'nullable|string|max:50',
            // Penugasan
            'sk_penugasan' => 'nullable|string|max:50',
            'tmt_penugasan' => 'nullable|date',
            'lembaga_penugasan' => 'nullable|string|max:100',
            'pangkat_golongan_penugasan' => 'nullable|string|max:50',
            // Kontak
            'no_telp' => 'nullable|string|max:15',
            'email' => 'nullable|email|max:100',
            'mata_pelajaran' => 'nullable|string|max:100',
            'status' => 'required|in:Aktif,Non-Aktif',
        ]);

        if ($validator->fails()) {
            \Log::error('Validation failed: ', $validator->errors()->toArray());
            return redirect()->route('gtk.guru.index')
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Log the request data
            \Log::info('Attempting to create guru with data: ', $request->all());
            
            // Use DB transaction for safer operation
            \DB::beginTransaction();
            // Hanya ambil data yang diperlukan untuk disimpan ke database
            $guruData = $request->only([
                'unit_id', 'nama', 'nik', 'jenis_kelamin', 'tempat_lahir', 
                'tanggal_lahir', 'nama_ibu_kandung', 'alamat', 'kelurahan', 
                'kecamatan', 'kabupaten', 'provinsi', 'agama', 'npwp', 
                'nama_wajib_pajak', 'kewarganegaraan', 'status_kawin', 
                'nama_pasangan', 'pekerjaan_pasangan', 'status_pegawai', 
                'niy', 'nuptk', 'jenis_ptk', 'sk_pengangkatan', 
                'tmt_pengangkatan', 'lembaga_pengangkat', 'pangkat_golongan', 
                'sk_penugasan', 'tmt_penugasan', 'lembaga_penugasan', 
                'pangkat_golongan_penugasan', 'no_telp', 'email', 
                'mata_pelajaran', 'status'
            ]);

            $guru = Guru::create($guruData);
            \DB::commit();
            
            \Log::info('Guru created successfully with ID: ' . $guru->id);
            
            return redirect()->route('gtk.guru.index')
                ->with('success', 'Data guru berhasil ditambahkan');
        } catch (\Exception $e) {
            \DB::rollBack();
            \Log::error('Error creating guru: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            
            return redirect()->route('gtk.guru.index')
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Menampilkan form tambah guru
     */
    public function create()
    {
        $units = Unit::orderBy('nama_unit')->get();
        return view('gtk.guru.create', compact('units'));
    }

    /**
     * Menampilkan form edit guru
     */
    public function edit($id)
    {
        $guru = Guru::findOrFail($id);
        $units = Unit::orderBy('nama_unit')->get();
        
        return view('gtk.guru.edit', compact('guru', 'units'));
    }

    /**
     * Memperbarui data guru
     */
    public function update(Request $request, $id)
    {
        try {
            \DB::beginTransaction(); // Pastikan ini ada di awal try block
            
            // Log request data untuk debugging
            \Log::info('Update guru request data for ID ' . $id . ':', $request->all());
            
            $guru = Guru::findOrFail($id);
            
            // Proses update data guru
            $guruData = $request->all(); // Atau gunakan validasi yang sesuai
            
            // Log data yang akan diupdate
            \Log::info('Updating guru with data:', $guruData);
            
            $guru->update($guruData);
            \DB::commit();
            
            \Log::info('Guru updated successfully: ' . $id);
            
            return redirect()->route('gtk.guru.index')
                ->with('success', 'Data guru berhasil diperbarui');
        } catch (\Exception $e) {
            \DB::rollBack();
            \Log::error('Error updating guru: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Hapus data guru
     */
    public function destroy($id)
    {
        try {
            $guru = Guru::findOrFail($id);
            $guru->delete();
            
            return redirect()->route('gtk.guru.index')
                ->with('success', 'Data guru berhasil dihapus');
        } catch (\Exception $e) {
            return redirect()->route('gtk.guru.index')
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }
    
    /**
     * Mendapatkan data unit untuk AJAX
     */
    public function getUnitData($unitId)
    {
        $unit = Unit::findOrFail($unitId);
        return response()->json([
            'npsn' => $unit->npsn,
            'alamat' => $unit->alamat
        ]);
    }

    /**
     * Menampilkan detail guru.
     */
    public function show(Request $request, $id)
    {
        $guru = Guru::with(['unit'])->findOrFail($id);
        
        // Ambil data pengembangan diri
        $pengembanganDiri = PengembanganDiri::where('guru_id', $id)->get();
        
        // Ambil data tugas tambahan
        $tugasTambahan = TugasTambahan::with('tahunAjaran')
                        ->where('guru_id', $id)
                        ->get();
        
        // Ambil daftar tahun ajaran untuk dropdown
        $tahunAjaranList = TahunAjaran::orderBy('aktif', 'desc')
                          ->orderBy('tanggal_mulai', 'desc')
                          ->get();
        
        // Ambil tahun ajaran aktif
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->first();
        $tahunAjaranAktifId = $tahunAjaranAktif ? $tahunAjaranAktif->id : null;
        
        // Query dasar untuk kelas
        $kelasQuery = \App\Models\Kelas::query();
        
        // Filter berdasarkan tahun ajaran aktif
        if ($tahunAjaranAktif) {
            $kelasQuery->where('tahun_ajaran', $tahunAjaranAktif->nama);
        }
        
        // Terapkan filter unit jika diperlukan
        $user = auth()->user();
        $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
        
        if (!$canViewAllUnits && $user->unit_id) {
            $kelasQuery->where('unit_id', $user->unit_id);
        }
        
        // Ambil daftar kelas
        $kelasList = $kelasQuery->orderBy('nama')->get();
        
        return view('gtk.guru.show', compact(
            'guru', 
            'pengembanganDiri', 
            'tugasTambahan', 
            'tahunAjaranList', 
            'kelasList',
            'tahunAjaranAktifId'
        ));
    }

    /**
     * Store a newly created pengembangan diri resource.
     */
    public function storePengembanganDiri(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'jenis_diklat' => 'required|string|max:255',
                'nama' => 'required|string|max:255',
                'penyelenggara' => 'required|string|max:255',
                'tingkat' => 'required|string|max:255',
                'tahun' => 'required|integer|min:1900|max:' . (date('Y') + 1),
                'peran' => 'required|string|max:255',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            // Pastikan guru ada
            $guru = Guru::findOrFail($id);

            // Buat pengembangan diri baru
            PengembanganDiri::create([
                'guru_id' => $id,
                'jenis_diklat' => $request->jenis_diklat,
                'nama' => $request->nama,
                'penyelenggara' => $request->penyelenggara,
                'tingkat' => $request->tingkat,
                'tahun' => $request->tahun,
                'peran' => $request->peran,
            ]);

            return redirect()->route('gtk.guru.show', $id)
                ->with('success', 'Data pengembangan diri berhasil ditambahkan');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Update the specified pengembangan diri resource.
     */
    public function updatePengembanganDiri(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'jenis_diklat' => 'required|string|max:255',
                'nama' => 'required|string|max:255',
                'penyelenggara' => 'required|string|max:255',
                'tingkat' => 'required|string|max:255',
                'tahun' => 'required|integer|min:1900|max:' . (date('Y') + 1),
                'peran' => 'required|string|max:255',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            // Temukan dan update pengembangan diri
            $pengembanganDiri = PengembanganDiri::findOrFail($id);
            $guruId = $pengembanganDiri->guru_id;
            
            $pengembanganDiri->update([
                'jenis_diklat' => $request->jenis_diklat,
                'nama' => $request->nama,
                'penyelenggara' => $request->penyelenggara,
                'tingkat' => $request->tingkat,
                'tahun' => $request->tahun,
                'peran' => $request->peran,
            ]);

            return redirect()->route('gtk.guru.show', $guruId)
                ->with('success', 'Data pengembangan diri berhasil diperbarui');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified pengembangan diri resource.
     */
    public function destroyPengembanganDiri($id)
    {
        try {
            $pengembanganDiri = PengembanganDiri::findOrFail($id);
            $guruId = $pengembanganDiri->guru_id; // Simpan guru_id sebelum menghapus
            $pengembanganDiri->delete();
            
            // Cek apakah request mengharapkan JSON response
            if (request()->ajax() || request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Data pengembangan diri berhasil dihapus'
                ]);
            }
            
            // Jika tidak, redirect kembali ke halaman detail guru
            return redirect()->route('gtk.guru.show', $guruId)
                ->with('success', 'Data pengembangan diri berhasil dihapus');
        } catch (\Exception $e) {
            \Log::error('Error deleting pengembangan diri: ' . $e->getMessage());
            
            // Cek apakah request mengharapkan JSON response
            if (request()->ajax() || request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan: ' . $e->getMessage()
                ], 500);
            }
            
            // Jika tidak, redirect kembali dengan pesan error
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Edit the specified pengembangan diri resource.
     */
    public function editPengembanganDiri($id)
    {
        try {
            $pengembanganDiri = PengembanganDiri::findOrFail($id);
            
            // Pastikan mengembalikan semua field yang diperlukan
            return response()->json([
                'id' => $pengembanganDiri->id,
                'guru_id' => $pengembanganDiri->guru_id,
                'jenis_diklat' => $pengembanganDiri->jenis_diklat,
                'nama' => $pengembanganDiri->nama,
                'penyelenggara' => $pengembanganDiri->penyelenggara,
                'tingkat' => $pengembanganDiri->tingkat,
                'tahun' => $pengembanganDiri->tahun,
                'peran' => $pengembanganDiri->peran
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Data tidak ditemukan: ' . $e->getMessage()], 404);
        }
    }

    /**
     * Menyimpan tugas tambahan baru untuk guru.
     */
    public function storeTugasTambahan(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'tugas_tambahan' => 'required|string|max:255',
                'tahun_ajaran_id' => 'required|exists:tahun_ajaran,id',
                'kelas' => 'nullable|string|max:255',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            // Cari guru berdasarkan ID
            $guru = Guru::findOrFail($id);
            
            // Buat tugas tambahan baru
            TugasTambahan::create([
                'guru_id' => $guru->id,
                'nama_guru' => $guru->nama,
                'tugas_tambahan' => $request->tugas_tambahan,
                'kelas' => in_array($request->tugas_tambahan, ['Wali Kelas', 'Guru Kelas']) ? $request->kelas : null,
                'tahun_ajaran_id' => $request->tahun_ajaran_id,
            ]);

            return redirect()->route('gtk.guru.show', $guru->id)
                ->with('success', 'Data tugas tambahan berhasil ditambahkan');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Memperbarui tugas tambahan yang ada.
     */
    public function updateTugasTambahan(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'tugas_tambahan' => 'required|string|max:255',
                'tahun_ajaran_id' => 'required|exists:tahun_ajaran,id',
                'kelas' => 'nullable|string|max:255',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            // Temukan dan update tugas tambahan
            $tugasTambahan = TugasTambahan::findOrFail($id);
            $guruId = $tugasTambahan->guru_id;
            
            // Pastikan nama_guru tetap konsisten dengan guru_id
            $guru = Guru::findOrFail($guruId);
            
            $tugasTambahan->update([
                'tugas_tambahan' => $request->tugas_tambahan,
                'kelas' => in_array($request->tugas_tambahan, ['Wali Kelas', 'Guru Kelas']) ? $request->kelas : null,
                'tahun_ajaran_id' => $request->tahun_ajaran_id,
                'nama_guru' => $guru->nama,
            ]);

            return redirect()->route('gtk.guru.show', $guruId)
                ->with('success', 'Data tugas tambahan berhasil diperbarui');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Menghapus tugas tambahan.
     */
    public function destroyTugasTambahan($id)
    {
        try {
            $tugasTambahan = TugasTambahan::findOrFail($id);
            $guruId = $tugasTambahan->guru_id;
            
            $tugasTambahan->delete();
            
            return redirect()->route('gtk.guru.show', $guruId)
                ->with('success', 'Data tugas tambahan berhasil dihapus');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Mendapatkan daftar kelas berdasarkan tahun ajaran
     */
    public function getKelasByTahunAjaran(Request $request)
    {
        $tahunAjaranId = $request->tahun_ajaran_id;
        
        // Ambil tahun ajaran
        $tahunAjaran = TahunAjaran::find($tahunAjaranId);
        
        if (!$tahunAjaran) {
            return response()->json(['error' => 'Tahun ajaran tidak ditemukan'], 404);
        }
        
        // Query dasar untuk kelas
        $kelasQuery = \App\Models\Kelas::where('tahun_ajaran', $tahunAjaran->nama);
        
        // Terapkan filter unit jika diperlukan
        $user = auth()->user();
        $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
        
        if (!$canViewAllUnits && $user->unit_id) {
            $kelasQuery->where('unit_id', $user->unit_id);
        }
        
        // Ambil daftar kelas
        $kelasList = $kelasQuery->orderBy('nama')->get();
        
        return response()->json($kelasList);
    }

    /**
     * Download template Excel untuk import data guru
     */
    public function exportTemplate()
    {
        try {
            // Gunakan pendekatan yang sama seperti di PesertaDidikController
            return Excel::download(new GuruTemplateExport, 'template-import-guru.xlsx');
        } catch (\Exception $e) {
            Log::error('Error exporting template: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return redirect()->route('gtk.guru.index')
                ->with('error', 'Gagal mengunduh template: ' . $e->getMessage());
        }
    }

    /**
     * Import data guru dari file Excel
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:csv,xls,xlsx|max:2048',
        ]);

        try {
            Log::info('Starting guru import process');
            
            // Simpan file untuk debugging jika diperlukan
            $file = $request->file('file');
            $path = $file->storeAs('imports', 'guru_import_' . time() . '.' . $file->getClientOriginalExtension(), 'public');
            Log::info('File stored at: ' . $path);
            
            // Import data
            Excel::import(new GuruImport, $file);
            
            Log::info('Guru import completed successfully');
            return redirect()->route('gtk.guru.index')->with('success', 'Data guru berhasil diimport!');
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            Log::error('Validation error during import: ' . json_encode($e->failures()));
            
            $failures = $e->failures();
            $errors = [];
            foreach ($failures as $failure) {
                $errors[] = 'Baris ' . $failure->row() . ': ' . implode(', ', $failure->errors());
            }
            
            return redirect()->route('gtk.guru.index')->with('error', 'Validasi gagal: ' . implode('<br>', $errors));
        } catch (\Exception $e) {
            Log::error('Error during guru import: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->route('gtk.guru.index')->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }
}












