<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('rapor_siswas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('siswa_id')->constrained('peserta_didiks')->onDelete('cascade');
            $table->foreignId('kelas_id')->constrained('kelas')->onDelete('cascade');
            $table->foreignId('tahun_ajaran_id')->constrained('tahun_ajarans')->onDelete('cascade');
            $table->string('semester'); // Ganjil/Genap
            $table->date('tanggal_rapor');
            $table->text('catatan_wali_kelas')->nullable();
            $table->integer('jumlah_kehadiran')->default(0);
            $table->integer('jumlah_ketidakhadiran')->default(0);
            $table->string('status_kenaikan')->nullable(); // Naik/Tidak Naik/Lulus
            $table->foreignId('generated_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });

        // Tabel untuk detail nilai per mata pelajaran di rapor
        Schema::create('rapor_nilai_mapels', function (Blueprint $table) {
            $table->id();
            $table->foreignId('rapor_siswa_id')->constrained('rapor_siswas')->onDelete('cascade');
            $table->foreignId('mata_pelajaran_id')->constrained('mata_pelajarans')->onDelete('cascade');
            $table->enum('nilai_pengetahuan', ['BP', 'MB', 'BSH', 'SB']);
            $table->enum('nilai_keterampilan', ['BP', 'MB', 'BSH', 'SB']);
            $table->text('deskripsi_pengetahuan');
            $table->text('deskripsi_keterampilan');
            $table->timestamps();
        });

        // Tabel untuk nilai proyek di rapor
        Schema::create('rapor_nilai_proyeks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('rapor_siswa_id')->constrained('rapor_siswas')->onDelete('cascade');
            $table->foreignId('penilaian_proyek_id')->constrained('penilaian_proyeks')->onDelete('cascade');
            $table->enum('nilai', ['BP', 'MB', 'BSH', 'SB']);
            $table->text('deskripsi');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('rapor_nilai_proyeks');
        Schema::dropIfExists('rapor_nilai_mapels');
        Schema::dropIfExists('rapor_siswas');
    }
};