<?php

namespace App\Exports\Sheets;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use Illuminate\Support\Collection;
use App\Models\JadwalPelajaran;

class JadwalPerGuruSheet implements FromCollection, WithTitle, WithHeadings, WithStyles
{
    private $jadwalGuru;
    private $namaGuru;
    private $jamSlots = [];
    private $jadwalData = [];

    public function __construct($jadwalGuru, $namaGuru)
    {
        $this->jadwalGuru = $jadwalGuru;
        $this->namaGuru = $namaGuru;
        $this->prepareData();
    }

    private function prepareData()
    {
        // Kumpulkan semua detail jadwal dari semua kelas untuk mendapatkan kegiatan khusus
        $allDetailJadwal = collect();

        // Ambil semua jadwal dari tahun ajaran yang sama
        $tahunAjaran = $this->jadwalGuru->first()->jadwalPelajaran->tahun_ajaran ?? null;
        if ($tahunAjaran) {
            $allJadwal = \App\Models\JadwalPelajaran::where('tahun_ajaran', $tahunAjaran)
                ->with(['detailJadwal'])
                ->get();

            foreach ($allJadwal as $jadwal) {
                $allDetailJadwal = $allDetailJadwal->concat($jadwal->detailJadwal);
            }
        }

        // Ambil kegiatan khusus (istirahat, upacara, kebaktian, ekstra)
        $kegiatanKhusus = $allDetailJadwal->filter(function ($detail) {
            return $detail->is_istirahat || $detail->keterangan;
        })->unique(function ($detail) {
            return $detail->hari . '-' . $detail->waktu_mulai . '-' . $detail->waktu_selesai;
        });

        // Tambahkan kegiatan khusus ke jadwal data
        foreach ($kegiatanKhusus as $detail) {
            $waktu = substr($detail->waktu_mulai, 0, 5) . '-' . substr($detail->waktu_selesai, 0, 5);
            if (!in_array($waktu, $this->jamSlots)) {
                $this->jamSlots[] = $waktu;
            }

            // Tentukan nama kegiatan
            $namaKegiatan = '';
            if ($detail->is_istirahat) {
                $namaKegiatan = 'Istirahat';
            } elseif ($detail->keterangan) {
                $namaKegiatan = ucfirst($detail->keterangan);
            }

            // Tambahkan ke jadwal data untuk hari yang sesuai
            $this->jadwalData[$detail->hari][$waktu] = (object) [
                'is_special' => true,
                'kegiatan' => $namaKegiatan,
                'waktu_mulai' => $detail->waktu_mulai,
                'waktu_selesai' => $detail->waktu_selesai
            ];
        }

        // Kumpulkan semua slot waktu dan data jadwal guru dari database
        foreach ($this->jadwalGuru as $detail) {
            $waktu = substr($detail->waktu_mulai, 0, 5) . '-' . substr($detail->waktu_selesai, 0, 5);
            if (!in_array($waktu, $this->jamSlots)) {
                $this->jamSlots[] = $waktu;
            }
            $this->jadwalData[$detail->hari][$waktu] = $detail;
        }

        // Urutkan slot waktu berdasarkan jam mulai
        usort($this->jamSlots, function($a, $b) {
            $timeA = explode('-', $a)[0];
            $timeB = explode('-', $b)[0];
            return strcmp($timeA, $timeB);
        });
    }

    public function collection()
    {
        $data = [];
        $hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];

        // Baris 1: Judul
        $data[] = ['JADWAL PELAJARAN GURU'];

        // Baris 2: Nama Guru
        $data[] = [$this->namaGuru];

        // Baris 3: Kosong untuk spacing
        $data[] = [''];

        // Baris 4: Header tabel - Jam di kolom pertama, hari-hari di kolom berikutnya
        $headerRow = ['JAM'];
        foreach ($hari as $h) {
            $headerRow[] = $h;
        }
        $data[] = $headerRow;

        // Baris data: setiap baris adalah slot waktu
        foreach ($this->jamSlots as $jam) {
            $row = [$jam]; // Kolom pertama adalah jam

            // Untuk setiap hari, cek apakah ada jadwal di jam ini
            foreach ($hari as $h) {
                if (isset($this->jadwalData[$h][$jam])) {
                    $detail = $this->jadwalData[$h][$jam];

                    // Cek apakah ini kegiatan khusus
                    if (isset($detail->is_special) && $detail->is_special) {
                        $cellContent = $detail->kegiatan;
                    } else {
                        // Jadwal pelajaran biasa
                        $cellContent = $detail->mataPelajaran->nama_mapel . "\n(" . $detail->jadwalPelajaran->nama_kelas_text . ")";
                    }
                    $row[] = $cellContent;
                } else {
                    $row[] = '-'; // Tidak ada jadwal
                }
            }
            $data[] = $row;
        }

        // Tambahkan baris kosong dan keterangan
        $data[] = [''];
        $data[] = ['KETERANGAN:'];
        $data[] = ['Biru: Jadwal Mengajar'];
        $data[] = ['Kuning: Kegiatan Khusus (Istirahat, Upacara, Kebaktian, Ekstra)'];
        $data[] = ['Abu-abu: Tidak Ada Kegiatan'];

        return new Collection($data);
    }

    public function title(): string
    {
        return substr($this->namaGuru, 0, 31);
    }

    public function headings(): array
    {
        return [];
    }

    public function styles(Worksheet $sheet)
    {
        $totalRows = 4 + count($this->jamSlots); // Header + data rows
        $totalCols = 6; // JAM + 5 hari (A sampai F)
        $lastColumn = chr(64 + $totalCols); // F

        // Style untuk judul "JADWAL PELAJARAN GURU"
        $sheet->getStyle('A1')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 16,
                'color' => ['rgb' => '2F5233']
            ]
        ]);

        // Style untuk nama guru
        $sheet->getStyle('A2')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 14,
                'color' => ['rgb' => '1F4E79']
            ]
        ]);

        // Style untuk header tabel (baris 4)
        $headerRange = "A4:{$lastColumn}4";
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
                'size' => 12
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4']
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER
            ]
        ]);

        // Mengatur lebar kolom
        $sheet->getColumnDimension('A')->setWidth(12); // Kolom JAM
        for ($i = 1; $i <= 5; $i++) { // Kolom hari (B-F)
            $col = chr(65 + $i);
            $sheet->getColumnDimension($col)->setWidth(25);
        }

        // Menggabungkan sel untuk judul dan nama guru
        $sheet->mergeCells("A1:{$lastColumn}1");
        $sheet->mergeCells("A2:{$lastColumn}2");

        // Mengatur perataan tengah untuk judul dan nama guru
        $sheet->getStyle("A1:{$lastColumn}2")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        // Style untuk kolom JAM (kolom A dari baris 5 ke bawah)
        if ($totalRows > 4) {
            $jamRange = "A5:A{$totalRows}";
            $sheet->getStyle($jamRange)->applyFromArray([
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F2F2F2']
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER
                ]
            ]);
        }

        // Style untuk sel data (B5 sampai F dan seterusnya)
        if ($totalRows > 4) {
            $dataRange = "B5:{$lastColumn}{$totalRows}";
            $sheet->getStyle($dataRange)->applyFromArray([
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                    'wrapText' => true
                ]
            ]);
        }

        // Menambahkan border untuk seluruh tabel
        $tableRange = "A4:{$lastColumn}{$totalRows}";
        $sheet->getStyle($tableRange)->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
                'outline' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => ['rgb' => '000000'],
                ]
            ],
        ]);

        // Mengatur tinggi baris untuk data agar cukup untuk text wrap
        for ($row = 5; $row <= $totalRows; $row++) {
            $sheet->getRowDimension($row)->setRowHeight(40);
        }

        // Mengatur tinggi baris header
        $sheet->getRowDimension(4)->setRowHeight(25);

        // Styling untuk keterangan/legend
        $legendStartRow = $totalRows + 2; // Baris setelah tabel + 1 baris kosong

        // Style untuk judul keterangan
        $sheet->getStyle("A{$legendStartRow}")->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12
            ]
        ]);

        // Style untuk item keterangan
        $sheet->getStyle("A" . ($legendStartRow + 1))->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => '1F4E79']
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'E7F3FF']
            ]
        ]);

        $sheet->getStyle("A" . ($legendStartRow + 2))->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'BF8F00'],
                'italic' => true
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'FFF2CC']
            ]
        ]);

        $sheet->getStyle("A" . ($legendStartRow + 3))->applyFromArray([
            'font' => [
                'color' => ['rgb' => '6C757D']
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'F8F9FA']
            ]
        ]);

        // Style khusus untuk sel yang berisi data jadwal (bukan "-")
        // Kita akan menggunakan data yang sudah kita siapkan untuk styling
        $hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];
        $rowIndex = 5; // Mulai dari baris 5 (setelah header)

        foreach ($this->jamSlots as $jam) {
            $colIndex = 2; // Mulai dari kolom B (setelah kolom JAM)

            foreach ($hari as $h) {
                $cellCoordinate = chr(64 + $colIndex) . $rowIndex;

                if (isset($this->jadwalData[$h][$jam])) {
                    $detail = $this->jadwalData[$h][$jam];

                    // Cek apakah ini kegiatan khusus
                    if (isset($detail->is_special) && $detail->is_special) {
                        // Style untuk kegiatan khusus
                        $sheet->getStyle($cellCoordinate)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'FFF2CC'] // Kuning muda
                            ],
                            'font' => [
                                'bold' => true,
                                'color' => ['rgb' => 'BF8F00'], // Kuning tua
                                'italic' => true
                            ]
                        ]);
                    } else {
                        // Style untuk jadwal pelajaran biasa
                        $sheet->getStyle($cellCoordinate)->applyFromArray([
                            'fill' => [
                                'fillType' => Fill::FILL_SOLID,
                                'startColor' => ['rgb' => 'E7F3FF'] // Biru muda
                            ],
                            'font' => [
                                'bold' => true,
                                'color' => ['rgb' => '1F4E79'] // Biru tua
                            ]
                        ]);
                    }
                } else {
                    // Tidak ada jadwal, style default untuk sel kosong
                    $sheet->getStyle($cellCoordinate)->applyFromArray([
                        'fill' => [
                            'fillType' => Fill::FILL_SOLID,
                            'startColor' => ['rgb' => 'F8F9FA'] // Abu-abu sangat terang
                        ],
                        'font' => [
                            'color' => ['rgb' => '6C757D'] // Abu-abu
                        ]
                    ]);
                }
                $colIndex++;
            }
            $rowIndex++;
        }

        return [];
    }
}
