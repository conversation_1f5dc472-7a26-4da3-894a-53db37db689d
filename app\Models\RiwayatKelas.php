<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RiwayatKelas extends Model
{
    use HasFactory;
    
    protected $table = 'riwayat_kelas';
    
    protected $fillable = [
        'siswa_id',
        'nama_pd',
        'kelas_lama_id',
        'kelas_baru_id',
        'tahun_ajaran',
        'jenis_perpindahan',
        'tanggal_pindah',
        'alasan',
        'sekolah_tujuan',
        'created_by'
    ];
    
    public function siswa()
    {
        return $this->belongsTo(PesertaDidik::class, 'siswa_id');
    }
    
    public function kelasLama()
    {
        return $this->belongsTo(Kelas::class, 'kelas_lama_id');
    }
    
    public function kelasBaru()
    {
        return $this->belongsTo(Kelas::class, 'kelas_baru_id');
    }
    
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}


