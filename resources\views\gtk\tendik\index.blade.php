@extends('layouts.admin')

@section('title', 'Data Tenaga Kependidikan')

@section('page_title', 'Data Tenaga Kependidikan')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar Tenaga Kependidikan</h3>
        <div class="card-tools">
            <a href="{{ route('gtk.tendik.create') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Tambah Tenaga Kependidikan
            </a>
        </div>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        @php
            $user = auth()->user();
            $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
        @endphp
        
        @if($canViewAllUnits)
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="filter-unit">Filter Unit:</label>
                    <select id="filter-unit" class="form-control">
                        <option value="">Semua Unit</option>
                        @foreach($units as $unit)
                            <option value="{{ $unit->nama_unit }}">{{ $unit->nama_unit }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
        @else
        <div class="alert alert-info">
            <h5><i class="icon fas fa-info"></i> Informasi Unit</h5>
            <p>Anda melihat data tenaga kependidikan dari unit: <strong>{{ $user->unit->nama_unit ?? 'Tidak ada unit' }}</strong></p>
        </div>
        @endif

        <table class="table table-bordered table-striped" id="tendikTable">
            <thead>
                <tr>
                    <th>No</th>
                    <th>NIP/NIY</th>
                    <th>Nama</th>
                    <th>Unit</th>
                    <th>Status Pegawai</th>
                    <th>Status</th>
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
                @forelse($tendik as $index => $t)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>{{ $t->nip ?? $t->niy ?? '-' }}</td>
                    <td>{{ $t->nama }}</td>
                    <td>{{ $t->unit->nama_unit ?? '-' }}</td>
                    <td>{{ $t->status_pegawai ?? '-' }}</td>
                    <td>
                        @if($t->status == 'Aktif')
                            <span class="badge badge-success">Aktif</span>
                        @else
                            <span class="badge badge-danger">Non-Aktif</span>
                        @endif
                    </td>
                    <td>
                        <a href="{{ route('gtk.tendik.show', $t->id) }}" class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i> Detail
                        </a>
                        <!-- <a href="{{ route('gtk.tendik.edit', $t->id) }}" class="btn btn-sm btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a> 

                        <form action="{{ route('gtk.tendik.destroy', $t->id) }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')">
                                <i class="fas fa-trash"></i> Hapus -->
                            </button>
                        </form>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="7" class="text-center">Tidak ada data tenaga kependidikan</td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
@endsection

@section('js')
<script>
    $(document).ready(function() {
        var table = $('#tendikTable').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
            }
        });
        
        // Filter berdasarkan unit (untuk admin, yayasan, pengawas)
        $('#filter-unit').on('change', function() {
            var unitName = $(this).val();
            table.column(3) // Kolom unit (indeks 3)
                .search(unitName)
                .draw();
        });
    });
</script>
@endsection



