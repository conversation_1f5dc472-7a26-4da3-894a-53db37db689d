@extends('adminlte::page')

@section('title', 'Pindah Kelas')

@section('content_header')
    <h1>Pindah Kelas</h1>
@stop

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Pindah Kelas Tahun Ajaran {{ $tahunAjaran->nama }}</h3>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                
                @if(session('error'))
                    <div class="alert alert-danger">{{ session('error') }}</div>
                @endif

                <!-- Form Pindah Kelas -->
                <form id="formPindahKelas" action="{{ route('rombel.reguler.pindah-kelas.massal') }}" method="POST">
                    @csrf
                    <input type="hidden" id="kelasAsalId" name="kelas_asal_id" value="">
                    
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Pilih Kelas Asal:</label>
                                <select id="kelasAsal" class="form-control select2">
                                    <option value="">-- Pilih Kelas Asal --</option>
                                    @foreach($kelas as $k)
                                        <option value="{{ $k->id }}">{{ $k->nama }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Pilih Kelas Tujuan:</label>
                                <select id="kelasTujuan" name="kelas_id" class="form-control select2" disabled>
                                    <option value="">-- Pilih Kelas Tujuan --</option>
                                    @foreach($kelas as $k)
                                        <option value="{{ $k->id }}">{{ $k->nama }}</option>
                                    @endforeach
                                </select>
                                <input type="hidden" id="kelasTujuanId" name="kelas_tujuan_id" value="">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Alasan Pindah Kelas:</label>
                                <textarea name="alasan" class="form-control" rows="1" placeholder="Masukkan alasan pindah kelas"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Daftar Siswa -->
                    <div id="daftarSiswaAsal" style="display: none;">
                        <div class="card">
                            <div class="card-header bg-primary">
                                <h3 class="card-title">Daftar Siswa</h3>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped" id="tabelSiswaAsal">
                                        <thead>
                                            <tr>
                                                <th width="5%">No</th>
                                                <th width="15%">NIS</th>
                                                <th>Nama</th>
                                                <th width="10%"><input type="checkbox" id="checkAll"> Pilih</th>
                                                <th width="15%">Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($kelas as $k)
                                                @foreach($k->siswa as $index => $s)
                                                <tr class="siswa-row" data-kelas-id="{{ $k->id }}" style="display: none;">
                                                    <td>{{ $index + 1 }}</td>
                                                    <td>{{ $s->nis ?? '-' }}</td>
                                                    <td>{{ $s->nama }}</td>
                                                    <td><input type="checkbox" name="siswa_ids[]" value="{{ $s->id }}" class="siswa-checkbox"></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary btn-pindah-individu" 
                                                                data-id="{{ $s->id }}" 
                                                                data-nama="{{ $s->nama }}">
                                                            Pindahkan
                                                        </button>
                                                    </td>
                                                </tr>
                                                @endforeach
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-success">Pindahkan Siswa Terpilih</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css" />
@stop

@section('js')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        // Inisialisasi Select2
        $('.select2').select2({
            theme: 'bootstrap4',
        });

        // Handler untuk pemilihan kelas asal
        $('#kelasAsal').change(function() {
            const kelasId = $(this).val();
            if (kelasId) {
                $('#kelasAsalId').val(kelasId);
                
                // Sembunyikan semua baris siswa
                $('.siswa-row').hide();
                
                // Tampilkan hanya siswa dari kelas yang dipilih
                $('.siswa-row[data-kelas-id="' + kelasId + '"]').show();
                
                // Tampilkan div daftar siswa
                $('#daftarSiswaAsal').show();
                
                // Enable kelas tujuan dropdown
                $('#kelasTujuan').prop('disabled', false);
                
                console.log('Kelas asal dipilih:', kelasId);
            } else {
                $('#daftarSiswaAsal').hide();
                $('#kelasTujuan').prop('disabled', true);
                $('#kelasAsalId').val('');
            }
        });

        // Handler untuk pemilihan kelas tujuan
        $('#kelasTujuan').change(function() {
            $('#kelasTujuanId').val($(this).val());
        });

        // Handler untuk checkbox "Pilih Semua"
        $('#checkAll').change(function() {
            // Hanya pilih siswa yang sedang ditampilkan (dari kelas yang dipilih)
            $('.siswa-row:visible .siswa-checkbox').prop('checked', $(this).prop('checked'));
        });

        // Validasi form pindah kelas
        $('#formPindahKelas').submit(function(e) {
            // Validasi form
            if ($('.siswa-checkbox:checked').length === 0) {
                e.preventDefault();
                alert('Silakan pilih minimal satu siswa untuk dipindahkan kelas');
                return false;
            }
            
            // Cek apakah kelas tujuan sudah dipilih
            if (!$('#kelasTujuan').val()) {
                e.preventDefault();
                alert('Silakan pilih kelas tujuan terlebih dahulu');
                return false;
            }
            
            if (!$('#kelasAsalId').val()) {
                e.preventDefault();
                alert('Silakan pilih kelas asal terlebih dahulu');
                return false;
            }
            
            return true;
        });

        // Handler untuk tombol pindah individu
        $(document).on('click', '.btn-pindah-individu', function() {
            const siswaId = $(this).data('id');
            const namaSiswa = $(this).data('nama');
            const kelasAsalId = $('#kelasAsal').val();
            const kelasTujuanId = $('#kelasTujuan').val();
            
            if (!kelasTujuanId) {
                alert('Silakan pilih kelas tujuan terlebih dahulu');
                return;
            }
            
            // Konfirmasi sebelum memindahkan
            if (confirm(`Apakah Anda yakin ingin memindahkan siswa ${namaSiswa} ke kelas tujuan?`)) {
                // Buat form dan tambahkan ke DOM
                const formHtml = `
                    <form id="singleMoveForm" action="{{ route('rombel.reguler.pindah-kelas.individu') }}" method="POST" style="display: none;">
                        @csrf
                        <input type="hidden" name="siswa_id" value="${siswaId}">
                        <input type="hidden" name="kelas_id" value="${kelasTujuanId}">
                    </form>
                `;
                
                $('body').append(formHtml);
                $('#singleMoveForm').submit();
            }
        });
    });
</script>
@stop
