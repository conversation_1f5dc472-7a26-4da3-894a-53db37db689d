@extends('adminlte::page')

@section('title', 'Daftar Penilaian Formatif')

@section('content_header')
    <h1>Daftar Penilaian Formatif</h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Penilaian Formatif</h3>
            <div class="card-tools">
                <a href="{{ route('penilaian.formatif.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Tambah Penilaian Formatif
                </a>
            </div>
        </div>
        <div class="card-body">
            <!-- Pesan Sukses -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h5><i class="icon fas fa-check"></i> Sukses!</h5>
                    {{ session('success') }}
                </div>
            @endif

            <!-- Filter -->
            <div class="row mb-3">
                <div class="col-md-8">
                    <form action="{{ route('penilaian.formatif.index') }}" method="GET" class="form-inline">
                        <select name="kelas_id" class="form-control mr-2">
                            <option value="">Semua Kelas</option>
                            @foreach($kelas as $k)
                                <option value="{{ $k->id }}" {{ request('kelas_id') == $k->id ? 'selected' : '' }}>
                                    {{ $k->nama }}
                                </option>
                            @endforeach
                        </select>
                        <select name="mata_pelajaran_id" class="form-control mr-2">
                            <option value="">Semua Mata Pelajaran</option>
                            @foreach($mataPelajaran as $mp)
                                <option value="{{ $mp->id }}" {{ request('mata_pelajaran_id') == $mp->id ? 'selected' : '' }}>
                                    {{ $mp->nama_mapel }}
                                </option>
                            @endforeach
                        </select>
                        <button type="submit" class="btn btn-primary">Filter</button>
                    </form>
                </div>
            </div>

            <!-- Tabel Penilaian Formatif -->
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="formatifTable">
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th width="15%">Tanggal</th>
                            <th width="20%">Siswa</th>
                            <th width="15%">Kelas</th>
                            <th width="15%">Mata Pelajaran</th>
                            <th width="10%">Nilai</th>
                            <th width="10%">Nilai Angka</th>
                            <th width="15%">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($penilaianFormatif as $index => $pf)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $pf->tanggal_penilaian->format('d/m/Y') }}</td>
                                <td>{{ $pf->siswa->nama }}</td>
                                <td>{{ $pf->kelas->nama_kelas }}</td>
                                <td>{{ $pf->mataPelajaran->nama_mapel }}</td>
                                <td>{{ $pf->nilai }}</td>
                                <td>{{ $pf->nilai_angka }}</td>
                                <td>
                                    <a href="{{ route('penilaian.formatif.show', $pf->id) }}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('penilaian.formatif.edit', $pf->id) }}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('penilaian.formatif.destroy', $pf->id) }}" method="POST" style="display: inline-block;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center">Tidak ada data penilaian formatif</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-3">
                {{ $penilaianFormatif->links() }}
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            // Inisialisasi Select2 untuk dropdown
            $('select').select2({
                theme: 'bootstrap4',
                width: '100%'
            });
            
            // Inisialisasi DataTables
            $('#formatifTable').DataTable({
                "paging": false, // Nonaktifkan paging karena sudah menggunakan pagination Laravel
                "lengthChange": false,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                }
            });
        });
    </script>
@stop

