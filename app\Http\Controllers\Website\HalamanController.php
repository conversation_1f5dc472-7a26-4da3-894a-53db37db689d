<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Unit;
use App\Models\Halaman;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class HalamanController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $query = Halaman::with('unit');
        
        // Jika bukan super admin, filter berdasarkan unit
        if (!$user->hasRole('Administrator')) {
            $query->where('unit_id', $user->unit_id);
        }
        
        $halaman = $query->get();
        return view('admin.website.halaman.index', compact('halaman'));
    }

    public function create()
    {
        $user = auth()->user();
        
        // Jika bukan super admin, hanya tampilkan unit user tersebut
        if ($user->hasRole('Administrator')) {
            $units = Unit::all();
        } else {
            $units = Unit::where('id', $user->unit_id)->get();
        }
        
        return view('admin.website.halaman.create', compact('units'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'judul' => 'required|string|max:255',
            'konten' => 'required',
            'unit_id' => 'required|exists:units,id',
            'tipe' => 'required|in:profil,visi-misi,sejarah,umum'
        ]);

        Halaman::create([
            'judul' => $request->judul,
            'konten' => $request->konten,
            'slug' => Str::slug($request->judul),
            'unit_id' => $request->unit_id,
            'tipe' => $request->tipe,
            'is_active' => true
        ]);

        return redirect()->route('admin.website.halaman.index')
            ->with('success', 'Halaman berhasil ditambahkan');
    }

    public function edit(Halaman $halaman)
    {
        $user = auth()->user();
        
        // Jika bukan super admin, hanya tampilkan unit user tersebut
        if ($user->hasRole('Administrator')) {
            $units = Unit::all();
        } else {
            $units = Unit::where('id', $user->unit_id)->get();
        }
        
        return view('admin.website.halaman.edit', compact('halaman', 'units'));
    }

    public function update(Request $request, Halaman $halaman)
    {
        $request->validate([
            'judul' => 'required|string|max:255',
            'konten' => 'required',
            'unit_id' => 'required|exists:units,id',
            'tipe' => 'required|in:profil,visi-misi,sejarah,umum'
        ]);

        $halaman->update([
            'judul' => $request->judul,
            'konten' => $request->konten,
            'slug' => Str::slug($request->judul),
            'unit_id' => $request->unit_id,
            'tipe' => $request->tipe,
            'is_active' => $request->has('is_active')
        ]);

        return redirect()->route('admin.website.halaman.index')
            ->with('success', 'Halaman berhasil diperbarui');
    }

    public function destroy(Halaman $halaman)
    {
        try {
            $halaman->delete();
            
            return redirect()->route('admin.website.halaman.index')
                ->with('success', 'Halaman berhasil dihapus');
        } catch (\Exception $e) {
            return redirect()->route('admin.website.halaman.index')
                ->with('error', 'Terjadi kesalahan saat menghapus halaman');
        }
    }
}
