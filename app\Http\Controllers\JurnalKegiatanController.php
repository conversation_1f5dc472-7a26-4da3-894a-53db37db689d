<?php

namespace App\Http\Controllers;

use App\Models\JurnalKegiatan;
use Illuminate\Http\Request;
use Carbon\Carbon;
use App\Traits\FiltersByUserUnit;
use Illuminate\Support\Facades\DB;

class JurnalKegiatanController extends Controller
{
    use FiltersByUserUnit;
    
    public function index()
    {
        $user = auth()->user();
        
        // Jika user adalah admin, kepala sekolah, atau waka, tampilkan semua jurnal kecuali yang approved
        if ($user->hasAnyRole(['administrator', 'admin', 'kepalasekolah', 'waka'])) {
            $query = JurnalKegiatan::with('user')
                ->where('status', '!=', 'approved'); // Exclude approved journals
                
            // Terapkan filter unit menggunakan trait
            $query = $this->applyUnitFilter($query, 'user');
            
            $jurnals = $query->orderBy('created_at', 'desc')
                ->paginate(20);
        } else {
            // Jika bukan, hanya tampilkan jurnal milik user tersebut kecuali yang approved
            $jurnals = JurnalKegiatan::where('user_id', $user->id)
                ->where('status', '!=', 'approved') // Exclude approved journals
                ->orderBy('created_at', 'desc')
                ->paginate(20);
        }
        
        return view('jurnal.index', compact('jurnals'));
    }

    public function create()
    {
        $bulanIni = Carbon::now()->startOfMonth();
        $tanggalList = [];
        
        for ($i = 0; $i < $bulanIni->daysInMonth; $i++) {
            $tanggalList[] = $bulanIni->copy()->addDays($i);
        }

        // Inisialisasi struktur data yang benar untuk jamMengajar dan jamTambahan
        $jamMengajar = [
            'jam_mengajar' => 0,
            'detail' => []
        ];
        
        $jamTambahan = [
            'jam_tambahan' => 0,
            'detail' => []
        ];
        
        return view('jurnal.create', compact('tanggalList', 'jamMengajar', 'jamTambahan'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'tanggal' => 'required|date',
            'kegiatan' => 'required|string',
            'keterangan' => 'nullable|string',
            'keterangan_jam_pengganti' => 'nullable|string',
            'jam_eskul.*.nama_eskul' => 'nullable|string',
            'jam_eskul.*.kelas' => 'nullable|string',
            'jam_eskul.*.jumlah_siswa' => 'nullable|integer|min:1',
            'jam_eskul.*.kegiatan' => 'nullable|string',
            'jam_eskul.*.jumlah_jam' => 'nullable|numeric|min:0.5',
            'jam_eskul.*.keterangan' => 'nullable|string',
            'jam_pengganti.*.kelas' => 'nullable|string',
            'jam_pengganti.*.jam_ke' => 'nullable|string',
            'jam_pengganti.*.guru_diganti' => 'nullable|string',
            'jam_pengganti.*.jumlah_jam' => 'nullable|numeric|min:0.5',
        ]);

        $user = auth()->user();

        DB::beginTransaction();
        try {
            // Simpan jurnal kegiatan
            $jurnal = JurnalKegiatan::create([
                'user_id' => $user->id,
                'unit_id' => $user->unit_id,
                'tanggal' => $request->tanggal,
                'kegiatan' => $request->kegiatan,
                'ada' => $request->has('ada'),
                'tidak' => $request->has('tidak'),
                'keterangan' => $request->keterangan,
                'keterangan_jam_pengganti' => $request->keterangan_jam_pengganti,
                'jam_tambahan' => $request->total_jam_eskul ?? 0,
                'jam_pengganti' => $request->total_jam_pengganti ?? 0,
                'status' => 'draft'
            ]);

            // Simpan data eskul jika ada
            if ($request->has('jam_eskul') && is_array($request->jam_eskul)) {
                foreach ($request->jam_eskul as $eskul) {
                    // Skip jika nama eskul kosong
                    if (empty($eskul['nama_eskul'])) continue;
                    
                    $jurnal->eskulItems()->create([
                        'nama_eskul' => $eskul['nama_eskul'],
                        'kelas' => $eskul['kelas'],
                        'jumlah_siswa' => $eskul['jumlah_siswa'],
                        'kegiatan' => $eskul['kegiatan'],
                        'jumlah_jam' => $eskul['jumlah_jam'],
                        'keterangan' => $eskul['keterangan'] ?? null,
                    ]);
                }
            }

            // Simpan data jam pengganti jika ada
            if ($request->has('jam_pengganti') && is_array($request->jam_pengganti)) {
                foreach ($request->jam_pengganti as $pengganti) {
                    // Skip jika kelas kosong
                    if (empty($pengganti['kelas'])) continue;
                    
                    $jurnal->jamPenggantiItems()->create([
                        'kelas' => $pengganti['kelas'],
                        'jam_ke' => $pengganti['jam_ke'],
                        'guru_diganti' => $pengganti['guru_diganti'],
                        'jumlah_jam' => $pengganti['jumlah_jam'],
                    ]);
                }
            }

            DB::commit();
            return redirect()->route('jurnal.index')
                ->with('success', 'Jurnal kegiatan berhasil disimpan');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function submit(Request $request)
    {
        $bulanIni = Carbon::now()->startOfMonth();
        
        JurnalKegiatan::where('user_id', auth()->id())
            ->whereYear('tanggal', $bulanIni->year)
            ->whereMonth('tanggal', $bulanIni->month)
            ->update([
                'status' => 'submitted',
                'submitted_at' => now()
            ]);

        return redirect()->route('jurnal.index')
            ->with('success', 'Jurnal kegiatan berhasil disubmit untuk persetujuan');
    }

    public function submitIndividual($id)
    {
        $jurnal = JurnalKegiatan::findOrFail($id);
        
        // Pastikan user hanya bisa submit jurnal miliknya
        if ($jurnal->user_id != auth()->id()) {
            return redirect()->route('jurnal.index')
                ->with('error', 'Anda tidak memiliki akses untuk jurnal ini');
        }
        
        // Update status jurnal
        $jurnal->update([
            'status' => 'submitted',
            'submitted_at' => now()
        ]);
        
        return redirect()->route('jurnal.index')
            ->with('success', 'Jurnal kegiatan berhasil disubmit untuk persetujuan');
    }

    /**
     * Menampilkan halaman persetujuan jurnal
     */
    public function approval()
    {
        // Ambil semua jurnal dengan status 'submitted'
        $query = \App\Models\JurnalKegiatan::where('status', 'submitted')
            ->with('user');
            
        // Terapkan filter unit menggunakan trait
        $query = $this->applyUnitFilter($query, 'user');
        
        $pendingJurnals = $query->orderBy('submitted_at', 'asc')
            ->paginate(20);
        
        return view('jurnal.approval', compact('pendingJurnals'));
    }

    /**
     * Menyetujui jurnal untuk user tertentu
     */
    public function approve($userId)
    {
        // Update semua jurnal dengan status 'submitted' untuk user tertentu
        $updated = \App\Models\JurnalKegiatan::where('user_id', $userId)
            ->where('status', 'submitted')
            ->update([
                'status' => 'approved',
                'approved_by' => auth()->id(),
                'approved_at' => now()
            ]);
        
        if ($updated) {
            return redirect()->route('jurnal.approval')
                ->with('success', 'Jurnal berhasil disetujui');
        }
        
        return redirect()->route('jurnal.approval')
            ->with('error', 'Gagal menyetujui jurnal');
    }

    /**
     * Menyetujui jurnal individual
     */
    public function approveIndividual($id)
    {
        $jurnal = JurnalKegiatan::findOrFail($id);
        
        $jurnal->update([
            'status' => 'approved',
            'approved_by' => auth()->id(),
            'approved_at' => now()
        ]);
        
        return redirect()->route('jurnal.approval')
            ->with('success', 'Jurnal berhasil disetujui');
    }

    /**
     * Menolak jurnal individual
     */
    public function rejectIndividual(Request $request, $id)
    {
        try {
            $request->validate([
                'alasan_penolakan' => 'required|string|max:255'
            ]);

            $jurnal = JurnalKegiatan::findOrFail($id);
            
            // Update status jurnal
            $jurnal->update([
                'status' => 'rejected',
                'rejected_by' => auth()->id(),
                'rejected_at' => now(),
                'alasan_penolakan' => $request->alasan_penolakan
            ]);
            
            return redirect()->route('jurnal.approval')
                ->with('success', 'Jurnal berhasil ditolak');
        } catch (\Exception $e) {
            \Log::error('Error rejecting jurnal: ' . $e->getMessage());
            return redirect()->route('jurnal.approval')
                ->with('error', 'Terjadi kesalahan saat menolak jurnal');
        }
    }

    /**
     * Menampilkan jurnal yang telah disetujui
     */
    public function approved()
    {
        $user = auth()->user();
        
        // Cek apakah user memiliki role administrator, admin, kepala sekolah, atau waka
        $isAdmin = $user->hasAnyRole(['Administrator', 'Admin', 'Kepala Sekolah', 'Waka Kurikulum']);
        
        if ($isAdmin) {
            // Admin dapat melihat semua jurnal yang disetujui
            $query = JurnalKegiatan::where('status', 'approved')
                ->with('user');
                
            // Terapkan filter unit menggunakan trait
            $query = $this->applyUnitFilter($query, 'user');
            
            $approvedJurnals = $query->orderBy('approved_at', 'desc')
                ->paginate(20);
        } else {
            // User biasa hanya melihat jurnal miliknya sendiri
            $approvedJurnals = JurnalKegiatan::where('status', 'approved')
                ->where('user_id', $user->id)
                ->orderBy('approved_at', 'desc')
                ->paginate(20);
        }
        
        return view('jurnal.approved', compact('approvedJurnals', 'isAdmin'));
    }

    /**
     * Menampilkan form untuk mengedit jurnal kegiatan
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function edit(JurnalKegiatan $jurnal)
    {
        // Pastikan user hanya bisa mengedit jurnal miliknya
        if ($jurnal->user_id != auth()->id()) {
            return redirect()->route('jurnal.index')
                ->with('error', 'Anda tidak memiliki akses untuk mengedit jurnal ini');
        }

        // Load relasi eskul
        $jurnal->load('eskulItems');
        
        return view('jurnal.edit', compact('jurnal'));
    }

    /**
     * Memperbarui jurnal kegiatan
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, JurnalKegiatan $jurnal)
    {
        $request->validate([
            'tanggal' => 'required|date',
            'kegiatan' => 'required|string',
            'keterangan' => 'nullable|string',
            'keterangan_jam_pengganti' => 'nullable|string',
            'jam_eskul.*.nama_eskul' => 'nullable|string',
            'jam_eskul.*.kelas' => 'nullable|string',
            'jam_eskul.*.jumlah_siswa' => 'nullable|integer|min:1',
            'jam_eskul.*.kegiatan' => 'nullable|string',
            'jam_eskul.*.jumlah_jam' => 'nullable|numeric|min:0.5',
            'jam_eskul.*.keterangan' => 'nullable|string',
            'jam_pengganti.*.kelas' => 'nullable|string',
            'jam_pengganti.*.jam_ke' => 'nullable|string',
            'jam_pengganti.*.guru_diganti' => 'nullable|string',
            'jam_pengganti.*.jumlah_jam' => 'nullable|numeric|min:0.5',
        ]);

        // Pastikan user hanya bisa update jurnal miliknya
        if ($jurnal->user_id != auth()->id()) {
            return redirect()->route('jurnal.index')
                ->with('error', 'Anda tidak memiliki akses untuk mengedit jurnal ini');
        }

        DB::beginTransaction();
        try {
            // Update jurnal kegiatan
            $jurnal->update([
                'tanggal' => $request->tanggal,
                'kegiatan' => $request->kegiatan,
                'ada' => $request->has('ada'),
                'tidak' => $request->has('tidak'),
                'keterangan' => $request->keterangan,
                'keterangan_jam_pengganti' => $request->keterangan_jam_pengganti,
                'jam_tambahan' => $request->total_jam_eskul ?? 0,
                'jam_pengganti' => $request->total_jam_pengganti ?? 0,
            ]);

            // Hapus semua data eskul yang lama
            $jurnal->eskulItems()->delete();

            // Simpan data eskul baru jika ada
            if ($request->has('jam_eskul') && is_array($request->jam_eskul)) {
                foreach ($request->jam_eskul as $eskul) {
                    // Skip jika nama eskul kosong
                    if (empty($eskul['nama_eskul'])) continue;
                    
                    $jurnal->eskulItems()->create([
                        'nama_eskul' => $eskul['nama_eskul'],
                        'kelas' => $eskul['kelas'],
                        'jumlah_siswa' => $eskul['jumlah_siswa'],
                        'kegiatan' => $eskul['kegiatan'],
                        'jumlah_jam' => $eskul['jumlah_jam'],
                        'keterangan' => $eskul['keterangan'] ?? null,
                    ]);
                }
            }

            // Hapus semua data jam pengganti yang lama
            $jurnal->jamPenggantiItems()->delete();

            // Simpan data jam pengganti baru jika ada
            if ($request->has('jam_pengganti') && is_array($request->jam_pengganti)) {
                foreach ($request->jam_pengganti as $pengganti) {
                    // Skip jika kelas kosong
                    if (empty($pengganti['kelas'])) continue;
                    
                    $jurnal->jamPenggantiItems()->create([
                        'kelas' => $pengganti['kelas'],
                        'jam_ke' => $pengganti['jam_ke'],
                        'guru_diganti' => $pengganti['guru_diganti'],
                        'jumlah_jam' => $pengganti['jumlah_jam'],
                    ]);
                }
            }

            DB::commit();
            return redirect()->route('jurnal.index')
                ->with('success', 'Jurnal kegiatan berhasil diperbarui');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\JurnalKegiatan  $jurnal
     * @return \Illuminate\Http\Response
     */
    public function show(JurnalKegiatan $jurnal)
    {
        // Pastikan user hanya bisa melihat jurnal dari unit mereka sendiri
        if (auth()->user()->unit_id != $jurnal->unit_id && !auth()->user()->hasRole('Administrator')) {
            abort(403, 'Unauthorized action.');
        }
        
        return view('jurnal.show', compact('jurnal'));
    }

    /**
     * Menampilkan rekap jurnal yang disetujui dengan total jam eskul
     */
    public function recap(Request $request)
    {
        $user = auth()->user();
        $isAdmin = $user->hasAnyRole(['Administrator', 'Admin', 'Kepala Sekolah', 'Waka Kurikulum']);
        $isGlobalAdmin = $user->hasRole('Administrator');
        
        // Default date range (current month)
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));
        
        // Convert to Carbon instances for queries
        $startDateCarbon = Carbon::parse($startDate);
        $endDateCarbon = Carbon::parse($endDate);
        
        // Base query for approved journals
        $baseQuery = JurnalKegiatan::where('status', 'approved')
            ->whereBetween('tanggal', [$startDateCarbon, $endDateCarbon]);
        
        // Log untuk debugging
        \Log::info('Recap base query', [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'user_role' => $user->roles->pluck('name'),
            'user_unit' => $user->unit_id,
            'is_admin' => $isAdmin,
            'is_global_admin' => $isGlobalAdmin
        ]);
        
        // Determine which users to show in the recap
        if ($isAdmin) {
            // Admin users see all journals based on their unit access
            $query = clone $baseQuery;
            
            // Apply unit filter if not global admin
            if (!$isGlobalAdmin) {
                $query = $this->applyUnitFilter($query, 'user');
            }
            
            // Get all user IDs with journals in the date range
            $userIds = $query->pluck('user_id')->unique()->toArray();
            
            // Ambil semua user yang memiliki jurnal
            $users = \App\Models\User::whereIn('id', $userIds)->get();
            
            \Log::info('Users found', [
                'count' => $users->count(),
                'user_names' => $users->pluck('name')->toArray()
            ]);
        } else {
            // Regular users only see their own journals
            $users = collect([$user]);
            \Log::info('Regular user mode', ['user_id' => $user->id, 'user_name' => $user->name]);
        }
        
        // Group journals by user
        $recapData = [];
        foreach ($users as $currentUser) {
            // Buat query baru untuk setiap user
            $userQuery = clone $baseQuery;
            $userQuery->where('user_id', $currentUser->id);
            $userJournals = $userQuery->with(['eskulItems', 'jamPenggantiItems'])->get();
            
            \Log::info("Journals for user {$currentUser->name}", [
                'count' => $userJournals->count(),
                'user_id' => $currentUser->id,
                'journal_ids' => $userJournals->pluck('id')->toArray()
            ]);
            
            // Skip users with no journals
            if ($userJournals->isEmpty()) {
                \Log::info("Skipping user {$currentUser->name} - no journals found");
                continue;
            }
            
            // Calculate total eskul hours
            $totalEskulHours = 0;
            $eskulDetails = [];
            
            foreach ($userJournals as $journal) {
                if ($journal->eskulItems && $journal->eskulItems->count() > 0) {
                    $journalEskulHours = $journal->eskulItems->sum('jumlah_jam');
                    $totalEskulHours += $journalEskulHours;
                    
                    // Group eskul items by name for detailed breakdown
                    foreach ($journal->eskulItems as $eskul) {
                        $eskulName = $eskul->nama_eskul;
                        if (!isset($eskulDetails[$eskulName])) {
                            $eskulDetails[$eskulName] = [
                                'total_jam' => 0,
                                'items' => []
                            ];
                        }
                        
                        $eskulDetails[$eskulName]['total_jam'] += $eskul->jumlah_jam;
                        $eskulDetails[$eskulName]['items'][] = [
                            'tanggal' => $journal->tanggal,
                            'kelas' => $eskul->kelas,
                            'jumlah_siswa' => $eskul->jumlah_siswa,
                            'kegiatan' => $eskul->kegiatan,
                            'jumlah_jam' => $eskul->jumlah_jam,
                            'keterangan' => $eskul->keterangan
                        ];
                    }
                }
            }
            
            // Hitung total jam pengganti
            $totalPenggantiHours = 0;
            $penggantiDetails = [];
            
            foreach ($userJournals as $journal) {
                if ($journal->jamPenggantiItems && $journal->jamPenggantiItems->count() > 0) {
                    $journalPenggantiHours = $journal->jamPenggantiItems->sum('jumlah_jam');
                    $totalPenggantiHours += $journalPenggantiHours;
                    
                    // Kumpulkan detail jam pengganti
                    foreach ($journal->jamPenggantiItems as $pengganti) {
                        $penggantiDetails[] = [
                            'tanggal' => $journal->tanggal,
                            'kelas' => $pengganti->kelas,
                            'jam_ke' => $pengganti->jam_ke,
                            'guru_diganti' => $pengganti->guru_diganti,
                            'jumlah_jam' => $pengganti->jumlah_jam
                        ];
                    }
                }
            }
            
            $recapData[] = [
                'user' => $currentUser,
                'journals' => $userJournals,
                'total_eskul_hours' => $totalEskulHours,
                'eskul_details' => $eskulDetails,
                'total_pengganti_hours' => $totalPenggantiHours,
                'pengganti_details' => $penggantiDetails
            ];
        }
        
        \Log::info('Final recap data', ['count' => count($recapData)]);
        
        return view('jurnal.recap', compact('recapData', 'startDate', 'endDate', 'isAdmin'));
    }
}



















