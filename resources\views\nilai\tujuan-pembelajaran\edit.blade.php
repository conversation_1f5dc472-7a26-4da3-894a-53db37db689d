@extends('adminlte::page')

@section('title', 'Edit Tuju<PERSON>')

@section('content_header')
    <h1>Edit Tujuan <PERSON></h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Form Edit Tujuan Pembelajaran</h3>
                </div>
                <div class="card-body">
                    <!-- Pesan Error -->
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul>
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <!-- Form Edit Tujuan Pembelajaran -->
                    <form action="{{ route('penilaian.tujuan-pembelajaran.update', $tujuanPembelajaran->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="form-group">
                            <label for="cakapan_pembelajaran_id">Cakapan Pembelajaran <span class="text-danger">*</span></label>
                            <select name="cakapan_pembelajaran_id" id="cakapan_pembelajaran_id" class="form-control @error('cakapan_pembelajaran_id') is-invalid @enderror" required>
                                <option value="">-- Pilih Cakapan Pembelajaran --</option>
                                @foreach($cakapanPembelajarans as $cp)
                                    <option value="{{ $cp->id }}" {{ (old('cakapan_pembelajaran_id') ?? $tujuanPembelajaran->cakapan_pembelajaran_id) == $cp->id ? 'selected' : '' }}>
                                        {{ $cp->deskripsi }}
                                    </option>
                                @endforeach
                            </select>
                            @error('cakapan_pembelajaran_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="deskripsi">Deskripsi Tujuan Pembelajaran <span class="text-danger">*</span></label>
                            <textarea name="deskripsi" id="deskripsi" rows="4" class="form-control @error('deskripsi') is-invalid @enderror" required>{{ old('deskripsi') ?? $tujuanPembelajaran->deskripsi }}</textarea>
                            @error('deskripsi')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                            <a href="{{ route('penilaian.tujuan-pembelajaran.index') }}" class="btn btn-secondary">Batal</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            // Jika diperlukan JavaScript tambahan
        });
    </script>
@stop