<?php

namespace App\Http\Controllers\Nilai;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\KompetensiDasar;
use App\Models\MataPelajaran;
use App\Models\Kelas;
use Illuminate\Support\Facades\Validator;

class KompetensiController extends Controller
{
    /**
     * Menampilkan daftar kompetensi dasar
     */
    public function index(Request $request)
    {
        // Filter berdasarkan mata pelajaran jika ada
        $query = KompetensiDasar::with('mataPelajaran');
        
        if ($request->has('mata_pelajaran_id') && $request->mata_pelajaran_id != '') {
            $query->where('mata_pelajaran_id', $request->mata_pelajaran_id);
        }
        
        $kompetensiDasar = $query->latest()->paginate(10);
        $mataPelajaran = MataPelajaran::all();
        
        return view('nilai.kompetensi.index', compact('kompetensiDasar', 'mataPelajaran'));
    }

    /**
     * Menampilkan form untuk membuat kompetensi dasar baru
     */
    public function create()
    {
        $mataPelajaran = MataPelajaran::all();
        $kelas = Kelas::all();
        
        return view('nilai.kompetensi.create', compact('mataPelajaran', 'kelas'));
    }

    /**
     * Menyimpan kompetensi dasar baru ke database
     */
    public function store(Request $request)
    {
        // Validasi input
        $validator = Validator::make($request->all(), [
            'mata_pelajaran_id' => 'required|exists:mata_pelajaran,id',
            'kode' => 'required|string|max:20|unique:kompetensi_dasar,kode',
            'deskripsi' => 'required|string',
            'jenis' => 'required|string|in:Pengetahuan,Keterampilan,Sikap',
            'kelas_id' => 'required|exists:kelas,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Simpan data
        KompetensiDasar::create([
            'mata_pelajaran_id' => $request->mata_pelajaran_id,
            'kode' => $request->kode,
            'deskripsi' => $request->deskripsi,
            'jenis' => $request->jenis,
            'kelas_id' => $request->kelas_id,
        ]);

        return redirect()->route('penilaian.kompetensi.index')
            ->with('success', 'Kompetensi Dasar berhasil ditambahkan!');
    }

    /**
     * Menampilkan form untuk mengedit kompetensi dasar
     */
    public function edit($id)
    {
        $kompetensiDasar = KompetensiDasar::findOrFail($id);
        $mataPelajaran = MataPelajaran::all();
        $kelas = Kelas::all();
        
        return view('nilai.kompetensi.edit', compact('kompetensiDasar', 'mataPelajaran', 'kelas'));
    }

    /**
     * Memperbarui data kompetensi dasar
     */
    public function update(Request $request, $id)
    {
        $kompetensiDasar = KompetensiDasar::findOrFail($id);
        
        // Validasi input
        $validator = Validator::make($request->all(), [
            'mata_pelajaran_id' => 'required|exists:mata_pelajaran,id',
            'kode' => 'required|string|max:20|unique:kompetensi_dasar,kode,' . $id,
            'deskripsi' => 'required|string',
            'jenis' => 'required|string|in:Pengetahuan,Keterampilan,Sikap',
            'kelas_id' => 'required|exists:kelas,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update data
        $kompetensiDasar->update([
            'mata_pelajaran_id' => $request->mata_pelajaran_id,
            'kode' => $request->kode,
            'deskripsi' => $request->deskripsi,
            'jenis' => $request->jenis,
            'kelas_id' => $request->kelas_id,
        ]);

        return redirect()->route('penilaian.kompetensi.index')
            ->with('success', 'Kompetensi Dasar berhasil diperbarui!');
    }

    /**
     * Menghapus kompetensi dasar
     */
    public function destroy($id)
    {
        $kompetensiDasar = KompetensiDasar::findOrFail($id);
        $kompetensiDasar->delete();

        return redirect()->route('penilaian.kompetensi.index')
            ->with('success', 'Kompetensi Dasar berhasil dihapus!');
    }
}