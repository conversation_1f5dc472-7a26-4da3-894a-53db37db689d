@extends('layouts.admin')

@section('title', 'Edit Jurnal Kegiatan')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Edit Jurnal Kegiatan</h3>
    </div>
    <div class="card-body">
        <form action="{{ route('jurnal.update', $jurnal->id) }}" method="POST">
            @csrf
            @method('PUT')
            
            <!-- Tambahkan informasi unit -->
            <div class="form-group">
                <label>Unit</label>
                <input type="text" class="form-control" value="{{ $jurnal->unit->nama_unit ?? auth()->user()->unit->nama_unit ?? '-' }}" disabled>
                <small class="form-text text-muted">Unit akan otomatis terisi berdasarkan unit Anda</small>
            </div>
            
            <div class="form-group">
                <label for="tanggal">Tanggal</label>
                <input type="date" class="form-control @error('tanggal') is-invalid @enderror" 
                       id="tanggal" 
                       name="tanggal" 
                       value="{{ old('tanggal', ($jurnal->tanggal ? $jurnal->tanggal->format('Y-m-d') : '')) }}" 
                       required>
                @error('tanggal')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label for="kegiatan">Kegiatan</label>
                <textarea class="form-control @error('kegiatan') is-invalid @enderror" id="kegiatan" name="kegiatan" rows="5" required>{{ old('kegiatan', $jurnal->kegiatan) }}</textarea>
                @error('kegiatan')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label>Kehadiran</label>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="ada" name="ada" value="1" {{ old('ada', $jurnal->ada) ? 'checked' : '' }}>
                    <label class="form-check-label" for="ada">Ada</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="tidak" name="tidak" value="1" {{ old('tidak', $jurnal->tidak) ? 'checked' : '' }}>
                    <label class="form-check-label" for="tidak">Tidak</label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="keterangan">Keterangan</label>
                <textarea class="form-control @error('keterangan') is-invalid @enderror" id="keterangan" name="keterangan" rows="3">{{ old('keterangan', $jurnal->keterangan) }}</textarea>
                @error('keterangan')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            
            <!-- Bagian untuk menampilkan data eskul yang sudah ada -->
            <div class="card mb-3">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Jam Eskul</h5>
                    <button type="button" class="btn btn-sm btn-light" id="tambahJamEskul">
                        <i class="fas fa-plus"></i> Tambah Jam Eskul
                    </button>
                </div>
                <div class="card-body" id="jamEskulContainer">
                    <div class="alert alert-info" id="noJamEskulAlert" style="{{ $jurnal->eskulItems->count() > 0 ? 'display:none' : '' }}">
                        Tidak ada jam eskul.
                    </div>
                    
                    <div class="table-responsive" id="jamEskulTableContainer" style="{{ $jurnal->eskulItems->count() > 0 ? '' : 'display:none' }}">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Nama Eskul</th>
                                    <th>Kelas</th>
                                    <th>Jumlah Siswa</th>
                                    <th>Kegiatan</th>
                                    <th>Jumlah Jam</th>
                                    <th>Keterangan</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="jamEskulBody">
                                @foreach($jurnal->eskulItems as $index => $eskul)
                                <tr>
                                    <td>
                                        <input type="text" name="jam_eskul[{{ $index }}][nama_eskul]" class="form-control" value="{{ $eskul->nama_eskul }}" required>
                                    </td>
                                    <td>
                                        <input type="text" name="jam_eskul[{{ $index }}][kelas]" class="form-control" value="{{ $eskul->kelas }}" required>
                                    </td>
                                    <td>
                                        <input type="number" name="jam_eskul[{{ $index }}][jumlah_siswa]" class="form-control" min="1" value="{{ $eskul->jumlah_siswa }}" required>
                                    </td>
                                    <td>
                                        <input type="text" name="jam_eskul[{{ $index }}][kegiatan]" class="form-control" value="{{ $eskul->kegiatan }}" required>
                                    </td>
                                    <td>
                                        <input type="number" name="jam_eskul[{{ $index }}][jumlah_jam]" class="form-control jam-eskul-durasi" min="0.5" step="0.5" value="{{ $eskul->jumlah_jam }}" required>
                                    </td>
                                    <td>
                                        <input type="text" name="jam_eskul[{{ $index }}][keterangan]" class="form-control" value="{{ $eskul->keterangan }}" placeholder="Keterangan jam pengganti">
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-danger hapus-jam-eskul">
                                            <i class="fas fa-trash"></i> Hapus
                                        </button>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr class="bg-light font-weight-bold">
                                    <td colspan="4" class="text-right">Total Jam Eskul:</td>
                                    <td id="totalJamEskul">{{ $jurnal->jam_tambahan }}</td>
                                    <td colspan="2"></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <input type="hidden" name="total_jam_eskul" value="{{ $jurnal->jam_tambahan }}">
                </div>
            </div>
            
            <!-- Bagian untuk menampilkan data jam pengganti yang sudah ada -->
            <div class="card mb-3">
                <div class="card-header bg-warning text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Jam Pengganti</h5>
                    <button type="button" class="btn btn-sm btn-light" id="tambahJamPengganti">
                        <i class="fas fa-plus"></i> Tambah Jam Pengganti
                    </button>
                </div>
                <div class="card-body" id="jamPenggantiContainer">
                    <div class="alert alert-info" id="noJamPenggantiAlert" style="{{ isset($jurnal->penggantiItems) && $jurnal->penggantiItems->count() > 0 ? 'display:none' : '' }}">
                        Tidak ada jam pengganti.
                    </div>
                    
                    <div class="table-responsive" id="jamPenggantiTableContainer" style="{{ isset($jurnal->penggantiItems) && $jurnal->penggantiItems->count() > 0 ? '' : 'display:none' }}">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Kelas</th>
                                    <th>Jam Ke</th>
                                    <th>Guru Diganti</th>
                                    <th>Jumlah Jam</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="jamPenggantiBody">
                                @if(isset($jurnal->penggantiItems) && $jurnal->penggantiItems->count() > 0)
                                    @foreach($jurnal->penggantiItems as $index => $pengganti)
                                    <tr>
                                        <td>
                                            <input type="text" name="jam_pengganti[{{ $index }}][kelas]" class="form-control" value="{{ $pengganti->kelas }}" required>
                                        </td>
                                        <td>
                                            <input type="text" name="jam_pengganti[{{ $index }}][jam_ke]" class="form-control" value="{{ $pengganti->jam_ke }}" required>
                                        </td>
                                        <td>
                                            <input type="text" name="jam_pengganti[{{ $index }}][guru_diganti]" class="form-control" value="{{ $pengganti->guru_diganti }}" required>
                                        </td>
                                        <td>
                                            <input type="number" name="jam_pengganti[{{ $index }}][jumlah_jam]" class="form-control jam-pengganti-durasi" min="0.5" step="0.5" value="{{ $pengganti->jumlah_jam }}" required>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-danger hapus-jam-pengganti">
                                                <i class="fas fa-trash"></i> Hapus
                                            </button>
                                        </td>
                                    </tr>
                                    @endforeach
                                @endif
                            </tbody>
                            <tfoot>
                                <tr class="bg-light font-weight-bold">
                                    <td colspan="3" class="text-right">Total Jam Pengganti:</td>
                                    <td id="totalJamPengganti">{{ $jurnal->jam_pengganti }}</td>
                                    <td></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <input type="hidden" name="total_jam_pengganti" value="{{ $jurnal->jam_pengganti }}">
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
            <a href="{{ route('jurnal.index') }}" class="btn btn-secondary">Kembali</a>
        </form>
    </div>
</div>
@endsection

@section('js')
<script>
    $(document).ready(function() {
        // Fungsi untuk menghitung total jam eskul
        function hitungTotalJamEskul() {
            let total = 0;
            $('.jam-eskul-durasi').each(function() {
                const nilai = parseFloat($(this).val()) || 0;
                total += nilai;
            });
            $('#totalJamEskul').text(total.toFixed(1));
            $('input[name="total_jam_eskul"]').val(total);
        }

        // Hitung total saat halaman dimuat
        hitungTotalJamEskul();

        // Hitung ulang saat nilai jam berubah
        $(document).on('change', '.jam-eskul-durasi', function() {
            hitungTotalJamEskul();
        });

        // Tambah baris jam eskul
        $('#tambahJamEskul').click(function() {
            const index = $('#jamEskulBody tr').length;
            const newRow = `
                <tr>
                    <td>
                        <input type="text" name="jam_eskul[${index}][nama_eskul]" class="form-control" required>
                    </td>
                    <td>
                        <input type="text" name="jam_eskul[${index}][kelas]" class="form-control" required>
                    </td>
                    <td>
                        <input type="number" name="jam_eskul[${index}][jumlah_siswa]" class="form-control" min="1" value="1" required>
                    </td>
                    <td>
                        <input type="text" name="jam_eskul[${index}][kegiatan]" class="form-control" required>
                    </td>
                    <td>
                        <input type="number" name="jam_eskul[${index}][jumlah_jam]" class="form-control jam-eskul-durasi" min="0.5" step="0.5" value="1" required>
                    </td>
                    <td>
                        <input type="text" name="jam_eskul[${index}][keterangan]" class="form-control" placeholder="Keterangan jam pengganti">
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger hapus-jam-eskul">
                            <i class="fas fa-trash"></i> Hapus
                        </button>
                    </td>
                </tr>
            `;
            $('#jamEskulBody').append(newRow);
            $('#noJamEskulAlert').hide();
            $('#jamEskulTableContainer').show();
            hitungTotalJamEskul();
        });

        // Hapus baris jam eskul
        $(document).on('click', '.hapus-jam-eskul', function() {
            $(this).closest('tr').remove();
            hitungTotalJamEskul();
            
            if ($('#jamEskulBody tr').length === 0) {
                $('#noJamEskulAlert').show();
                $('#jamEskulTableContainer').hide();
            }
        });
    });
</script>
<script>
$(document).ready(function() {
    // Hapus semua event handler yang ada pada tombol
    $('#tambahJamPengganti').off('click');
    
    // Tambah baris jam pengganti (hanya satu handler)
    $('#tambahJamPengganti').click(function() {
        const index = $('#jamPenggantiBody tr').length;
        const newRow = `
            <tr>
                <td>
                    <input type="text" name="jam_pengganti[${index}][kelas]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][jam_ke]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][guru_diganti]" class="form-control" required>
                </td>
                <td>
                    <input type="number" name="jam_pengganti[${index}][jumlah_jam]" class="form-control jam-pengganti-durasi" min="0.5" step="0.5" value="1" required>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger hapus-jam-pengganti">
                        <i class="fas fa-trash"></i> Hapus
                    </button>
                </td>
            </tr>
        `;
        $('#jamPenggantiBody').append(newRow);
        $('#noJamPenggantiAlert').hide();
        $('#jamPenggantiTableContainer').show();
        hitungTotalJamPengganti();
    });
    
    // Hapus baris jam pengganti
    $(document).on('click', '.hapus-jam-pengganti', function() {
        $(this).closest('tr').remove();
        if ($('#jamPenggantiBody tr').length === 0) {
            $('#noJamPenggantiAlert').show();
            $('#jamPenggantiTableContainer').hide();
        }
        hitungTotalJamPengganti();
    });
    
    // Function to calculate total jam pengganti
    function hitungTotalJamPengganti() {
        let total = 0;
        $('.jam-pengganti-durasi').each(function() {
            total += parseFloat($(this).val()) || 0;
        });
        $('#totalJamPengganti').text(total.toFixed(1));
        $('input[name="total_jam_pengganti"]').val(total.toFixed(1));
    }
    
    // Calculate initial totals
    hitungTotalJamPengganti();
    
    // Update totals when values change
    $(document).on('change', '.jam-pengganti-durasi', function() {
        hitungTotalJamPengganti();
    });
});
</script>
<!-- batas hapus script jam penggati 
<script>
$(document).ready(function() {
    // Hapus semua event handler yang ada pada tombol
    $('#tambahJamPengganti').off('click');
    
    // Tambah baris jam pengganti (hanya satu handler)
    $('#tambahJamPengganti').click(function() {
        const index = $('#jamPenggantiBody tr').length;
        const newRow = `
            <tr>
                <td>
                    <input type="text" name="jam_pengganti[${index}][kelas]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][jam_ke]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][guru_diganti]" class="form-control" required>
                </td>
                <td>
                    <input type="number" name="jam_pengganti[${index}][jumlah_jam]" class="form-control jam-pengganti-durasi" min="0.5" step="0.5" value="1" required>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger hapus-jam-pengganti">
                        <i class="fas fa-trash"></i> Hapus
                    </button>
                </td>
            </tr>
        `;
        $('#jamPenggantiBody').append(newRow);
        $('#noJamPenggantiAlert').hide();
        $('#jamPenggantiTableContainer').show();
        hitungTotalJamPengganti();
    });
    
    // Hapus baris jam pengganti
    $(document).on('click', '.hapus-jam-pengganti', function() {
        $(this).closest('tr').remove();
        if ($('#jamPenggantiBody tr').length === 0) {
            $('#noJamPenggantiAlert').show();
            $('#jamPenggantiTableContainer').hide();
        }
        hitungTotalJamPengganti();
    });
    
    // Function to calculate total jam pengganti
    function hitungTotalJamPengganti() {
        let total = 0;
        $('.jam-pengganti-durasi').each(function() {
            total += parseFloat($(this).val()) || 0;
        });
        $('#totalJamPengganti').text(total.toFixed(1));
        $('input[name="total_jam_pengganti"]').val(total.toFixed(1));
    }
    
    // Calculate initial totals
    hitungTotalJamPengganti();
    
    // Update totals when values change
    $(document).on('change', '.jam-pengganti-durasi', function() {
        hitungTotalJamPengganti();
    });
});
</script>
<script>
$(document).ready(function() {
    // Hapus semua event handler yang ada pada tombol
    $('#tambahJamPengganti').off('click');
    
    // Tambah baris jam pengganti (hanya satu handler)
    $('#tambahJamPengganti').click(function() {
        const index = $('#jamPenggantiBody tr').length;
        const newRow = `
            <tr>
                <td>
                    <input type="text" name="jam_pengganti[${index}][kelas]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][jam_ke]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][guru_diganti]" class="form-control" required>
                </td>
                <td>
                    <input type="number" name="jam_pengganti[${index}][jumlah_jam]" class="form-control jam-pengganti-durasi" min="0.5" step="0.5" value="1" required>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger hapus-jam-pengganti">
                        <i class="fas fa-trash"></i> Hapus
                    </button>
                </td>
            </tr>
        `;
        $('#jamPenggantiBody').append(newRow);
        $('#noJamPenggantiAlert').hide();
        $('#jamPenggantiTableContainer').show();
        hitungTotalJamPengganti();
    });
    
    // Hapus baris jam pengganti
    $(document).on('click', '.hapus-jam-pengganti', function() {
        $(this).closest('tr').remove();
        if ($('#jamPenggantiBody tr').length === 0) {
            $('#noJamPenggantiAlert').show();
            $('#jamPenggantiTableContainer').hide();
        }
        hitungTotalJamPengganti();
    });
    
    // Function to calculate total jam pengganti
    function hitungTotalJamPengganti() {
        let total = 0;
        $('.jam-pengganti-durasi').each(function() {
            total += parseFloat($(this).val()) || 0;
        });
        $('#totalJamPengganti').text(total.toFixed(1));
        $('input[name="total_jam_pengganti"]').val(total.toFixed(1));
    }
    
    // Calculate initial totals
    hitungTotalJamPengganti();
    
    // Update totals when values change
    $(document).on('change', '.jam-pengganti-durasi', function() {
        hitungTotalJamPengganti();
    });
});
</script>
<script>
$(document).ready(function() {
    // Hapus semua event handler yang ada pada tombol
    $('#tambahJamPengganti').off('click');
    
    // Tambah baris jam pengganti (hanya satu handler)
    $('#tambahJamPengganti').click(function() {
        const index = $('#jamPenggantiBody tr').length;
        const newRow = `
            <tr>
                <td>
                    <input type="text" name="jam_pengganti[${index}][kelas]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][jam_ke]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][guru_diganti]" class="form-control" required>
                </td>
                <td>
                    <input type="number" name="jam_pengganti[${index}][jumlah_jam]" class="form-control jam-pengganti-durasi" min="0.5" step="0.5" value="1" required>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger hapus-jam-pengganti">
                        <i class="fas fa-trash"></i> Hapus
                    </button>
                </td>
            </tr>
        `;
        $('#jamPenggantiBody').append(newRow);
        $('#noJamPenggantiAlert').hide();
        $('#jamPenggantiTableContainer').show();
        hitungTotalJamPengganti();
    });
    
    // Hapus baris jam pengganti
    $(document).on('click', '.hapus-jam-pengganti', function() {
        $(this).closest('tr').remove();
        if ($('#jamPenggantiBody tr').length === 0) {
            $('#noJamPenggantiAlert').show();
            $('#jamPenggantiTableContainer').hide();
        }
        hitungTotalJamPengganti();
    });
    
    // Function to calculate total jam pengganti
    function hitungTotalJamPengganti() {
        let total = 0;
        $('.jam-pengganti-durasi').each(function() {
            total += parseFloat($(this).val()) || 0;
        });
        $('#totalJamPengganti').text(total.toFixed(1));
        $('input[name="total_jam_pengganti"]').val(total.toFixed(1));
    }
    
    // Calculate initial totals
    hitungTotalJamPengganti();
    
    // Update totals when values change
    $(document).on('change', '.jam-pengganti-durasi', function() {
        hitungTotalJamPengganti();
    });
});
</script>
<script>
$(document).ready(function() {
    // Hapus semua event handler yang ada pada tombol
    $('#tambahJamPengganti').off('click');
    
    // Tambah baris jam pengganti (hanya satu handler)
    $('#tambahJamPengganti').click(function() {
        const index = $('#jamPenggantiBody tr').length;
        const newRow = `
            <tr>
                <td>
                    <input type="text" name="jam_pengganti[${index}][kelas]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][jam_ke]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][guru_diganti]" class="form-control" required>
                </td>
                <td>
                    <input type="number" name="jam_pengganti[${index}][jumlah_jam]" class="form-control jam-pengganti-durasi" min="0.5" step="0.5" value="1" required>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger hapus-jam-pengganti">
                        <i class="fas fa-trash"></i> Hapus
                    </button>
                </td>
            </tr>
        `;
        $('#jamPenggantiBody').append(newRow);
        $('#noJamPenggantiAlert').hide();
        $('#jamPenggantiTableContainer').show();
        hitungTotalJamPengganti();
    });
    
    // Hapus baris jam pengganti
    $(document).on('click', '.hapus-jam-pengganti', function() {
        $(this).closest('tr').remove();
        if ($('#jamPenggantiBody tr').length === 0) {
            $('#noJamPenggantiAlert').show();
            $('#jamPenggantiTableContainer').hide();
        }
        hitungTotalJamPengganti();
    });
    
    // Function to calculate total jam pengganti
    function hitungTotalJamPengganti() {
        let total = 0;
        $('.jam-pengganti-durasi').each(function() {
            total += parseFloat($(this).val()) || 0;
        });
        $('#totalJamPengganti').text(total.toFixed(1));
        $('input[name="total_jam_pengganti"]').val(total.toFixed(1));
    }
    
    // Calculate initial totals
    hitungTotalJamPengganti();
    
    // Update totals when values change
    $(document).on('change', '.jam-pengganti-durasi', function() {
        hitungTotalJamPengganti();
    });
});
</script>
<script>
$(document).ready(function() {
    // Hapus semua event handler yang ada pada tombol
    $('#tambahJamPengganti').off('click');
    
    // Tambah baris jam pengganti (hanya satu handler)
    $('#tambahJamPengganti').click(function() {
        const index = $('#jamPenggantiBody tr').length;
        const newRow = `
            <tr>
                <td>
                    <input type="text" name="jam_pengganti[${index}][kelas]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][jam_ke]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][guru_diganti]" class="form-control" required>
                </td>
                <td>
                    <input type="number" name="jam_pengganti[${index}][jumlah_jam]" class="form-control jam-pengganti-durasi" min="0.5" step="0.5" value="1" required>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger hapus-jam-pengganti">
                        <i class="fas fa-trash"></i> Hapus
                    </button>
                </td>
            </tr>
        `;
        $('#jamPenggantiBody').append(newRow);
        $('#noJamPenggantiAlert').hide();
        $('#jamPenggantiTableContainer').show();
        hitungTotalJamPengganti();
    });
    
    // Hapus baris jam pengganti
    $(document).on('click', '.hapus-jam-pengganti', function() {
        $(this).closest('tr').remove();
        if ($('#jamPenggantiBody tr').length === 0) {
            $('#noJamPenggantiAlert').show();
            $('#jamPenggantiTableContainer').hide();
        }
        hitungTotalJamPengganti();
    });
    
    // Function to calculate total jam pengganti
    function hitungTotalJamPengganti() {
        let total = 0;
        $('.jam-pengganti-durasi').each(function() {
            total += parseFloat($(this).val()) || 0;
        });
        $('#totalJamPengganti').text(total.toFixed(1));
        $('input[name="total_jam_pengganti"]').val(total.toFixed(1));
    }
    
    // Calculate initial totals
    hitungTotalJamPengganti();
    
    // Update totals when values change
    $(document).on('change', '.jam-pengganti-durasi', function() {
        hitungTotalJamPengganti();
    });
});
</script>
<script>
$(document).ready(function() {
    // Hapus semua event handler yang ada pada tombol
    $('#tambahJamPengganti').off('click');
    
    // Tambah baris jam pengganti (hanya satu handler)
    $('#tambahJamPengganti').click(function() {
        const index = $('#jamPenggantiBody tr').length;
        const newRow = `
            <tr>
                <td>
                    <input type="text" name="jam_pengganti[${index}][kelas]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][jam_ke]" class="form-control" required>
                </td>
                <td>
                    <input type="text" name="jam_pengganti[${index}][guru_diganti]" class="form-control" required>
                </td>
                <td>
                    <input type="number" name="jam_pengganti[${index}][jumlah_jam]" class="form-control jam-pengganti-durasi" min="0.5" step="0.5" value="1" required>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-danger hapus-jam-pengganti">
                        <i class="fas fa-trash"></i> Hapus
                    </button>
                </td>
            </tr>
        `;
        $('#jamPenggantiBody').append(newRow);
        $('#noJamPenggantiAlert').hide();
        $('#jamPenggantiTableContainer').show();
        hitungTotalJamPengganti();
    });
    
    // Hapus baris jam pengganti
    $(document).on('click', '.hapus-jam-pengganti', function() {
        $(this).closest('tr').remove();
        if ($('#jamPenggantiBody tr').length === 0) {
            $('#noJamPenggantiAlert').show();
            $('#jamPenggantiTableContainer').hide();
        }
        hitungTotalJamPengganti();
    });
    
    // Function to calculate total jam pengganti
    function hitungTotalJamPengganti() {
        let total = 0;
        $('.jam-pengganti-durasi').each(function() {
            total += parseFloat($(this).val()) || 0;
        });
        $('#totalJamPengganti').text(total.toFixed(1));
        $('input[name="total_jam_pengganti"]').val(total.toFixed(1));
    }
    
    // Calculate initial totals
    hitungTotalJamPengganti();
    
    // Update totals when values change
    $(document).on('change', '.jam-pengganti-durasi', function() {
        hitungTotalJamPengganti();
    });
});
</script>
-->
<script>
    $(document).ready(function() {
        // Fungsi untuk menghitung total jam pengganti
        function hitungTotalJamPengganti() {
            let total = 0;
            $('.jam-pengganti-durasi').each(function() {
                const nilai = parseFloat($(this).val()) || 0;
                total += nilai;
            });
            $('#totalJamPengganti').text(total.toFixed(1));
            $('input[name="total_jam_pengganti"]').val(total);
        }

        // Hitung total saat halaman dimuat
        hitungTotalJamPengganti();

        // Hitung ulang saat nilai jam berubah
        $(document).on('change', '.jam-pengganti-durasi', function() {
            hitungTotalJamPengganti();
        });

        // Tambah baris jam pengganti
        $('#tambahJamPengganti').click(function() {
            const index =
                        <input type="number" name="jam_pengganti[${index}][jumlah_jam]" class="form-control jam-pengganti-durasi" min="0.5" step="0.5" value="1" required>
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger hapus-jam-pengganti">
                            <i class="fas fa-trash"></i> Hapus
                        </button>
                    </td>
                </tr>
            `;
            $('#jamPenggantiBody').append(newRow);
            $('#noJamPenggantiAlert').hide();
            $('#jamPenggantiTableContainer').show();
            hitungTotalJamPengganti();
        });

        // Hapus baris jam pengganti
        $(document).on('click', '.hapus-jam-pengganti', function() {
            $(this).closest('tr').remove();
            hitungTotalJamPengganti();
            
            if ($('#jamPenggantiBody tr').length === 0) {
                $('#noJamPenggantiAlert').show();
                $('#jamPenggantiTableContainer').hide();
            }
        });
    });
</script>
<script>
    $(document).ready(function() {
        // Fungsi untuk menghitung total jam pengganti
        function hitungTotalJamPengganti() {
            let total = 0;
            $('.jam-pengganti-durasi').each(function() {
                const nilai = parseFloat($(this).val()) || 0;
                total += nilai;
            });
            $('#totalJamPengganti').text(total.toFixed(1));
            $('input[name="total_jam_pengganti"]').val(total);
        }

        // Hitung total saat halaman dimuat
        hitungTotalJamPengganti();

        // Hitung ulang saat nilai jam berubah
        $(document).on('change', '.jam-pengganti-durasi', function() {
            hitungTotalJamPengganti();
        });

        // Tambah baris jam pengganti
        $('#tambahJamPengganti').click(function() {
            const index = $('#jamPenggantiBody tr').length;
            const newRow = `
                <tr>
                    <td>
                        <input type="text" name="
</script>

<script>
    $(document).ready(function() {
        // Fungsi untuk menghitung total jam pengganti
        function hitungTotalJamPengganti() {
            let total = 0;
            $('.jam-pengganti-durasi').each(function() {
                const nilai = parseFloat($(this).val()) || 0;
                total += nilai;
            });
            $('#totalJamPengganti').text(total.toFixed(1));
            $('input[name="total_jam_pengganti"]').val(total);
        }

        // Hitung total saat halaman dimuat
        hitungTotalJamPengganti();

        // Hitung ulang saat nilai jam berubah
        $(document).on('change', '.jam-pengganti-durasi', function() {
            hitungTotalJamPengganti();
        });

        // Tambah baris jam pengganti
        $('#tambahJamPengganti').click(function() {
            const index = $('#jamPenggantiBody tr').length;
            const newRow = `
                <tr>
                    <td>
                        <input type="text" name="jam_pengganti[${index}][kelas]" class="form-control" required>
                    </td>
                    <td>
                        <input type="text" name="jam_pengganti[${index}][jam_ke]" class="form-control" required>
                    </td>
                    <td>
                        <input type="text" name="jam_pengganti[${index}][guru_diganti]" class="form-control" required>
                    </td>
                    <td>
                        <input type="number" name="jam_pengganti[${index}][jumlah_jam]" class="form-control jam-pengganti-durasi" min="0.5" step="0.5" value="1" required
        function hitungTotalJamPengganti() {
            let total = 0;
            $('.jam-pengganti-durasi').each(function() {
                const nilai = parseFloat($(this).val()) || 0;
                total += nilai;
            });
            $('#totalJamPengganti').text(total.toFixed(1));
            $('input[name="total_jam_pengganti"]').val(total);
        }

        // Hitung total saat halaman dimuat
        hitungTotalJamPengganti();

        // Hitung ulang saat nilai jam berubah
        $(document).on('change', '.jam-pengganti-durasi', function() {
            hitungTotalJamPengganti();
        });

        // Tambah baris jam pengganti
        $('#tambahJamPengganti').click(function() {
            const index = $('#jamPenggantiBody tr').length;
            const newRow = `
                <tr>
                    <td>
                        <input type="text" name="jam_pengganti[${index}][kelas]" class="form-control" required>
                    </td>
                    <td>
                        <input type="text" name="jam_pengganti[${index}][jam_ke]" class="form-control" required>
                    </td>
                    <td>
                        <input type="text" name="jam_pengganti[${index}][guru_diganti]" class="form-control" required>
                    </td>
                    <td>
                        <input type="number" name="jam_pengganti[${index}][jumlah_jam]" class="form-control jam-pengganti-durasi" min="0.5" step="0.5" value="1" required>
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger hapus-jam-pengganti">
                            <i class="fas fa-trash"></i> Hapus
                        </button>
                    </td>
                </tr>
            `;
            $('#jamPenggantiBody').append(newRow);
            $('#noJamPenggantiAlert').hide();
            $('#jamPenggantiTableContainer').show();
            hitungTotalJamPengganti();
        });

        // Hapus baris jam pengganti
        $(document).on('click', '.hapus-jam-pengganti', function() {
            $(this).closest('tr').remove();
            hitungTotalJamPengganti();
            
            if ($('#jamPenggantiBody tr').length === 0) {
                $('#noJamPenggantiAlert').show();
                $('#jamPenggantiTableContainer').hide();
            }
        });
    });
</script>

<script>
    $(document).ready(function() {
        // Fungsi untuk menghitung total jam pengganti
        function hitungTotalJamPengganti() {
            let total = 0;
            $('.jam-pengganti-durasi').each(function() {
                const nilai = parseFloat($(this).val()) || 0;
                total += nilai;
            });
            $('#totalJamPengganti').text(total.toFixed(1));
            $('input[name="total_jam_pengganti"]').val(total);
        }

        // Hitung total saat halaman dimuat
        hitungTotalJamPengganti();

        // Hitung ulang saat nilai jam berubah
        $(document).on('change', '.jam-pengganti-durasi', function() {
            hitungTotalJamPengganti();
        });

        // Tambah baris jam pengganti
        $('#tambahJamPengganti').click(function() {
            const index = $('#jamPenggantiBody tr').length;
            const newRow = `
                <tr>
@stop









