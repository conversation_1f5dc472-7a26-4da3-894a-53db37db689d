<div class="modal fade" id="mutasiKeluarModal" tabindex="-1" role="dialog" aria-labelledby="mutasiKeluarModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form action="{{ route('rombel.mutasi-keluar') }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title" id="mutasiKeluarModalLabel">Mutasi Keluar Siswa</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="siswa_id">Pilih Si<PERSON></label>
                        <select name="siswa_id" id="siswa_id" class="form-control select2" required>
                            <option value="">-- <PERSON><PERSON>h <PERSON> --</option>
                            @foreach($siswa as $s)
                                <option value="{{ $s->id }}">{{ $s->nis }} - {{ $s->nama }} ({{ $s->kelas->nama ?? 'Belum ada kelas' }})</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="sekolah_tujuan">Sekolah Tujuan</label>
                        <input type="text" class="form-control" id="sekolah_tujuan" name="sekolah_tujuan" required>
                    </div>
                    <div class="form-group">
                        <label for="tanggal_mutasi">Tanggal Mutasi</label>
                        <input type="date" class="form-control" id="tanggal_mutasi" name="tanggal_mutasi" value="{{ date('Y-m-d') }}" required>
                    </div>
                    <div class="form-group">
                        <label for="alasan">Alasan Mutasi</label>
                        <textarea class="form-control" id="alasan" name="alasan" rows="3" required></textarea>
                    </div>
                    <p class="text-danger">Perhatian: Siswa akan dikeluarkan dari kelas saat ini dan statusnya akan berubah menjadi mutasi keluar.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Proses Mutasi</button>
                </div>
            </form>
        </div>
    </div>
</div>