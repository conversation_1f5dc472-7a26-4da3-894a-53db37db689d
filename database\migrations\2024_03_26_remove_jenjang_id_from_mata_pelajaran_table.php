<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('mata_pelajaran', function (Blueprint $table) {
            // Pastikan semua data penting dari jenjang_id sudah dipindahkan ke unit_id
            $table->dropForeign(['jenjang_id']);
            $table->dropColumn('jenjang_id');
        });
    }

    public function down(): void
    {
        Schema::table('mata_pelajaran', function (Blueprint $table) {
            $table->foreignId('jenjang_id')
                  ->after('id')
                  ->constrained('jenjangs')
                  ->onDelete('cascade');
        });
    }
};