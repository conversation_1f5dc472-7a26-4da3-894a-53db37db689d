@extends('layouts.admin')

@section('title', 'Data Kelas')

@section('plugins.Datatables', true)

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar Kelas</h3>
        <div class="card-tools">
            {{-- Tombol Filter Tahun Ajaran --}}
            <div class="btn-group mr-2">
                <button type="button" class="btn btn-secondary btn-sm dropdown-toggle" data-toggle="dropdown">
                    <i class="fas fa-filter"></i> Tahun Ajaran: {{ $tahunAjaranTerpilih }}
                </button>
                <div class="dropdown-menu">
                    {{-- Loop untuk menampilkan semua tahun ajaran yang tersedia --}}
                    @foreach($tahunAjaranList as $ta)
                        <a class="dropdown-item {{ $ta == $tahunAjaranTerpilih ? 'active' : '' }}" 
                           href="{{ route('pengaturan.kelas', ['tahun_ajaran' => $ta]) }}">
                            {{ $ta }}
                        </a>
                    @endforeach
                </div>
            </div>
            
            {{-- Filter Unit --}}
            <div class="btn-group">
                <button type="button" class="btn btn-secondary btn-sm dropdown-toggle" data-toggle="dropdown">
                    <i class="fas fa-filter"></i> Unit: {{ request('unit') ? $units->where('id', request('unit'))->first()->nama_unit ?? 'Semua' : 'Semua' }}
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item {{ !request('unit') ? 'active' : '' }}" 
                       href="{{ route('pengaturan.kelas', ['tahun_ajaran' => $tahunAjaranTerpilih]) }}">
                        Semua
                    </a>
                    @foreach($units as $unit)
                        <a class="dropdown-item {{ request('unit') == $unit->id ? 'active' : '' }}" 
                           href="{{ route('pengaturan.kelas', ['tahun_ajaran' => $tahunAjaranTerpilih, 'unit' => $unit->id]) }}">
                            {{ $unit->nama_unit }}
                        </a>
                    @endforeach
                </div>
            </div>
            
            {{-- Tombol untuk membuka modal tambah kelas baru --}}
            <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#tambahKelas">
                <i class="fas fa-plus"></i> Tambah Kelas
            </button>
        </div>
    </div>
    <div class="card-body">
        {{-- Menampilkan pesan sukses jika ada --}}
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        {{-- Menampilkan pesan error jika ada --}}
        @if($errors->any())
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <ul class="mb-0">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        {{-- Tabel untuk menampilkan data kelas --}}
        <table class="table table-bordered table-striped" id="kelasTable">
            <thead>
                <tr>
                    <th width="5%">No</th>
                    <th>Nama Kelas</th>
                    <th>Unit</th>
                    <th>Jenjang</th>
                    <th>Ruang</th>
                    <th>Tahun Ajaran</th>
                    <th width="15%">Aksi</th>
                </tr>
            </thead>
            <tbody>
                {{-- Loop untuk menampilkan data kelas --}}
                @forelse($kelas as $k)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $k->nama }}</td> <!-- Nama Kelas -->
                        <td>{{ $k->unit ? $k->unit->nama_unit : '-' }}</td> <!-- Unit -->
                        <td>{{ $k->jenjang ? $k->jenjang->jenjang . ' ' . $k->jenjang->tingkat : '-' }}</td> <!-- Jenjang -->
                        <td>{{ $k->gedung ? $k->gedung->nama_gedung : '-' }}</td> <!-- Ruang -->
                        <td>{{ $k->tahun_ajaran }}</td> <!-- Tahun Ajaran -->
                        <td>
                            {{-- Tombol edit untuk membuka modal edit --}}
                            <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#editKelas{{ $k->id }}">
                                <i class="fas fa-edit"></i>
                            </button>
                            {{-- Form untuk menghapus kelas --}}
                            <form action="{{ route('pengaturan.kelas.destroy', $k->id) }}" method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Yakin hapus data?')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                @empty
                    {{-- Tampilkan pesan jika tidak ada data --}}
                    <tr>
                        <td colspan="7" class="text-center">Tidak ada data kelas</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>

<!-- Modal Tambah Kelas -->
<div class="modal fade" id="tambahKelas" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Kelas</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            {{-- Form untuk menambah kelas baru --}}
            <form action="{{ route('pengaturan.kelas.store') }}" method="POST">
                @csrf
                <input type="hidden" name="tahun_ajaran" value="{{ $tahunAjaranTerpilih }}">
                <div class="modal-body">
                    <!-- Unit -->
                    <div class="form-group">
                        <label>Unit <span class="text-danger">*</span></label>
                        <select name="unit_id" class="form-control" required>
                            <option value="">Pilih Unit</option>
                            @foreach($units as $unit)
                                <option value="{{ $unit->id }}">{{ $unit->nama_unit }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <!-- Jenjang -->
                    <div class="form-group">
                        <label>Jenjang <span class="text-danger">*</span></label>
                        <select name="jenjang_id" class="form-control" required>
                            <option value="">Pilih Jenjang</option>
                            @foreach($jenjangs as $jenjang)
                                <option value="{{ $jenjang->id }}">
                                    {{ $jenjang->jenjang }} {{ $jenjang->tingkat }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <!-- Ruang -->
                    <div class="form-group">
                        <label>Ruang <span class="text-danger">*</span></label>
                        <select name="gedung_id" class="form-control" required>
                            <option value="">Pilih Ruang</option>
                            @foreach($gedungs as $gedung)
                                <option value="{{ $gedung->id }}">{{ $gedung->nama_gedung }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <!-- Nama Kelas -->
                    <div class="form-group">
                        <label>Nama Kelas <span class="text-danger">*</span></label>
                        <input type="text" name="nama" class="form-control" maxlength="20" required>
                    </div>
                    
                    <!-- Tahun Ajaran -->
                    <div class="form-group">
                        <label>Tahun Ajaran <span class="text-danger">*</span></label>
                        <select name="tahun_ajaran" class="form-control" required>
                            @foreach($tahunAjaranList as $ta)
                                <option value="{{ $ta }}" {{ $ta == $tahunAjaranTerpilih ? 'selected' : '' }}>
                                    {{ $ta }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

{{-- Loop untuk membuat modal edit untuk setiap kelas --}}
@foreach($kelas as $k)
<!-- Modal Edit Kelas -->
<div class="modal fade" id="editKelas{{ $k->id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Kelas</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            {{-- Form untuk mengupdate data kelas --}}
            <form action="{{ route('pengaturan.kelas.update', $k->id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    {{-- Dropdown untuk memilih unit --}}
                    <div class="form-group">
                        <label>Unit <span class="text-danger">*</span></label>
                        <select name="unit_id" id="edit_unit_id_{{ $k->id }}" class="form-control" required>
                            <option value="">Pilih Unit</option>
                            @foreach($units as $unit)
                                <option value="{{ $unit->id }}" {{ $unit->id == $k->unit_id ? 'selected' : '' }}>
                                    {{ $unit->nama_unit }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    {{-- Dropdown untuk memilih jenjang --}}
                    <div class="form-group">
                        <label>Jenjang <span class="text-danger">*</span></label>
                        <select name="jenjang_id" id="edit_jenjang_id_{{ $k->id }}" class="form-control" required>
                            <option value="">Pilih Jenjang</option>
                            @foreach($jenjangs as $jenjang)
                                <option value="{{ $jenjang->id }}" data-jenjang="{{ $jenjang->jenjang }}" {{ $jenjang->id == $k->jenjang_id ? 'selected' : '' }}>
                                    {{ $jenjang->nama_jenjang }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    {{-- Dropdown untuk memilih ruang dari tabel gedungs --}}
                    <div class="form-group">
                        <label>Ruang <span class="text-danger">*</span></label>
                        <select name="gedung_id" id="edit_gedung_id_{{ $k->id }}" class="form-control" required>
                            <option value="">Pilih Ruang</option>
                            @foreach($gedungs as $gedung)
                                <option value="{{ $gedung->id }}" data-jenjang="{{ $gedung->jenjang ?? '' }}" {{ $gedung->id == $k->gedung_id ? 'selected' : '' }}>
                                    {{ $gedung->nama_gedung }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    {{-- Input untuk nama kelas --}}
                    <div class="form-group">
                        <label>Nama Kelas <span class="text-danger">*</span></label>
                        <input type="text" name="nama" class="form-control" maxlength="20" value="{{ $k->nama }}" required>
                    </div>
                    
                    {{-- Dropdown untuk memilih tahun ajaran --}}
                    <div class="form-group">
                        <label>Tahun Ajaran <span class="text-danger">*</span></label>
                        <select name="tahun_ajaran" class="form-control" required>
                            @foreach($tahunAjaranList as $ta)
                                <option value="{{ $ta }}" {{ $ta == $k->tahun_ajaran ? 'selected' : '' }}>
                                    {{ $ta }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endforeach

@endsection

@section('css')
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="{{ asset('vendor/datatables-bs4/css/dataTables.bootstrap4.min.css') }}">
    <link rel="stylesheet" href="{{ asset('vendor/datatables-responsive/css/responsive.bootstrap4.min.css') }}">
@stop

@section('js')
<script>
$(document).ready(function() {
    // Inisialisasi DataTables
    $('#kelasTable').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        "pageLength": 10, // Jumlah baris per halaman
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]], // Opsi jumlah baris
        "order": [[1, "asc"]], // Urutan default berdasarkan kolom nama kelas (kolom ke-1) secara ascending
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        },
        "columnDefs": [
            { "orderable": false, "targets": [0, 6] } // Kolom nomor dan aksi tidak dapat diurutkan
        ]
    });

    // Filter untuk form tambah
    $('#tambah_unit_id').change(function() {
        var unitId = $(this).val();
        console.log('Unit dipilih:', unitId);
        
        if (unitId) {
            // Ambil jenjang berdasarkan unit
            $.ajax({
                url: '/api/unit/' + unitId + '/jenjangs',
                type: 'GET',
                success: function(data) {
                    console.log('Jenjang data:', data);
                    // Reset dropdown jenjang
                    $('#tambah_jenjang_id').empty().append('<option value="">Pilih Jenjang</option>');
                    
                    // Tambahkan opsi jenjang yang sesuai
                    $.each(data, function(index, jenjang) {
                        $('#tambah_jenjang_id').append('<option value="' + jenjang.id + '" data-jenjang="' + jenjang.jenjang + '">' + 
                            (jenjang.nama_jenjang || (jenjang.jenjang + ' ' + jenjang.tingkat)) + '</option>');
                    });
                },
                error: function(xhr, status, error) {
                    console.error('Error saat mengambil jenjang:', error);
                    console.error('Response:', xhr.responseText);
                    alert('Gagal mengambil data jenjang. Silakan coba lagi.');
                }
            });
            
            // Ambil gedung berdasarkan unit
            $.ajax({
                url: '/api/unit/' + unitId + '/gedungs',
                type: 'GET',
                success: function(data) {
                    console.log('Gedung data:', data);
                    // Reset dropdown gedung
                    $('#tambah_gedung_id').empty().append('<option value="">Pilih Ruang</option>');
                    
                    // Tambahkan opsi gedung yang sesuai
                    $.each(data, function(index, gedung) {
                        $('#tambah_gedung_id').append('<option value="' + gedung.id + '">' + 
                            (gedung.nama_gedung || gedung.nama) + '</option>');
                    });
                },
                error: function(xhr, status, error) {
                    console.error('Error saat mengambil gedung:', error);
                    console.error('Response:', xhr.responseText);
                    alert('Gagal mengambil data ruang. Silakan coba lagi.');
                }
            });
        } else {
            // Jika unit tidak dipilih, reset dropdown jenjang dan gedung
            $('#tambah_jenjang_id').empty().append('<option value="">Pilih Jenjang</option>');
            $('#tambah_gedung_id').empty().append('<option value="">Pilih Ruang</option>');
        }
    });
    
    // Tambahkan inisialisasi dropdown jenjang dan gedung jika unit sudah dipilih
    var selectedUnitId = $('#tambah_unit_id').val();
    if (selectedUnitId) {
        $('#tambah_unit_id').trigger('change');
    }
});
</script>
@stop


































