@extends('adminlte::page')

@section('title', 'Tambah Event')

@section('content_header')
    <h1>Tambah Event2</h1>
@stop

@section('content')
<div class="card">
    <div class="card-body">
        <form action="{{ route('website.event.store') }}" method="POST" enctype="multipart/form-data">
            @csrf
            <div class="form-group">
                <label for="judul">Judul Event <span class="text-danger">*</span></label>
                <input type="text" 
                       class="form-control @error('judul') is-invalid @enderror" 
                       id="judul" 
                       name="judul" 
                       value="{{ old('judul') }}" 
                       required>
                @error('judul')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="deskripsi">Deskripsi <span class="text-danger">*</span></label>
                <textarea class="form-control @error('deskripsi') is-invalid @enderror" 
                          id="deskripsi" 
                          name="deskripsi" 
                          rows="5" 
                          required>{{ old('deskripsi') }}</textarea>
                @error('deskripsi')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="tanggal">Tanggal Event <span class="text-danger">*</span></label>
                <input type="date" 
                       class="form-control @error('tanggal') is-invalid @enderror" 
                       id="tanggal" 
                       name="tanggal" 
                       value="{{ old('tanggal') }}" 
                       required>
                @error('tanggal')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="lokasi">Lokasi <span class="text-danger">*</span></label>
                <input type="text" 
                       class="form-control @error('lokasi') is-invalid @enderror" 
                       id="lokasi" 
                       name="lokasi" 
                       value="{{ old('lokasi') }}" 
                       required>
                @error('lokasi')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="gambar">Gambar Event</label>
                <div class="custom-file">
                    <input type="file" 
                           class="custom-file-input @error('gambar') is-invalid @enderror" 
                           id="gambar" 
                           name="gambar"
                           accept="image/jpeg,image/png,image/jpg">
                    <label class="custom-file-label" for="gambar">Pilih file</label>
                    <small class="form-text text-muted">Format: JPEG, PNG, JPG. Maksimal 2MB</small>
                </div>
                @error('gambar')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">Simpan</button>
                <a href="{{ route('website.event.index') }}" class="btn btn-secondary">Batal</a>
            </div>
        </form>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        // Menampilkan nama file yang dipilih pada input file
        $('input[type="file"]').change(function(e){
            var fileName = e.target.files[0].name;
            $('.custom-file-label').html(fileName);
        });

        // Comment out atau hapus CKEditor sementara
        /*
        ClassicEditor
            .create(document.querySelector('#deskripsi'))
            .catch(error => {
                console.error(error);
            });
        */
    </script>
@stop
