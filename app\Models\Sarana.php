<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Sarana extends Model
{
    use HasFactory;

    // Tentukan nama tabel yang benar
    protected $table = 'sarana';

    // Kolom yang dapat diisi (mass assignment)
    protected $fillable = [
        'nama_sarana',
        'no_barang',    // Nomor inventaris barang
        'jenis',        // Contoh: Gedung, Ruangan, Peralatan, dll
        'jumlah',
        'kondisi',      // Contoh: Baik, Rusak <PERSON>, Rusak Berat
        'tahun_pengadaan',
        'keterangan',
        'unit_id',      // Relasi ke unit/sekolah
        'gedung_id',    // Relasi ke gedung/ruangan
        'foto'          // Path ke foto sarana (opsional)
    ];

    // Relasi ke unit
    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }
    
    // Relasi ke gedung/ruangan
    public function gedung()
    {
        return $this->belongsTo(Gedung::class);
    }
}


