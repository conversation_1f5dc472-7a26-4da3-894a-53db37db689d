@extends('layouts.website')

@section('title', 'Event ' . ucfirst($jenjang))

@section('content')


@php
/**
 * Mengekstrak ID video dari URL YouTube
 *
 * @param string $url URL YouTube
 * @return string|null ID video YouTube atau null jika tidak valid
 */
function getYoutubeVideoId($url) {
    if (empty($url)) return null;

    $pattern =
        '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i';

    if (preg_match($pattern, $url, $match)) {
        return $match[1];
    }

    return null;
}
@endphp

<style>
/* ===== STYLING UNTUK HALAMAN EVENT ===== */

/* Container utama dengan background gradient */
.event-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

/* ===== STYLING UNTUK JUDUL HALAMAN EVENT ===== */
.event-page-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.8rem;
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.5px;
    line-height: 1.2;
}

/* Efek underline animasi untuk judul */
.event-page-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    animation: expandLine 2s ease-out forwards;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* Animasi untuk garis bawah judul */
@keyframes expandLine {
    0% {
        width: 0;
    }
    100% {
        width: 120px;
    }
}

/* Efek shimmer pada judul */
.event-page-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Hover effect untuk judul */
.event-page-title:hover {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

/* Styling untuk card event */
.event-card {
    background: white;
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    overflow: hidden;
    position: relative;
    height: 100%;
}

.event-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.event-card:hover::before {
    transform: scaleX(1);
}

.event-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Container media untuk gambar dan video */
.media-container {
    width: 100%;
    height: 220px;
    overflow: hidden;
    position: relative;
    border-radius: 20px 20px 0 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Styling untuk gambar */
.event-image {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: transform 0.4s ease;
    border-radius: 0;
}

.event-card:hover .event-image {
    transform: scale(1.05);
}

/* Styling untuk embed YouTube */
.embed-responsive {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0 !important;
    margin: 0 !important;
    overflow: hidden;
    border-radius: 20px 20px 0 0;
}

.embed-responsive-16by9 {
    padding-bottom: 0 !important;
    height: 100% !important;
}

.embed-responsive-item {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    border: 0 !important;
    border-radius: 20px 20px 0 0 !important;
}

/* Styling untuk body card */
.event-card-body {
    padding: 1.5rem;
    background: white;
}

.event-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.25rem;
    margin-bottom: 1rem;
    transition: color 0.3s ease;
    line-height: 1.4;
}

.event-card:hover .event-title {
    color: #667eea;
}

/* Styling untuk informasi event */
.event-info {
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    color: #6c757d;
    display: flex;
    align-items: center;
}

.event-info i {
    margin-right: 10px;
    width: 16px;
    text-align: center;
    color: #667eea;
}

.event-description {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Styling untuk placeholder jika tidak ada media */
.no-media-placeholder {
    height: 220px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 3rem;
    border-radius: 20px 20px 0 0;
}

/* Responsive untuk judul */
@media (max-width: 768px) {
    .event-page-title {
        font-size: 2.2rem;
        margin-bottom: 2rem;
    }

    .event-page-title::after {
        width: 80px;
    }

    .event-container {
        padding: 1rem 0;
    }

    .media-container,
    .event-image,
    .no-media-placeholder {
        height: 180px;
    }

    .event-card-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .event-page-title {
        font-size: 1.8rem;
        letter-spacing: -0.3px;
    }

    .event-page-title::after {
        width: 60px;
        height: 3px;
    }

    .media-container,
    .event-image,
    .no-media-placeholder {
        height: 160px;
    }
}
</style>

<div class="event-container">
    <div class="container">
        <div class="row">
            <!-- Sidebar Menu -->
            <div class="col-md-3 mb-4">
                @include('website.partials._sidebar')
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <h1 class="event-page-title">Event {{ ucfirst($jenjang) }}</h1>

                @if($events->count() > 0)
                    <div class="row">
                        @foreach($events as $event)
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card event-card">
                                <div class="media-container">
                                    @if($event->gambar)
                                        <img src="{{ asset('storage/events/' . $event->gambar) }}"
                                             class="event-image"
                                             alt="{{ $event->judul }}">
                                    @elseif($event->youtube_url)
                                        <div class="embed-responsive embed-responsive-16by9">
                                            <iframe class="embed-responsive-item"
                                                    src="https://www.youtube.com/embed/{{ getYoutubeVideoId($event->youtube_url) }}"
                                                    allowfullscreen></iframe>
                                        </div>
                                    @else
                                        <div class="no-media-placeholder">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                    @endif
                                </div>
                                <div class="card-body event-card-body">
                                    <h5 class="card-title event-title">{{ $event->judul }}</h5>
                                    <div class="event-info">
                                        <i class="fas fa-calendar"></i>
                                        {{ \Carbon\Carbon::parse($event->tanggal)->format('d M Y') }}
                                    </div>
                                    <div class="event-info">
                                        <i class="fas fa-map-marker-alt"></i>
                                        {{ $event->lokasi }}
                                    </div>
                                    <p class="card-text event-description">{{ Str::limit($event->deskripsi, 100) }}</p>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="empty-state">
                        <i class="fas fa-calendar-alt"></i>
                        <h3>Belum Ada Event</h3>
                        <p>Event untuk jenjang {{ ucfirst($jenjang) }} belum tersedia.</p>
                    </div>
                @endif

                @if($events->hasPages())
                    <div class="pagination-container d-flex justify-content-center mt-4">
                        {{ $events->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

