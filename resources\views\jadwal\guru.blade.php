@extends('layouts.admin')

@section('title')
    <PERSON><PERSON>wal Mengajar {{ auth()->user()->name }}
@endsection

@section('content')
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="card-title"><PERSON><PERSON><PERSON> Mengajar {{ auth()->user()->name }}</h3>
            <div class="card-tools">
                <div class="btn-group">
                    <button type="button" class="btn btn-secondary btn-sm dropdown-toggle" data-toggle="dropdown">
                        <i class="fas fa-filter"></i> Tahun Ajaran: {{ $tahunAjaranTerpilih }}
                    </button>
                    <div class="dropdown-menu">
                        @foreach($tahunAjaranList as $ta)
                            <a class="dropdown-item {{ $ta == $tahunAjaranTerpilih ? 'active' : '' }}" 
                               href="{{ route('jadwal.guru', ['tahun_ajaran' => $ta]) }}">
                                {{ $ta }}
                            </a>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        @if($jadwalGuru->isEmpty())
            <div class="alert alert-info">
                Tidak ada jadwal mengajar untuk tahun ajaran ini.
            </div>
        @else
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Hari</th>
                            <th>Jam</th>
                            <th>Mata Pelajaran</th>
                            <th>Kelas</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php $currentHari = null; @endphp
                        @foreach($jadwalGuru->groupBy('hari') as $hari => $jadwalPerHari)
                            @foreach($jadwalPerHari as $jadwal)
                                <tr>
                                    @if($loop->first)
                                        <td rowspan="{{ $jadwalPerHari->count() }}">{{ $hari }}</td>
                                    @endif
                                    <td>{{ substr($jadwal->waktu_mulai, 0, 5) }} - {{ substr($jadwal->waktu_selesai, 0, 5) }}</td>
                                    <td>{{ $jadwal->mataPelajaran->nama_mapel }}</td>
                                    <td>{{ $jadwal->jadwalPelajaran->nama_kelas_text }}</td>
                                </tr>
                            @endforeach
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif
    </div>
</div>
@endsection

@section('css')
<style>
    .card-tools {
        float: right;
    }
    .table th {
        background-color: #f8f9fa;
        vertical-align: middle;
    }
    .table td {
        vertical-align: middle;
    }
</style>
@endsection
