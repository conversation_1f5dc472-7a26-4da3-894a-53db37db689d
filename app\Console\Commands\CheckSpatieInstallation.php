<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class CheckSpatieInstallation extends Command
{
    protected $signature = 'check:spatie';
    protected $description = 'Check Spatie Permission package installation';

    public function handle()
    {
        $this->info('Checking Spatie Permission Installation...');

        // 1. Check if package is installed
        $this->info("\nPackage Installation:");
        $this->line('- Package Version: ' . \Composer\InstalledVersions::getVersion('spatie/laravel-permission'));

        // 2. Check if tables exist
        $this->info("\nDatabase Tables:");
        $tables = ['permissions', 'roles', 'model_has_permissions', 'model_has_roles', 'role_has_permissions'];
        foreach ($tables as $table) {
            $exists = Schema::hasTable($table);
            $this->line("- {$table}: " . ($exists ? '✓' : '✗'));
        }

        // 5. Check middleware registration
        $this->info("\nMiddleware Registration:");
        $kernel = app()->make(\App\Http\Kernel::class);
        
        // Cek di middlewareAliases
        $middlewareAliases = $kernel->getMiddlewareAliases();
        
        // Tambahkan pengecekan lebih detail
        $this->line("- 'role' middleware: " . (isset($middlewareAliases['role']) && class_exists($middlewareAliases['role']) ? '✓' : '✗'));
        $this->line("- 'permission' middleware: " . (isset($middlewareAliases['permission']) && class_exists($middlewareAliases['permission']) ? '✓' : '✗'));
        $this->line("- 'role_or_permission' middleware: " . (isset($middlewareAliases['role_or_permission']) && class_exists($middlewareAliases['role_or_permission']) ? '✓' : '✗'));
        
        // Tambahkan debug info
        if (isset($middlewareAliases['role'])) {
            $this->line("\nDebug Info:");
            $this->line("Role middleware class: " . $middlewareAliases['role']);
            $this->line("Class exists: " . (class_exists($middlewareAliases['role']) ? 'Yes' : 'No'));
        }
    }
}
