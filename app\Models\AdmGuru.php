<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AdmGuru extends Model
{
    protected $table = 'adm_guru';
    
    protected $fillable = [
        'user_id',
        'unit_id',
        'judul',
        'keterangan',
        'file_path',
        'status',
        'approved_by',
        'approved_at',
        'rejected_by',
        'rejected_at',
        'alasan_penolakan'
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'approved_at',
        'rejected_at'
    ];

    public function guru()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function rejector()
    {
        return $this->belongsTo(User::class, 'rejected_by');
    }
}


