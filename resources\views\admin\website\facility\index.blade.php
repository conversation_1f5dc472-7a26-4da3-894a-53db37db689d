@extends('adminlte::page')

@section('title', 'Daftar Fasilitas')

@section('content_header')
    <h1>Daftar Fasilitas</h1>
@stop

@section('content')
<div class="card">
    <div class="card-header">
        <a href="{{ route('admin.website.facility.create') }}" class="btn btn-primary">
            Tambah Fasilitas
        </a>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Gambar</th>
                        <th>Judul</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($facilities as $facility)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>
                            @if($facility->image)
                                <img src="{{ asset('storage/facilities/'.$facility->image) }}" 
                                     alt="{{ $facility->title }}" 
                                     width="100">
                            @endif
                        </td>
                        <td>{{ $facility->title }}</td>
                        <td>
                            <a href="{{ route('admin.website.facility.edit', $facility) }}" 
                               class="btn btn-sm btn-warning">Edit</a>
                            <form action="{{ route('admin.website.facility.destroy', $facility) }}" 
                                  method="POST" 
                                  class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" 
                                        class="btn btn-sm btn-danger" 
                                        onclick="return confirm('Yakin ingin menghapus?')">Hapus</button>
                            </form>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@stop