@extends('adminlte::page')

@section('title', 'Jurnal Disetujui')

@section('content_header')
    <h1>Jurnal Kegiatan Disetujui</h1>
@stop

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Daftar Jurnal Kegiatan yang Telah Disetujui</h3>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            @if($isAdmin)
                            <th>Nama</th>
                            @endif
                            <th>Tanggal</th>
                            <th>Kegiatan</th>
                            <th>Keterangan</th>
                            <th>Disetujui Pada</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($approvedJurnals as $jurnal)
                            <tr>
                                @if($isAdmin)
                                <td>{{ $jurnal->user->name }}</td>
                                @endif
                                <td>
                                    @if(is_string($jurnal->tanggal))
                                        {{ \Carbon\Carbon::parse($jurnal->tanggal)->format('d/m/Y') }}
                                    @else
                                        {{ $jurnal->tanggal->format('d/m/Y') }}
                                    @endif
                                </td>
                                <td>{{ $jurnal->kegiatan }}</td>
                                <td>{{ $jurnal->keterangan ?? '-' }}</td>
                                <td>
                                    @if(is_string($jurnal->approved_at))
                                        {{ \Carbon\Carbon::parse($jurnal->approved_at)->format('d/m/Y H:i') }}
                                    @else
                                        {{ $jurnal->approved_at->format('d/m/Y H:i') }}
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="{{ $isAdmin ? 5 : 4 }}" class="text-center">Tidak ada jurnal yang disetujui</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
                
                {{ $approvedJurnals->links() }}
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="{{ asset('css/admin_custom.css') }}">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            $('table').DataTable({
                "paging": false,
                "lengthChange": false,
                "searching": true,
                "ordering": true,
                "info": false,
                "autoWidth": false,
                "responsive": true,
            });
        });
    </script>
@stop