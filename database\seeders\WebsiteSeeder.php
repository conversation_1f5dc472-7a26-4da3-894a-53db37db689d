<?php

namespace Database\Seeders;

use App\Models\Article;
use App\Models\Achievement;
use App\Models\Extracurricular;
use App\Models\User;
use Illuminate\Database\Seeder;

class WebsiteSeeder extends Seeder
{
    public function run()
    {
        // Contoh artikel
        Article::create([
            'title' => 'Selamat Datang di Website Baru Kami',
            'slug' => 'selamat-datang',
            'excerpt' => 'Kami dengan bangga meluncurkan website baru sekolah.',
            'content' => 'Konten lengkap artikel...',
            'status' => 'published',
            'author_id' => User::first()->id,
            'published_at' => now(),
        ]);

        // Contoh prestasi
        Achievement::create([
            'title' => 'Juara 1 Olimpiade Sains',
            'description' => 'Deskripsi prestasi...',
            'date' => now(),
            'level' => 'Provinsi',
            'participant' => 'Tim Olimpiade Sains'
        ]);

        // Contoh ekstrakurikuler
        Extracurricular::create([
            'name' => '<PERSON><PERSON>uka',
            'description' => 'Kegiatan kepramukaan...',
            'coach' => 'Pembina Pramuka',
            'schedule' => 'Setiap Jumat, 14:00 - 16:00',
            'location' => 'Lapangan Sekolah'
        ]);
    }
}