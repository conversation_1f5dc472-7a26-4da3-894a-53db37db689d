@extends('adminlte::page')

@section('title', 'Rekap Absensi Peserta Didik')

@section('content_header')
    <h1>Rekap Absensi Peserta Didik</h1>
@stop

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Filter Rekap Absensi Peserta Didik</h3>
    </div>
    <div class="card-body">
        <form id="rekapForm">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="guru_id">Guru</label>
                        <select class="form-control" id="guru_id" name="guru_id">
                            <option value="">-- Se<PERSON>a Guru --</option>
                            @foreach($guruOptions as $id => $nama)
                                <option value="{{ $id }}" {{ $guruId == $id ? 'selected' : '' }}>{{ $nama }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="kelas_id">Kelas</label>
                        <select class="form-control" id="kelas_id" name="kelas_id">
                            <option value="">-- Pilih Kelas --</option>
                            @foreach($kelasOptions as $id => $nama)
                                <option value="{{ $id }}" {{ $kelasId == $id ? 'selected' : '' }}>{{ $nama }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="bulan">Bulan</label>
                        <select class="form-control" id="bulan" name="bulan">
                            @foreach($namaBulan as $id => $nama)
                                <option value="{{ $id }}" {{ $id == $bulan ? 'selected' : '' }}>{{ $nama }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="tahun">Tahun</label>
                        <select class="form-control" id="tahun" name="tahun">
                            @php
                                $tahunSekarang = date('Y');
                                $tahunMulai = $tahunSekarang - 5;
                                $tahunAkhir = $tahunSekarang + 1;
                            @endphp
                            
                            @for($tahun = $tahunAkhir; $tahun >= $tahunMulai; $tahun--)
                                <option value="{{ $tahun }}" {{ $tahun == request('tahun', date('Y')) ? 'selected' : '' }}>{{ $tahun }}</option>
                            @endfor
                        </select>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="button" id="btnLoadRekap" class="btn btn-primary btn-block">
                            <i class="fas fa-search"></i> Tampilkan
                        </button>
                    </div>
                </div>
            </div>
        </form>
        
        <div id="rekapContainer" class="mt-4" style="display: none;">
            <div class="text-right mb-3">
                <button type="button" id="btnExportExcel" class="btn btn-success">
                    <i class="fas fa-file-excel"></i> Export Excel
                </button>
                <button type="button" id="btnPrint" class="btn btn-info">
                    <i class="fas fa-print"></i> Cetak
                </button>
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="rekapTable">
                    <thead>
                        <tr>
                            <th rowspan="2" class="text-center align-middle">No</th>
                            <th rowspan="2" class="text-center align-middle">NISN</th>
                            <th rowspan="2" class="text-center align-middle">Nama Siswa</th>
                            <th colspan="{{ $totalDays ?? 31 }}" class="text-center" id="bulanTahunHeader">
                                {{ isset($namaBulan[$bulan]) ? $namaBulan[$bulan] . ' ' . $tahun : 'Tanggal' }}
                            </th>
                            <th colspan="4" class="text-center">Jumlah</th>
                        </tr>
                        <tr id="tanggalHeader">
                            @if(isset($totalDays))
                                @for($i = 1; $i <= $totalDays; $i++)
                                    <th class="text-center">{{ $i }}</th>
                                @endfor
                            @else
                                <!-- Tanggal akan diisi oleh JavaScript jika tidak ada filter -->
                            @endif
                            <th class="text-center">H</th>
                            <th class="text-center">S</th>
                            <th class="text-center">I</th>
                            <th class="text-center">A</th>
                        </tr>
                    </thead>
                    <tbody id="rekapTableBody">
                        @if(isset($rekapData) && count($rekapData) > 0)
                            @foreach($rekapData as $index => $siswa)
                                <tr>
                                    <td class="text-center">{{ $index + 1 }}</td>
                                    <td>{{ $siswa->nisn }}</td>
                                    <td>{{ $siswa->nama }}</td>
                                    @if(isset($totalDays))
                                        @for($i = 1; $i <= $totalDays; $i++)
                                            <td class="text-center">
                                                @if(isset($siswa->absensi[$i]))
                                                    @if($siswa->absensi[$i] == 'hadir')
                                                        <span class="badge bg-success">H</span>
                                                    @elseif($siswa->absensi[$i] == 'sakit')
                                                        <span class="badge bg-warning">S</span>
                                                    @elseif($siswa->absensi[$i] == 'izin')
                                                        <span class="badge bg-info">I</span>
                                                    @elseif($siswa->absensi[$i] == 'alpa')
                                                        <span class="badge bg-danger">A</span>
                                                    @endif
                                                @else
                                                    -
                                                @endif
                                            </td>
                                        @endfor
                                    @endif
                                    <td class="text-center">{{ $siswa->hadir ?? 0 }}</td>
                                    <td class="text-center">{{ $siswa->sakit ?? 0 }}</td>
                                    <td class="text-center">{{ $siswa->izin ?? 0 }}</td>
                                    <td class="text-center">{{ $siswa->alpa ?? 0 }}</td>
                                </tr>
                            @endforeach
                            <tr class="font-weight-bold bg-light">
                                <td colspan="3" class="text-right">Total</td>
                                @if(isset($totalDays))
                                    @for($i = 1; $i <= $totalDays; $i++)
                                        <td class="text-center">-</td>
                                    @endfor
                                @endif
                                <td class="text-center">{{ $totalHadir ?? 0 }}</td>
                                <td class="text-center">{{ $totalSakit ?? 0 }}</td>
                                <td class="text-center">{{ $totalIzin ?? 0 }}</td>
                                <td class="text-center">{{ $totalAlpa ?? 0 }}</td>
                            </tr>
                        @else
                            <tr>
                                <td colspan="{{ ($totalDays ?? 31) + 7 }}" class="text-center">
                                    @if(isset($kelasId) && $kelasId)
                                        Tidak ada data absensi untuk kelas dan periode yang dipilih
                                    @else
                                        Silakan pilih kelas, bulan, dan tahun untuk melihat rekap absensi
                                    @endif
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
            
            <div class="mt-4">
                <h5>Keterangan:</h5>
                <div class="row">
                    <div class="col-md-6">
                        <p>H = Hadir</p>
                        <p>S = Sakit</p>
                        <p>I = Izin</p>
                        <p>A = Alpa (Tanpa Keterangan)</p>
                    </div>
                    <div class="col-md-6">
                        @if(isset($totalHadir) && isset($totalSakit) && isset($totalIzin) && isset($totalAlpa))
                            @php
                                $totalKeseluruhan = $totalHadir + $totalSakit + $totalIzin + $totalAlpa;
                                $persenHadir = $totalKeseluruhan > 0 ? round(($totalHadir / $totalKeseluruhan) * 100, 2) : 0;
                                $persenSakit = $totalKeseluruhan > 0 ? round(($totalSakit / $totalKeseluruhan) * 100, 2) : 0;
                                $persenIzin = $totalKeseluruhan > 0 ? round(($totalIzin / $totalKeseluruhan) * 100, 2) : 0;
                                $persenAlpa = $totalKeseluruhan > 0 ? round(($totalAlpa / $totalKeseluruhan) * 100, 2) : 0;
                            @endphp
                            <p>Persentase Hadir: {{ $persenHadir }}% ({{ $totalHadir }})</p>
                            <p>Persentase Sakit: {{ $persenSakit }}% ({{ $totalSakit }})</p>
                            <p>Persentase Izin: {{ $persenIzin }}% ({{ $totalIzin }})</p>
                            <p>Persentase Alpa: {{ $persenAlpa }}% ({{ $totalAlpa }})</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('js')
<script>
$(function() {
    // Handler untuk pemilihan guru
    $('#guru_id').change(function() {
        const guruId = $(this).val();
        
        // Reset kelas dropdown
        $('#kelas_id').empty().append('<option value="">-- Pilih Kelas --</option>');
        
        // Jika guru dipilih, ambil kelas yang diajar oleh guru tersebut
        $.ajax({
            url: "{{ route('absensi.get-kelas-by-guru') }}",
            type: "GET",
            data: {
                guru_id: guruId
            },
            dataType: "json",
            success: function(response) {
                if (response.length > 0) {
                    $.each(response, function(index, kelas) {
                        $('#kelas_id').append(`<option value="${kelas.id}">${kelas.nama_kelas}</option>`);
                    });
                }
            },
            error: function(xhr) {
                console.error('Error loading classes:', xhr.responseText);
            }
        });
    });
    
    // Debug: Log when document is ready
    console.log('Document ready, attaching event handlers');
    
    // Load rekap when button is clicked - using direct click binding
    $(document).on('click', '#btnLoadRekap', function() {
        console.log('Load rekap button clicked');
        const kelasId = $('#kelas_id').val();
        const guruId = $('#guru_id').val();
        const bulan = $('#bulan').val();
        const tahun = $('#tahun').val();
        
        if (!kelasId) {
            alert('Silakan pilih kelas terlebih dahulu');
            return;
        }
        
        // Set header bulan dan tahun
        const namaBulan = $('#bulan option:selected').text();
        $('#bulanTahunHeader').text(`${namaBulan} ${tahun}`);
        
        // Buat header tanggal
        const jumlahHari = new Date(tahun, bulan, 0).getDate();
        let headerTanggal = '';
        
        for (let i = 1; i <= jumlahHari; i++) {
            headerTanggal += `<th class="text-center">${i}</th>`;
        }
        
        // Tambahkan kolom jumlah
        headerTanggal += `
            <th class="text-center">H</th>
            <th class="text-center">S</th>
            <th class="text-center">I</th>
            <th class="text-center">A</th>
        `;
        
        // Update colspan pada header bulan dan tahun
        $('#bulanTahunHeader').attr('colspan', jumlahHari);
        
        // Update header tanggal
        $('#tanggalHeader').html(headerTanggal);
        
        // Load data rekap
        $.ajax({
            url: "{{ route('absensi.get-rekap-admin') }}",
            type: "GET",
            data: {
                kelas_id: kelasId,
                guru_id: guruId,
                bulan: bulan,
                tahun: tahun
            },
            dataType: "json",
            beforeSend: function() {
                $('#rekapTableBody').html('<tr><td colspan="' + (jumlahHari + 7) + '" class="text-center">Loading...</td></tr>');
                $('#rekapContainer').show();
            },
            success: function(response) {
                let html = '';
                let totalHadir = 0;
                let totalSakit = 0;
                let totalIzin = 0;
                let totalAlpa = 0;
                
                if (response.length > 0) {
                    $.each(response, function(index, siswa) {
                        html += `<tr>
                            <td class="text-center">${index + 1}</td>
                            <td>${siswa.nisn || '-'}</td>
                            <td>${siswa.nama}</td>`;
                        
                        // Tampilkan status untuk setiap tanggal
                        for (let i = 1; i <= jumlahHari; i++) {
                            let status = '-';
                            if (siswa.absensi && siswa.absensi[i]) {
                                if (siswa.absensi[i] === 'hadir') {
                                    status = '<span class="badge bg-success">H</span>';
                                } else if (siswa.absensi[i] === 'sakit') {
                                    status = '<span class="badge bg-warning">S</span>';
                                } else if (siswa.absensi[i] === 'izin') {
                                    status = '<span class="badge bg-info">I</span>';
                                } else if (siswa.absensi[i] === 'alpa') {
                                    status = '<span class="badge bg-danger">A</span>';
                                }
                            }
                            html += `<td class="text-center">${status}</td>`;
                        }
                        
                        // Tampilkan jumlah
                        html += `
                            <td class="text-center">${siswa.hadir || 0}</td>
                            <td class="text-center">${siswa.sakit || 0}</td>
                            <td class="text-center">${siswa.izin || 0}</td>
                            <td class="text-center">${siswa.alpa || 0}</td>
                        </tr>`;
                        
                        totalHadir += parseInt(siswa.hadir || 0);
                        totalSakit += parseInt(siswa.sakit || 0);
                        totalIzin += parseInt(siswa.izin || 0);
                        totalAlpa += parseInt(siswa.alpa || 0);
                    });
                    
                    // Tambahkan baris total
                    html += `<tr class="font-weight-bold bg-light">
                        <td colspan="3" class="text-right">Total</td>`;
                        
                    for (let i = 1; i <= jumlahHari; i++) {
                        html += `<td class="text-center">-</td>`;
                    }
                    
                    html += `
                        <td class="text-center">${totalHadir}</td>
                        <td class="text-center">${totalSakit}</td>
                        <td class="text-center">${totalIzin}</td>
                        <td class="text-center">${totalAlpa}</td>
                    </tr>`;
                    
                    // Hitung persentase
                    const totalKeseluruhan = totalHadir + totalSakit + totalIzin + totalAlpa;
                    const persenHadir = totalKeseluruhan > 0 ? Math.round((totalHadir / totalKeseluruhan) * 100 * 100) / 100 : 0;
                    const persenSakit = totalKeseluruhan > 0 ? Math.round((totalSakit / totalKeseluruhan) * 100 * 100) / 100 : 0;
                    const persenIzin = totalKeseluruhan > 0 ? Math.round((totalIzin / totalKeseluruhan) * 100 * 100) / 100 : 0;
                    const persenAlpa = totalKeseluruhan > 0 ? Math.round((totalAlpa / totalKeseluruhan) * 100 * 100) / 100 : 0;
                    
                    // Update persentase di keterangan
                    $('.col-md-6:last-child').html(`
                        <p>Persentase Hadir: ${persenHadir}% (${totalHadir})</p>
                        <p>Persentase Sakit: ${persenSakit}% (${totalSakit})</p>
                        <p>Persentase Izin: ${persenIzin}% (${totalIzin})</p>
                        <p>Persentase Alpa: ${persenAlpa}% (${totalAlpa})</p>
                    `);
                } else {
                    html = `<tr><td colspan="${jumlahHari + 7}" class="text-center">Tidak ada data absensi untuk kelas dan periode yang dipilih</td></tr>`;
                }
                
                $('#rekapTableBody').html(html);
            },
            error: function(xhr) {
                alert('Terjadi kesalahan saat memuat data rekap absensi');
                console.error(xhr.responseText);
                $('#rekapTableBody').html('<tr><td colspan="' + (jumlahHari + 7) + '" class="text-center text-danger">Error: Gagal memuat data</td></tr>');
            }
        });
    });
    
    // Alternative binding method
    $('#btnLoadRekap').on('click', function() {
        console.log('Alternative binding triggered');
        // This is a backup in case the first method doesn't work
    });
    
    // Export Excel
    $('#btnExportExcel').click(function() {
        const kelasId = $('#kelas_id').val();
        const guruId = $('#guru_id').val();
        const bulan = $('#bulan').val();
        const tahun = $('#tahun').val();
        
        if (!kelasId) {
            alert('Silakan pilih kelas terlebih dahulu');
            return;
        }
        
        window.location.href = `{{ url('absensi/export-excel') }}?kelas_id=${kelasId}&guru_id=${guruId}&bulan=${bulan}&tahun=${tahun}`;
    });
    
    // Print
    $('#btnPrint').click(function() {
        window.print();
    });
});
</script>
@stop

@section('css')
<style>
    .table td, .table th {
        vertical-align: middle;
        padding: 0.5rem;
        text-align: center;
    }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        .card-header, .btn, form {
            display: none !important;
        }
        
        .card {
            border: none !important;
        }
        
        .card-body {
            padding: 0 !important;
        }
    }
</style>
@stop






