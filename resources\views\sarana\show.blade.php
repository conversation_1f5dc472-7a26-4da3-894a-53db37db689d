

@extends('adminlte::page')

@section('title', 'Detail Sarana')

@section('content_header')
    <h1>Detail Sarana</h1>
@stop

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="200">Nama Sarana</th>
                                    <td>{{ $sarana->nama_sarana }}</td>
                                </tr>
                                <tr>
                                    <th>No. Barang</th>
                                    <td>{{ $sarana->no_barang ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Jenis</th>
                                    <td>{{ $sarana->jenis }}</td>
                                </tr>
                                <tr>
                                    <th>Jumlah</th>
                                    <td>{{ $sarana->jumlah }}</td>
                                </tr>
                                <tr>
                                    <th><PERSON><PERSON><PERSON></th>
                                    <td>{{ $sarana->kondisi }}</td>
                                </tr>
                                <tr>
                                    <th><PERSON><PERSON></th>
                                    <td>{{ $sarana->tahun_pengadaan ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Unit</th>
                                    <td>{{ $sarana->unit->nama_unit ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Lokasi</th>
                                    <td>{{ $sarana->gedung->nama_gedung ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Keterangan</th>
                                    <td>{{ $sarana->keterangan ?? '-' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            @if($sarana->foto)
                                <img src="{{ asset('storage/' . $sarana->foto) }}" class="img-fluid" alt="Foto Sarana">
                            @else
                                <div class="alert alert-info">Tidak ada foto</div>
                            @endif
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('sarana.index') }}" class="btn btn-secondary">Kembali</a>
                        
                        {{-- Tombol Edit - Hanya muncul jika pengguna memiliki permission edit-sarpras --}}
                        @can('edit-sarpras')
                        <a href="{{ route('sarana.edit', $sarana->id) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        @endcan
                        
                        {{-- Tombol Hapus - Hanya muncul jika pengguna memiliki permission delete-sarpras --}}
                        @can('delete-sarpras')
                        <form action="{{ route('sarana.destroy', $sarana->id) }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')">
                                <i class="fas fa-trash"></i> Hapus
                            </button>
                        </form>
                        @endcan
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop


