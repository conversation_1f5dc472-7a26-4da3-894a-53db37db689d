<?php

namespace App\Http\Controllers;

use App\Models\AdmKepsek;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdmKepsekController extends Controller
{
    public function __construct()
    {
        // Middleware sudah didaftarkan di route, jadi tidak perlu didaftarkan lagi di sini
        // Jika ingin tetap mendaftarkan di sini, gunakan parent::middleware()
    }

    public function index()
    {
        $user = auth()->user();
        $admKepsek = AdmKepsek::with(['user', 'approver', 'rejecter'])->orderBy('created_at', 'desc')->get();
        
        return view('adm.kepsek.index', compact('admKepsek'));
    }

    public function store(Request $request)
    {
        // Debug informasi request
        Log::info('AdmKepsek store - Request received', [
            'method' => $request->method(),
            'url' => $request->url(),
            'has_file' => $request->hasFile('file'),
            'all_inputs' => $request->all(),
        ]);
        
        // Validasi request dengan pesan error yang jelas
        $validated = $request->validate([
            'judul' => 'required|string|max:255',
            'keterangan' => 'nullable|string',
            'file' => 'required|file|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx|max:10240',
        ]);

        try {
            DB::beginTransaction();
            
            // Proses upload file
            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('adm_kepsek', $fileName, 'public');
            
            // Verifikasi file tersimpan
            if (!Storage::disk('public')->exists($filePath)) {
                throw new \Exception('File gagal disimpan ke storage');
            }

            // Buat record baru dengan data lengkap
            $admKepsek = new AdmKepsek();
            $admKepsek->user_id = auth()->id();
            $admKepsek->judul = $request->judul;
            $admKepsek->keterangan = $request->keterangan;
            $admKepsek->file_path = $filePath;
            $admKepsek->status = 'pending';
            
            // Tambahkan unit_id jika ada
            if (auth()->user()->unit_id) {
                $admKepsek->unit_id = auth()->user()->unit_id;
            }
            
            // Simpan record
            $admKepsek->save();
            
            DB::commit();
            
            return redirect()->route('adm.kepsek.index')
                ->with('success', 'ADM Kepala Sekolah berhasil diupload');
        } catch (\Exception $e) {
            DB::rollBack();
            
            // Hapus file jika sudah terupload tapi gagal menyimpan ke database
            if (isset($filePath) && Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
            }
            
            Log::error('Error in AdmKepsekController@store: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->route('adm.kepsek.index')
                ->with('error', 'Terjadi kesalahan saat upload ADM: ' . $e->getMessage());
        }
    }

    public function approve(AdmKepsek $adm)
    {
        if (!auth()->user()->hasPermissionTo('approve-adm-kepsek')) {
            return redirect()->route('adm.kepsek.index')
                ->with('error', 'Anda tidak memiliki izin untuk menyetujui ADM Kepala Sekolah.');
        }

        try {
            $adm->update([
                'status' => 'approved',
                'approved_by' => auth()->id(),
                'approved_at' => Carbon::now(), // Pastikan ini objek Carbon
                'rejected_by' => null,
                'rejected_at' => null,
                'alasan_penolakan' => null,
            ]);

            return redirect()->route('adm.kepsek.index')
                ->with('success', 'ADM Kepala Sekolah berhasil disetujui');
        } catch (\Exception $e) {
            Log::error('Error in AdmKepsekController@approve: ' . $e->getMessage());
            return redirect()->route('adm.kepsek.index')
                ->with('error', 'Terjadi kesalahan saat menyetujui ADM');
        }
    }

    public function reject(Request $request, AdmKepsek $adm)
    {
        if (!auth()->user()->hasPermissionTo('approve-adm-kepsek')) {
            return redirect()->route('adm.kepsek.index')
                ->with('error', 'Anda tidak memiliki izin untuk menolak ADM Kepala Sekolah.');
        }

        $request->validate([
            'alasan_penolakan' => 'required|string',
        ]);

        try {
            // Periksa apakah ADM sudah diproses sebelumnya
            if ($adm->status !== 'pending') {
                return redirect()->route('adm.kepsek.index')
                    ->with('error', 'ADM sudah diproses sebelumnya');
            }

            $adm->update([
                'status' => 'ditangguhkan',
                'rejected_by' => auth()->id(),
                'rejected_at' => Carbon::now(), // Pastikan ini objek Carbon
                'alasan_penolakan' => $request->alasan_penolakan,
                'approved_by' => null,
                'approved_at' => null,
            ]);

            return redirect()->route('adm.kepsek.index')
                ->with('success', 'ADM Kepala Sekolah berhasil ditolak');
        } catch (\Exception $e) {
            Log::error('Error in AdmKepsekController@reject: ' . $e->getMessage());
            
            // Tampilkan detail error jika dalam mode debug
            if (config('app.debug')) {
                return redirect()->route('adm.kepsek.index')
                    ->with('error', 'Error: ' . $e->getMessage());
            }
            
            return redirect()->route('adm.kepsek.index')
                ->with('error', 'Terjadi kesalahan saat menolak ADM');
        }
    }

    public function viewFile($filename)
    {
        try {
            $path = storage_path('app/public/adm_kepsek/' . $filename);
            
            if (!file_exists($path)) {
                abort(404, 'File tidak ditemukan');
            }

            return response()->file($path, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="' . $filename . '"'
            ]);
        } catch (\Exception $e) {
            Log::error('Error viewing file: ' . $e->getMessage());
            return response()->json(['error' => 'File tidak dapat diakses'], 404);
        }
    }
}













