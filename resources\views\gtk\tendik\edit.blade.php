@extends('layouts.admin')

@section('title', 'Edit Tenaga Kependidikan')

@section('page_title', 'Edit Data Tenaga Kependidikan')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Form Edit Tenaga Kependidikan</h3>
        <div class="card-tools">
            <a href="{{ route('gtk.tendik.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
    <div class="card-body">
        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        <form action="{{ route('gtk.tendik.update', $tendik->id) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            
            <!-- IDENTITAS SEKOLAH -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <h6 class="section-title font-weight-bold">IDENTITAS SEKOLAH</h6>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Nama Sekolah <span class="text-danger">*</span></label>
                        <select name="unit_id" id="unit_id" class="form-control @error('unit_id') is-invalid @enderror" required>
                            <option value="">Pilih Unit</option>
                            @foreach($units as $unit)
                                <option value="{{ $unit->id }}" {{ old('unit_id', $tendik->unit_id) == $unit->id ? 'selected' : '' }}>{{ $unit->nama_unit }}</option>
                            @endforeach
                        </select>
                        @error('unit_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- IDENTITAS TENAGA KEPENDIDIKAN -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <h6 class="section-title font-weight-bold">IDENTITAS TENAGA KEPENDIDIKAN</h6>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Nama Lengkap <span class="text-danger">*</span></label>
                        <input type="text" name="nama" class="form-control @error('nama') is-invalid @enderror" value="{{ old('nama', $tendik->nama) }}" required>
                        @error('nama')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>NIK <span class="text-danger">*</span></label>
                        <input type="text" name="nik" class="form-control @error('nik') is-invalid @enderror" value="{{ old('nik', $tendik->nik) }}" required>
                        @error('nik')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Jenis Kelamin <span class="text-danger">*</span></label>
                        <select name="jenis_kelamin" class="form-control @error('jenis_kelamin') is-invalid @enderror" required>
                            <option value="">Pilih Jenis Kelamin</option>
                            <option value="L" {{ old('jenis_kelamin', $tendik->jenis_kelamin) == 'L' ? 'selected' : '' }}>Laki-laki</option>
                            <option value="P" {{ old('jenis_kelamin', $tendik->jenis_kelamin) == 'P' ? 'selected' : '' }}>Perempuan</option>
                        </select>
                        @error('jenis_kelamin')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Tempat Lahir <span class="text-danger">*</span></label>
                        <input type="text" name="tempat_lahir" class="form-control @error('tempat_lahir') is-invalid @enderror" value="{{ old('tempat_lahir', $tendik->tempat_lahir) }}" required>
                        @error('tempat_lahir')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Tanggal Lahir <span class="text-danger">*</span></label>
                        <input type="date" name="tanggal_lahir" class="form-control @error('tanggal_lahir') is-invalid @enderror" value="{{ old('tanggal_lahir', $tendik->tanggal_lahir) }}" required>
                        @error('tanggal_lahir')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>NIP</label>
                        <input type="text" name="nip" class="form-control @error('nip') is-invalid @enderror" value="{{ old('nip', $tendik->nip) }}">
                        @error('nip')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>NIY</label>
                        <input type="text" name="niy" class="form-control @error('niy') is-invalid @enderror" value="{{ old('niy', $tendik->niy) }}">
                        @error('niy')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Agama</label>
                        <select name="agama" class="form-control @error('agama') is-invalid @enderror">
                            <option value="">Pilih Agama</option>
                            <option value="Islam" {{ old('agama', $tendik->agama) == 'Islam' ? 'selected' : '' }}>Islam</option>
                            <option value="Kristen" {{ old('agama', $tendik->agama) == 'Kristen' ? 'selected' : '' }}>Kristen</option>
                            <option value="Katolik" {{ old('agama', $tendik->agama) == 'Katolik' ? 'selected' : '' }}>Katolik</option>
                            <option value="Hindu" {{ old('agama', $tendik->agama) == 'Hindu' ? 'selected' : '' }}>Hindu</option>
                            <option value="Buddha" {{ old('agama', $tendik->agama) == 'Buddha' ? 'selected' : '' }}>Buddha</option>
                            <option value="Konghucu" {{ old('agama', $tendik->agama) == 'Konghucu' ? 'selected' : '' }}>Konghucu</option>
                        </select>
                        @error('agama')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Status Perkawinan</label>
                        <select name="status_kawin" class="form-control @error('status_kawin') is-invalid @enderror">
                            <option value="">Pilih Status</option>
                            <option value="Belum Kawin" {{ old('status_kawin', $tendik->status_kawin) == 'Belum Kawin' ? 'selected' : '' }}>Belum Kawin</option>
                            <option value="Kawin" {{ old('status_kawin', $tendik->status_kawin) == 'Kawin' ? 'selected' : '' }}>Kawin</option>
                            <option value="Cerai Hidup" {{ old('status_kawin', $tendik->status_kawin) == 'Cerai Hidup' ? 'selected' : '' }}>Cerai Hidup</option>
                            <option value="Cerai Mati" {{ old('status_kawin', $tendik->status_kawin) == 'Cerai Mati' ? 'selected' : '' }}>Cerai Mati</option>
                        </select>
                        @error('status_kawin')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- ALAMAT -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <h6 class="section-title font-weight-bold">ALAMAT</h6>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <label>Alamat <span class="text-danger">*</span></label>
                        <textarea name="alamat" class="form-control @error('alamat') is-invalid @enderror" rows="3" required>{{ old('alamat', $tendik->alamat) }}</textarea>
                        @error('alamat')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Desa/Kelurahan</label>
                        <input type="text" name="desa_kelurahan" class="form-control @error('desa_kelurahan') is-invalid @enderror" value="{{ old('desa_kelurahan', $tendik->desa_kelurahan) }}">
                        @error('desa_kelurahan')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Kecamatan</label>
                        <input type="text" name="kecamatan" class="form-control @error('kecamatan') is-invalid @enderror" value="{{ old('kecamatan', $tendik->kecamatan) }}">
                        @error('kecamatan')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Kabupaten/Kota</label>
                        <input type="text" name="kabupaten_kota" class="form-control @error('kabupaten_kota') is-invalid @enderror" value="{{ old('kabupaten_kota', $tendik->kabupaten_kota) }}">
                        @error('kabupaten_kota')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Provinsi</label>
                        <input type="text" name="provinsi" class="form-control @error('provinsi') is-invalid @enderror" value="{{ old('provinsi', $tendik->provinsi) }}">
                        @error('provinsi')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- KONTAK -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <h6 class="section-title font-weight-bold">KONTAK</h6>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" name="email" class="form-control @error('email') is-invalid @enderror" value="{{ old('email', $tendik->email) }}">
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>No. HP</label>
                        <input type="text" name="no_hp" class="form-control @error('no_hp') is-invalid @enderror" value="{{ old('no_hp', $tendik->no_hp) }}">
                        @error('no_hp')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- PENDIDIKAN -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <h6 class="section-title font-weight-bold">PENDIDIKAN</h6>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Pendidikan Terakhir</label>
                        <select name="pendidikan_terakhir" class="form-control @error('pendidikan_terakhir') is-invalid @enderror">
                            <option value="">Pilih Pendidikan</option>
                            <option value="SD" {{ old('pendidikan_terakhir', $tendik->pendidikan_terakhir) == 'SD' ? 'selected' : '' }}>SD</option>
                            <option value="SMP" {{ old('pendidikan_terakhir', $tendik->pendidikan_terakhir) == 'SMP' ? 'selected' : '' }}>SMP</option>
                            <option value="SMA/SMK" {{ old('pendidikan_terakhir', $tendik->pendidikan_terakhir) == 'SMA/SMK' ? 'selected' : '' }}>SMA/SMK</option>
                            <option value="D1" {{ old('pendidikan_terakhir', $tendik->pendidikan_terakhir) == 'D1' ? 'selected' : '' }}>D1</option>
                            <option value="D2" {{ old('pendidikan_terakhir', $tendik->pendidikan_terakhir) == 'D2' ? 'selected' : '' }}>D2</option>
                            <option value="D3" {{ old('pendidikan_terakhir', $tendik->pendidikan_terakhir) == 'D3' ? 'selected' : '' }}>D3</option>
                            <option value="D4/S1" {{ old('pendidikan_terakhir', $tendik->pendidikan_terakhir) == 'D4/S1' ? 'selected' : '' }}>D4/S1</option>
                            <option value="S2" {{ old('pendidikan_terakhir', $tendik->pendidikan_terakhir) == 'S2' ? 'selected' : '' }}>S2</option>
                            <option value="S3" {{ old('pendidikan_terakhir', $tendik->pendidikan_terakhir) == 'S3' ? 'selected' : '' }}>S3</option>
                        </select>
                        @error('pendidikan_terakhir')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Jurusan/Prodi</label>
                        <input type="text" name="jurusan" class="form-control @error('jurusan') is-invalid @enderror" value="{{ old('jurusan', $tendik->jurusan) }}">
                        @error('jurusan')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Perguruan Tinggi</label>
                        <input type="text" name="perguruan_tinggi" class="form-control @error('perguruan_tinggi') is-invalid @enderror" value="{{ old('perguruan_tinggi', $tendik->perguruan_tinggi) }}">
                        @error('perguruan_tinggi')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Tahun Lulus</label>
                        <input type="number" name="tahun_lulus" class="form-control @error('tahun_lulus') is-invalid @enderror" value="{{ old('tahun_lulus', $tendik->tahun_lulus) }}" min="1900" max="{{ date('Y') }}">
                        @error('tahun_lulus')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- KEPEGAWAIAN -->
            <div class="row mb-3">
                <div class="col-md-12">
                    <h6 class="section-title font-weight-bold">KEPEGAWAIAN</h6>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Status Pegawai</label>
                        <select name="status_pegawai" class="form-control @error('status_pegawai') is-invalid @enderror">
                            <option value="">Pilih Status</option>
                            <option value="PNS" {{ old('status_pegawai', $tendik->status_pegawai) == 'PNS' ? 'selected' : '' }}>PNS</option>
                            <option value="CPNS" {{ old('status_pegawai', $tendik->status_pegawai) == 'CPNS' ? 'selected' : '' }}>CPNS</option>
                            <option value="Honorer" {{ old('status_pegawai', $tendik->status_pegawai) == 'Honorer' ? 'selected' : '' }}>Honorer</option>
                            <option value="Magang" {{ old('status_pegawai', $tendik->status_pegawai) == 'Magang' ? 'selected' : '' }}>Magang</option>
                            <option value="Kontrak" {{ old('status_pegawai', $tendik->status_pegawai) == 'Kontrak' ? 'selected' : '' }}>Kontrak</option>
                        </select>
                        @error('status_pegawai')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>TMT PNS</label>
                        <input type="date" name="tmt_pns" class="form-control @error('tmt_pns') is-invalid @enderror" value="{{ old('tmt_pns', $tendik->tmt_pns) }}">
                        @error('tmt_pns')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>TMT CPNS</label>
                        <input type="date" name="tmt_cpns" class="form-control @error('tmt_cpns') is-invalid @enderror" value="{{ old('tmt_cpns', $tendik->tmt_cpns) }}">
                        @error('tmt_cpns')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>TMT Kontrak</label>
                        <input type="date" name="tmt_kontrak" class="form-control @error('tmt_kontrak') is-invalid @enderror" value="{{ old('tmt_kontrak', $tendik->tmt_kontrak) }}">
                        @error('tmt_kontrak')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>TMT Magang</label>
                        <input type="date" name="tmt_magang" class="form-control @error('tmt_magang') is-invalid @enderror" value="{{ old('tmt_magang', $tendik->tmt_magang) }}">
                        @error('tmt_magang')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>TMT Honorer</label>
                        <input type="date" name="tmt_honorer" class="form-control @error('tmt_honorer') is-invalid @enderror" value="{{ old('tmt_honorer', $tendik->tmt_honorer) }}">
                        @error('tmt_honorer')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- SIMPAN -->
            <div class="row">
                <div class="col-md-12">
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

