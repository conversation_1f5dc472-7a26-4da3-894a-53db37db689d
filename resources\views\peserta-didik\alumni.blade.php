@extends('adminlte::page')

@section('title', 'Data Alumni')

@section('content_header')
    <h1>Data Alumni</h1>
@stop

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Daftar Alumni</h3>
                <div class="card-tools">
                    <div class="input-group input-group-sm">
                        <select id="filter-tahun" class="form-control mr-2">
                            <option value="">Se<PERSON><PERSON><PERSON></option>
                            @foreach($tahunKelulusan as $tahun)
                                <option value="{{ $tahun }}">{{ $tahun }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                
                @if(session('error'))
                    <div class="alert alert-danger">{{ session('error') }}</div>
                @endif
                
                <table id="tabel-alumni" class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>NIS</th>
                            <th>NISN</th>
                            <th>Nama</th>
                            <th>Jenis Kelamin</th>
                            <th>Tahun Lulus</th>
                            <th>Kelas Terakhir</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($alumni as $a)
                        <tr>
                            <td>{{ $a->nis }}</td>
                            <td>{{ $a->nisn }}</td>
                            <td>{{ $a->nama }}</td>
                            <td>{{ $a->jenis_kelamin == 'L' ? 'Laki-laki' : 'Perempuan' }}</td>
                            <td>{{ $a->tahun_lulus }}</td>
                            <td>{{ $a->kelas_terakhir }}</td>
                            <td>
                                <button class="btn btn-sm btn-primary" data-toggle="modal" data-target="#detailAlumni{{ $a->id }}" data-toggle="tooltip" title="Detail">
                                    <i class="fas fa-eye"></i>
                                </button>
                                @can('manage-peserta-didik')
                                <a href="{{ route('peserta-didik.alumni.edit', $a->id) }}" class="btn btn-sm btn-warning" data-toggle="tooltip" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('peserta-didik.alumni.destroy', $a->id) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')" data-toggle="tooltip" title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                @endcan
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@foreach($alumni as $a)
<!-- Modal Detail Alumni -->
<div class="modal fade" id="detailAlumni{{ $a->id }}" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detail Data Alumni</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>NIS</label>
                            <p>{{ $a->nis }}</p>
                        </div>
                        <div class="form-group">
                            <label>NISN</label>
                            <p>{{ $a->nisn }}</p>
                        </div>
                        <div class="form-group">
                            <label>Nama Lengkap</label>
                            <p>{{ $a->nama }}</p>
                        </div>
                        <div class="form-group">
                            <label>Jenis Kelamin</label>
                            <p>{{ $a->jenis_kelamin == 'L' ? 'Laki-laki' : 'Perempuan' }}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Tahun Lulus</label>
                            <p>{{ $a->tahun_lulus }}</p>
                        </div>
                        <div class="form-group">
                            <label>Kelas Terakhir</label>
                            <p>{{ $a->kelas_terakhir }}</p>
                        </div>
                        <div class="form-group">
                            <label>Tanggal Kelulusan</label>
                            <p>{{ $a->tanggal_lulus ? date('d-m-Y', strtotime($a->tanggal_lulus)) : '-' }}</p>
                        </div>
                        <div class="form-group">
                            <label>No. Ijazah</label>
                            <p>{{ $a->no_ijazah ?? '-' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>
@endforeach
@stop

@section('css')
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
@stop

@section('js')
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script>
        $(document).ready(function() {
            var table = $('#tabel-alumni').DataTable({
                "responsive": true,
                "lengthChange": true,
                "autoWidth": false,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                }
            });

            // Filter berdasarkan tahun kelulusan
            $('#filter-tahun').on('change', function() {
                var tahun = $(this).val();
                table.column(4) // Sesuaikan dengan index kolom tahun lulus
                    .search(tahun)
                    .draw();
            });
        });
    </script>
@stop