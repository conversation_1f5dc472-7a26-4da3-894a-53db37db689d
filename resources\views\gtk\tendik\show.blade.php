@extends('layouts.admin')

@section('title', 'Detail Tenaga Kependidikan')

@section('page_title', 'Detail Tenaga Kependidikan')
 
@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Informasi Tenaga Kependidikan</h3>
                <div class="card-tools">
                    <a href="{{ route('gtk.tendik.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                    <a href="{{ route('gtk.tendik.edit', $tendik->id) }}" class="btn btn-warning btn-sm">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Informasi Umum -->
                <div class="row">
                    <div class="col-md-12">
                        <h5 class="section-title font-weight-bold mb-3">IDENTITAS SEKOLAH</h5>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 200px">Unit</th>
                                <td>{{ $tendik->unit->nama_unit ?? '-' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5 class="section-title font-weight-bold mb-3">IDENTITAS TENAGA KEPENDIDIKAN</h5>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 200px">Nama</th>
                                <td>{{ $tendik->nama }}</td>
                            </tr>
                            <tr>
                                <th>NIK</th>
                                <td>{{ $tendik->nik }}</td>
                            </tr>
                            <tr>
                                <th>Jenis Kelamin</th>
                                <td>{{ $tendik->jenis_kelamin == 'L' ? 'Laki-laki' : 'Perempuan' }}</td>
                            </tr>
                            <tr>
                                <th>Tempat, Tanggal Lahir</th>
                                <td>{{ $tendik->tempat_lahir }}, {{ \Carbon\Carbon::parse($tendik->tanggal_lahir)->format('d-m-Y') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 200px">Agama</th>
                                <td>{{ $tendik->agama ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>Status Kawin</th>
                                <td>{{ $tendik->status_kawin ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>NIP</th>
                                <td>{{ $tendik->nip ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>NIY</th>
                                <td>{{ $tendik->niy ?? '-' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5 class="section-title font-weight-bold mb-3">ALAMAT</h5>
                    </div>
                    <div class="col-md-12">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 200px">Alamat</th>
                                <td>{{ $tendik->alamat }}</td>
                            </tr>
                            <tr>
                                <th>Desa/Kelurahan</th>
                                <td>{{ $tendik->desa_kelurahan ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>Kecamatan</th>
                                <td>{{ $tendik->kecamatan ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>Kabupaten/Kota</th>
                                <td>{{ $tendik->kabupaten_kota ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>Provinsi</th>
                                <td>{{ $tendik->provinsi ?? '-' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5 class="section-title font-weight-bold mb-3">DATA KEPEGAWAIAN</h5>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 200px">Status Pegawai</th>
                                <td>{{ $tendik->status_pegawai ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>Jabatan</th>
                                <td>{{ $tendik->jabatan ?? '-' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 200px">TMT</th>
                                <td>{{ $tendik->tmt ? \Carbon\Carbon::parse($tendik->tmt)->format('d-m-Y') : '-' }}</td>
                            </tr>
                            <tr>
                                <th>NPWP</th>
                                <td>{{ $tendik->npwp ?? '-' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5 class="section-title font-weight-bold mb-3">PENDIDIKAN</h5>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 200px">Pendidikan Terakhir</th>
                                <td>{{ $tendik->pendidikan_terakhir ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>Jurusan/Prodi</th>
                                <td>{{ $tendik->jurusan ?? '-' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 200px">Perguruan Tinggi</th>
                                <td>{{ $tendik->perguruan_tinggi ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>Tahun Lulus</th>
                                <td>{{ $tendik->tahun_lulus ?? '-' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5 class="section-title font-weight-bold mb-3">KONTAK</h5>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 200px">No. Telepon</th>
                                <td>{{ $tendik->no_telp ?? '-' }}</td>
                            </tr>
                            <tr>
                                <th>Email</th>
                                <td>{{ $tendik->email ?? '-' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 200px">Status</th>
                                <td>
                                    @if($tendik->status == 'Aktif')
                                        <span class="badge badge-success">Aktif</span>
                                    @else
                                        <span class="badge badge-danger">Non-Aktif</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Tambahkan Section Pengembangan Diri -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5 class="section-title font-weight-bold mb-3">PENGEMBANGAN DIRI</h5>
                        <div class="mb-3">
                            <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#modalTambahPengembangan">
                                <i class="fas fa-plus"></i> Tambah Data Pengembangan Diri
                            </button>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="tablePengembanganDiri">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Jenis Diklat</th>
                                        <th>Nama</th>
                                        <th>Penyelenggara</th>
                                        <th>Tingkat</th>
                                        <th>Tahun</th>
                                        <th>Peran</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($pengembanganDiri as $index => $pd)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $pd->jenis_diklat }}</td>
                                        <td>{{ $pd->nama }}</td>
                                        <td>{{ $pd->penyelenggara }}</td>
                                        <td>{{ $pd->tingkat }}</td>
                                        <td>{{ $pd->tahun }}</td>
                                        <td>{{ $pd->peran }}</td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-warning" 
                                                    data-toggle="modal" data-target="#editPengembangan{{ $pd->id }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <form action="{{ route('gtk.tendik.pengembangan-diri.destroy', $pd->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus data pengembangan diri ini?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    
                                    <!-- Modal Edit untuk setiap pengembangan diri -->
                                    <div class="modal fade" id="editPengembangan{{ $pd->id }}" tabindex="-1" role="dialog" aria-labelledby="editPengembangan{{ $pd->id }}Label" aria-hidden="true">
                                        <div class="modal-dialog" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="editPengembangan{{ $pd->id }}Label">Edit Pengembangan Diri</h5>
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>
                                                <form action="{{ route('gtk.tendik.pengembangan-diri.update', $pd->id) }}" method="POST">
                                                    @csrf
                                                    @method('PUT')
                                                    <div class="modal-body">
                                                        <div class="form-group">
                                                            <label for="jenis_diklat{{ $pd->id }}">Jenis Diklat <span class="text-danger">*</span></label>
                                                            <select name="jenis_diklat" id="jenis_diklat{{ $pd->id }}" class="form-control" required>
                                                                <option value="">Pilih Jenis Diklat</option>
                                                                <option value="Diklat Fungsional" {{ $pd->jenis_diklat == 'Diklat Fungsional' ? 'selected' : '' }}>Diklat Fungsional</option>
                                                                <option value="Diklat Teknis" {{ $pd->jenis_diklat == 'Diklat Teknis' ? 'selected' : '' }}>Diklat Teknis</option>
                                                                <option value="Seminar/Workshop" {{ $pd->jenis_diklat == 'Seminar/Workshop' ? 'selected' : '' }}>Seminar/Workshop</option>
                                                                <option value="Bimtek" {{ $pd->jenis_diklat == 'Bimtek' ? 'selected' : '' }}>Bimtek</option>
                                                                <option value="Lainnya" {{ $pd->jenis_diklat == 'Lainnya' ? 'selected' : '' }}>Lainnya</option>
                                                            </select>
                                                        </div>
                                                        <div class="form-group">
                                                            <label for="nama{{ $pd->id }}">Nama Diklat <span class="text-danger">*</span></label>
                                                            <input type="text" name="nama" id="nama{{ $pd->id }}" class="form-control" value="{{ $pd->nama }}" required>
                                                        </div>
                                                        <div class="form-group">
                                                            <label for="penyelenggara{{ $pd->id }}">Penyelenggara <span class="text-danger">*</span></label>
                                                            <input type="text" name="penyelenggara" id="penyelenggara{{ $pd->id }}" class="form-control" value="{{ $pd->penyelenggara }}" required>
                                                        </div>
                                                        <div class="form-group">
                                                            <label for="tingkat{{ $pd->id }}">Tingkat <span class="text-danger">*</span></label>
                                                            <select name="tingkat" id="tingkat{{ $pd->id }}" class="form-control" required>
                                                                <option value="">Pilih Tingkat</option>
                                                                <option value="Kecamatan" {{ $pd->tingkat == 'Kecamatan' ? 'selected' : '' }}>Kecamatan</option>
                                                                <option value="Kabupaten/Kota" {{ $pd->tingkat == 'Kabupaten/Kota' ? 'selected' : '' }}>Kabupaten/Kota</option>
                                                                <option value="Provinsi" {{ $pd->tingkat == 'Provinsi' ? 'selected' : '' }}>Provinsi</option>
                                                                <option value="Nasional" {{ $pd->tingkat == 'Nasional' ? 'selected' : '' }}>Nasional</option>
                                                                <option value="Internasional" {{ $pd->tingkat == 'Internasional' ? 'selected' : '' }}>Internasional</option>
                                                            </select>
                                                        </div>
                                                        <div class="form-group">
                                                            <label for="tahun{{ $pd->id }}">Tahun <span class="text-danger">*</span></label>
                                                            <input type="number" name="tahun" id="tahun{{ $pd->id }}" class="form-control" value="{{ $pd->tahun }}" min="1900" max="{{ date('Y') }}" required>
                                                        </div>
                                                        <div class="form-group">
                                                            <label for="peran{{ $pd->id }}">Peran <span class="text-danger">*</span></label>
                                                            <select name="peran" id="peran{{ $pd->id }}" class="form-control" required>
                                                                <option value="">Pilih Peran</option>
                                                                <option value="Peserta" {{ $pd->peran == 'Peserta' ? 'selected' : '' }}>Peserta</option>
                                                                <option value="Pemateri" {{ $pd->peran == 'Pemateri' ? 'selected' : '' }}>Pemateri</option>
                                                                <option value="Panitia" {{ $pd->peran == 'Panitia' ? 'selected' : '' }}>Panitia</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                                        <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    @empty
                                    <tr>
                                        <td colspan="8" class="text-center">Belum ada data pengembangan diri</td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Tambahkan Section Tugas Tambahan -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h5 class="section-title font-weight-bold mb-3">TUGAS TAMBAHAN</h5>
                        <div class="mb-3">
                            <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#modalTambahTugas">
                                <i class="fas fa-plus"></i> Tambah Tugas Tambahan
                            </button>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="tableTugasTambahan">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Tugas Tambahan</th>
                                        <th>Tahun Ajaran</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($tugasTambahan as $index => $tugas)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $tugas->tugas_tambahan }}</td>
                                        <td>{{ $tugas->tahunAjaran->nama }}</td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-warning" 
                                                    data-toggle="modal" data-target="#editTugas{{ $tugas->id }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <form action="{{ route('gtk.tendik.tugas-tambahan.destroy', $tugas->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus tugas tambahan ini?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    
                                    <!-- Modal Edit untuk setiap tugas tambahan -->
                                    <div class="modal fade" id="editTugas{{ $tugas->id }}" tabindex="-1" role="dialog" aria-labelledby="editTugas{{ $tugas->id }}Label" aria-hidden="true">
                                        <div class="modal-dialog" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="editTugas{{ $tugas->id }}Label">Edit Tugas Tambahan</h5>
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>
                                                <form action="{{ route('gtk.tendik.tugas-tambahan.update', $tugas->id) }}" method="POST">
                                                    @csrf
                                                    @method('PUT')
                                                    <div class="modal-body">
                                                        <div class="form-group">
                                                            <label for="tugas_tambahan{{ $tugas->id }}">Tugas Tambahan <span class="text-danger">*</span></label>
                                                            <input type="text" name="tugas_tambahan" id="tugas_tambahan{{ $tugas->id }}" class="form-control" value="{{ $tugas->tugas_tambahan }}" required>
                                                        </div>
                                                        <div class="form-group">
                                                            <label for="tahun_ajaran_id{{ $tugas->id }}">Tahun Ajaran <span class="text-danger">*</span></label>
                                                            <select name="tahun_ajaran_id" id="tahun_ajaran_id{{ $tugas->id }}" class="form-control" required>
                                                                <option value="">Pilih Tahun Ajaran</option>
                                                                @foreach($tahunAjaranList as $ta)
                                                                    <option value="{{ $ta->id }}" {{ $tugas->tahun_ajaran_id == $ta->id ? 'selected' : '' }}>
                                                                        {{ $ta->nama }} {{ $ta->aktif ? '(Aktif)' : '' }}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                                                        <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    @empty
                                    <tr>
                                        <td colspan="4" class="text-center">Belum ada tugas tambahan</td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('css')
<style>
    /* Gaya tambahan untuk halaman detail */
    .table th {
        background-color: #f4f6f9;
    }
</style>
@endsection

<!-- Modal Tambah Pengembangan Diri -->
<div class="modal fade" id="modalTambahPengembangan" tabindex="-1" role="dialog" aria-labelledby="modalTambahPengembanganLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTambahPengembanganLabel">Tambah Data Pengembangan Diri</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('gtk.tendik.pengembangan-diri.store', $tendik->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="jenis_diklat">Jenis Diklat <span class="text-danger">*</span></label>
                        <select name="jenis_diklat" id="jenis_diklat" class="form-control" required>
                            <option value="">Pilih Jenis Diklat</option>
                            <option value="Diklat Fungsional">Diklat Fungsional</option>
                            <option value="Diklat Teknis">Diklat Teknis</option>
                            <option value="Seminar/Workshop">Seminar/Workshop</option>
                            <option value="Bimtek">Bimtek</option>
                            <option value="Lainnya">Lainnya</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="nama">Nama Diklat <span class="text-danger">*</span></label>
                        <input type="text" name="nama" id="nama" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="penyelenggara">Penyelenggara <span class="text-danger">*</span></label>
                        <input type="text" name="penyelenggara" id="penyelenggara" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="tingkat">Tingkat <span class="text-danger">*</span></label>
                        <select name="tingkat" id="tingkat" class="form-control" required>
                            <option value="">Pilih Tingkat</option>
                            <option value="Kecamatan">Kecamatan</option>
                            <option value="Kabupaten/Kota">Kabupaten/Kota</option>
                            <option value="Provinsi">Provinsi</option>
                            <option value="Nasional">Nasional</option>
                            <option value="Internasional">Internasional</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="tahun">Tahun <span class="text-danger">*</span></label>
                        <input type="number" name="tahun" id="tahun" class="form-control" min="1900" max="{{ date('Y') }}" required>
                    </div>
                    <div class="form-group">
                        <label for="peran">Peran <span class="text-danger">*</span></label>
                        <select name="peran" id="peran" class="form-control" required>
                            <option value="">Pilih Peran</option>
                            <option value="Peserta">Peserta</option>
                            <option value="Pemateri">Pemateri</option>
                            <option value="Panitia">Panitia</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Tambah Tugas Tambahan -->
<div class="modal fade" id="modalTambahTugas" tabindex="-1" role="dialog" aria-labelledby="modalTambahTugasLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTambahTugasLabel">Tambah Tugas Tambahan</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('gtk.tendik.tugas-tambahan.store', $tendik->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="tugas_tambahan">Tugas Tambahan <span class="text-danger">*</span></label>
                        <input type="text" name="tugas_tambahan" id="tugas_tambahan" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="tahun_ajaran_id">Tahun Ajaran <span class="text-danger">*</span></label>
                        <select name="tahun_ajaran_id" id="tahun_ajaran_id" class="form-control" required>
                            <option value="">Pilih Tahun Ajaran</option>
                            @foreach($tahunAjaranList as $ta)
                                <option value="{{ $ta->id }}" {{ $ta->aktif ? 'selected' : '' }}>{{ $ta->nama }} {{ $ta->aktif ? '(Aktif)' : '' }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit untuk setiap tugas tambahan -->
@foreach($tugasTambahan as $tugas)
<div class="modal fade" id="editTugas{{ $tugas->id }}" tabindex="-1" role="dialog" aria-labelledby="editTugas{{ $tugas->id }}Label" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editTugas{{ $tugas->id }}Label">Edit Tugas Tambahan</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('gtk.tendik.tugas-tambahan.update', $tugas->id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="form-group">
                        <label for="tugas_tambahan{{ $tugas->id }}">Tugas Tambahan <span class="text-danger">*</span></label>
                        <input type="text" name="tugas_tambahan" id="tugas_tambahan{{ $tugas->id }}" class="form-control" value="{{ $tugas->tugas_tambahan }}" required>
                    </div>
                    <div class="form-group">
                        <label for="tahun_ajaran_id{{ $tugas->id }}">Tahun Ajaran <span class="text-danger">*</span></label>
                        <select name="tahun_ajaran_id" id="tahun_ajaran_id{{ $tugas->id }}" class="form-control" required>
                            <option value="">Pilih Tahun Ajaran</option>
                            @foreach($tahunAjaranList as $ta)
                                <option value="{{ $ta->id }}" {{ $tugas->tahun_ajaran_id == $ta->id ? 'selected' : '' }}>
                                    {{ $ta->nama }} {{ $ta->aktif ? '(Aktif)' : '' }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endforeach



