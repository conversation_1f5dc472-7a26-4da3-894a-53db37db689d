@extends('layouts.website')

@section('title', 'Fasilitas')

@section('content')
<div class="container py-5">
    <div class="title-container mb-5">
        <div class="text-center">
            <h1 class="facility-title">Fasilitas <PERSON></h1>
            <div class="title-decoration"></div>
        </div>
    </div>
    
    <div class="row">
        @foreach($facilities as $facility)
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                @if($facility->image)
                <img src="{{ asset('storage/facilities/'.$facility->image) }}" 
                     class="card-img-top" 
                     alt="{{ $facility->title }}">
                @endif
                <div class="card-body">
                    <h5 class="card-title">{{ $facility->title }}</h5>
                    <div class="card-text">{!! $facility->description !!}</div>
                </div>
            </div>
        </div>
        @endforeach
    </div>
</div>

<style>
.title-container {
    position: relative;
    padding: 20px 0;
    margin-bottom: 50px;
}

.facility-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.facility-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, #3498db, #2ecc71);
    border-radius: 2px;
}

.title-decoration {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 2px;
    background-color: #ecf0f1;
}

.title-container::before,
.title-container::after {
    content: '★';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24px;
    color: #3498db;
}

.title-container::before {
    left: 25%;
}

.title-container::after {
    right: 25%;
}

@media (max-width: 768px) {
    .facility-title {
        font-size: 2rem;
    }
    
    .title-container::before {
        left: 10%;
    }
    
    .title-container::after {
        right: 10%;
    }
}

/* Animasi hover untuk cards */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.card-img-top {
    height: 250px;
    object-fit: cover;
}
</style>
@endsection