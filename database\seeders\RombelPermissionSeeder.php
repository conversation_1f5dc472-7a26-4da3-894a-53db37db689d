<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RombelPermissionSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles dan permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Daftar permission baru untuk rombongan belajar
        $newPermissions = [
            'view-rombel',      // Untuk melihat data rombel
            'create-rombel',    // Untuk membuat rombel baru
            'edit-rombel',      // Untuk mengedit rombel
            'delete-rombel',    // Untuk menghapus rombel
            'manage-rombel',    // Permission umum untuk mengelola rombel
            'assign-siswa-rombel', // Untuk menambahkan siswa ke rombel
            'remove-siswa-rombel', // Untuk menghapus siswa dari rombel
            'view-daftar-kelas', // Permission khusus untuk melihat daftar kelas
        ];

        // Buat permission baru jika belum ada
        foreach ($newPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permission ke role yang sesuai
        $rolePermissions = [
            'Administrator' => [
                'view-rombel',
                'create-rombel',
                'edit-rombel',
                'delete-rombel',
                'manage-rombel',
                'assign-siswa-rombel',
                'remove-siswa-rombel',
                'view-daftar-kelas',
            ],
            'Kepala Sekolah' => [
                'view-rombel',
                'manage-rombel',
                'assign-siswa-rombel',
                'remove-siswa-rombel',
                'view-daftar-kelas',
            ],
            'Waka Kurikulum' => [
                'view-rombel',
                'create-rombel',
                'edit-rombel',
                'manage-rombel',
                'assign-siswa-rombel',
                'remove-siswa-rombel',
                'view-daftar-kelas',
            ],
            'Waka Kesiswaan' => [
                'view-rombel',
                'assign-siswa-rombel',
                'remove-siswa-rombel',
                'view-daftar-kelas',
            ],
            'Guru' => [
                'view-rombel',
                'view-daftar-kelas',
            ],
            'Tata Usaha' => [
                'view-rombel',
                'create-rombel',
                'edit-rombel',
                'assign-siswa-rombel',
                'remove-siswa-rombel',
                'view-daftar-kelas',
            ],
        ];

        // Assign permission ke role tanpa menghapus permission yang sudah ada
        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Role::firstOrCreate(['name' => $roleName]);
            foreach ($permissions as $permission) {
                if (!$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                }
            }
        }
    }
}
