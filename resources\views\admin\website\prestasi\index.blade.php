@extends('adminlte::page')

@section('title', 'Daftar Prestasi')

@section('content_header')
    <h1>Daftar Prestasi</h1>
@stop

@section('content')
<div class="card">
    <div class="card-header">
        <a href="{{ route('admin.website.prestasi.create') }}" class="btn btn-primary">
            Tambah Prestasi
        </a>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Gambar</th>
                        <th>Judul</th>
                        <th>Tingkat</th>
                        <th>Unit</th>
                        <th>Peserta</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($prestasis as $prestasi)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>
                            @if($prestasi->image)
                                <img src="{{ asset('storage/prestasi/' . $prestasi->image) }}" 
                                     alt="{{ $prestasi->title }}"
                                     class="img-thumbnail"
                                     style="max-height: 100px">
                            @else
                                <span class="text-muted">Tidak ada gambar</span>
                            @endif
                        </td>
                        <td>{{ $prestasi->title }}</td>
                        <td>{{ $prestasi->level }}</td>
                        <td>{{ $prestasi->unit->nama_unit ?? '-' }}</td>
                        <td>{{ $prestasi->participant }}</td>
                        <td>
                            <a href="{{ route('admin.website.prestasi.edit', $prestasi->id) }}" 
                               class="btn btn-sm btn-info">Edit</a>
                            <form action="{{ route('admin.website.prestasi.destroy', $prestasi->id) }}" 
                                  method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-sm btn-danger" 
                                        onclick="return confirm('Yakin ingin menghapus?')">Hapus</button>
                            </form>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center">Tidak ada data</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="mt-3">
            {{ $prestasis->links() }}
        </div>
    </div>
</div>
@stop

@section('css')
<style>
    .img-thumbnail {
        object-fit: cover;
        width: 100px;
        height: 100px;
    }
</style>
@stop
