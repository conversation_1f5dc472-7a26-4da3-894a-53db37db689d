<?php

namespace App\Exports;

use App\Models\PesertaDidik;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class PesertaDidikExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return PesertaDidik::all();
    }

    /**
     * @param PesertaDidik $pesertaDidik
     * @return array
     */
    public function map($pesertaDidik): array
    {
        return [
            $pesertaDidik->nis,
            $pesertaDidik->nisn,
            $pesertaDidik->nik,
            $pesertaDidik->nama,
            $pesertaDidik->jenis_kelamin,
            $pesertaDidik->tempat_lahir,
            $pesertaDidik->tanggal_lahir,
            $pesertaDidik->agama,
            $pesertaDidik->alama<PERSON>,
            $pesertaDidik->rt,
            $pesertaDidik->rw,
            $pesertaDidik->dusun,
            $pesertaDidik->kelurahan,
            $pesertaDidik->kecamatan,
            $pesertaDidik->kode_pos,
            $pesertaDidik->jenis_tinggal,
            $pesertaDidik->transportasi,
            $pesertaDidik->no_telp,
            $pesertaDidik->no_hp,
            $pesertaDidik->email,
            $pesertaDidik->kelas_id,
            $pesertaDidik->tingkat,
            $pesertaDidik->nama_program,
            $pesertaDidik->no_reg,
            $pesertaDidik->unit_id,
            // Tambahkan field lainnya sesuai kebutuhan
        ];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'NIS',
            'NISN',
            'NIK',
            'Nama',
            'Jenis Kelamin',
            'Tempat Lahir',
            'Tanggal Lahir',
            'Agama',
            'Alamat',
            'RT',
            'RW',
            'Dusun',
            'Kelurahan',
            'Kecamatan',
            'Kode Pos',
            'Jenis Tinggal',
            'Transportasi',
            'No. Telepon',
            'No. HP',
            'Email',
            'Kelas ID',
            'Tingkat',
            'Nama Program',
            'No. Registrasi',
            'Unit ID',
            // Tambahkan field lainnya sesuai kebutuhan
        ];
    }
}
