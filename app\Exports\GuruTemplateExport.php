<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class GuruTemplateExport implements FromArray, WithHeadings, WithStyles, ShouldAutoSize
{
    /**
     * @return array
     */
    public function array(): array
    {
        // Contoh data untuk template
        return [
            [
                'Nama Guru', // nama
                'L', // jenis_kelamin (L/P)
                'Tempat Lahir', // tempat_lahir
                '1990-01-01', // tanggal_lahir (format: YYYY-MM-DD)
                'Alamat lengkap', // alamat
                'Ke<PERSON>rahan', // kelurahan
                'Kecamatan', // kecamatan
                'Kabupaten', // kabupaten
                'Provinsi', // provinsi
                'Islam', // agama
                'Indonesia', // kewarganegaraan
                'Belum Kawin', // status_kawin
                'GTY', // status_pegawai
                '12345', // niy
                '1234567890123456', // nuptk
                'Guru Kelas', // jenis_ptk
                '08123456789', // no_telp
                '<EMAIL>', // email
                'Matematika', // mata_pelajaran
                'Aktif', // status
                'SD Pelopor', // nama_unit (harus sesuai dengan nama unit di database)
            ],
        ];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'nama',
            'jenis_kelamin',
            'tempat_lahir',
            'tanggal_lahir',
            'alamat',
            'kelurahan',
            'kecamatan',
            'kabupaten',
            'provinsi',
            'agama',
            'kewarganegaraan',
            'status_kawin',
            'status_pegawai',
            'niy',
            'nuptk',
            'jenis_ptk',
            'no_telp',
            'email',
            'mata_pelajaran',
            'status',
            'unit',
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}

