@extends('layouts.website')

@section('title', $achievement->title)

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6">
            <ol class="flex items-center space-x-2 text-sm text-gray-600">
                <li><a href="{{ route('website.home') }}" class="hover:text-blue-600">Beranda</a></li>
                <li><span class="mx-2">/</span></li>
                <li><a href="{{ route('website.prestasi.all') }}" class="hover:text-blue-600">Prestasi</a></li>
                <li><span class="mx-2">/</span></li>
                <li class="text-gray-900">{{ $achievement->title }}</li>
            </ol>
        </nav>

        <!-- Achievement Header -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
            @if($achievement->image)
            <div class="aspect-video w-full">
                <img src="{{ asset('storage/prestasi/' . $achievement->image) }}" 
                     alt="{{ $achievement->title }}"
                     class="w-full h-full object-cover">
            </div>
            @endif
            
            <div class="p-6">
                <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $achievement->title }}</h1>
                
                <div class="flex flex-wrap gap-4 mb-6">
                    <div class="flex items-center text-sm text-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="font-medium">Tingkat:</span>
                        <span class="ml-1">{{ $achievement->level }}</span>
                    </div>
                    
                    <div class="flex items-center text-sm text-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span class="font-medium">Peserta:</span>
                        <span class="ml-1">{{ $achievement->participant }}</span>
                    </div>
                    
                    @if($achievement->unit)
                    <div class="flex items-center text-sm text-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <span class="font-medium">Unit:</span>
                        <span class="ml-1">{{ $achievement->unit->nama ?? 'Unit tidak ditemukan' }}</span>
                    </div>
                    @endif
                </div>
                
                @if($achievement->description)
                <div class="prose max-w-none">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Deskripsi</h3>
                    <div class="text-gray-700 leading-relaxed">
                        {!! nl2br(e($achievement->description)) !!}
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Back Button -->
        <div class="text-center">
            <a href="{{ route('website.prestasi.all') }}" 
               class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Kembali ke Daftar Prestasi
            </a>
        </div>
    </div>
</div>
@endsection
