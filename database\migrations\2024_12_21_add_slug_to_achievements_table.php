<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use App\Models\Achievement;

return new class extends Migration
{
    public function up()
    {
        Schema::table('achievements', function (Blueprint $table) {
            $table->string('slug')->unique()->after('title');
        });

        // Generate slug untuk data yang sudah ada
        Achievement::chunk(100, function ($achievements) {
            foreach ($achievements as $achievement) {
                $baseSlug = Str::slug($achievement->title);
                $slug = $baseSlug;
                $counter = 1;

                // Pastikan slug unik
                while (Achievement::where('slug', $slug)->where('id', '!=', $achievement->id)->exists()) {
                    $slug = $baseSlug . '-' . $counter;
                    $counter++;
                }

                $achievement->update(['slug' => $slug]);
            }
        });
    }

    public function down()
    {
        Schema::table('achievements', function (Blueprint $table) {
            $table->dropColumn('slug');
        });
    }
};
