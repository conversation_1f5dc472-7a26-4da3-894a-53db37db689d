<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class LaporanKerusakanPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Buat permission untuk laporan kerusakan
        Permission::create(['name' => 'lihat-laporan-kerusakan', 'guard_name' => 'web']);
        Permission::create(['name' => 'buat-laporan-kerusakan', 'guard_name' => 'web']);
        Permission::create(['name' => 'kelola-laporan-kerusakan', 'guard_name' => 'web']);

        // Berikan permission ke role Administrator
        $adminRole = Role::findByName('Administrator', 'web');
        if ($adminRole) {
            $adminRole->givePermissionTo([
                'lihat-laporan-kerusakan', 
                'buat-laporan-kerusakan', 
                'kelola-laporan-kerusakan'
            ]);
        }

        // Berikan permission ke Kepala Sekolah
        $kepsekRole = Role::findByName('Kepala Sekolah', 'web');
        if ($kepsekRole) {
            $kepsekRole->givePermissionTo([
                'lihat-laporan-kerusakan',
                'kelola-laporan-kerusakan'
            ]);
        }

        // Berikan permission ke Waka Sarpras
        $wakaSarprasRole = Role::findByName('Waka Sarpras', 'web');
        if ($wakaSarprasRole) {
            $wakaSarprasRole->givePermissionTo([
                'lihat-laporan-kerusakan',
                'kelola-laporan-kerusakan'
            ]);
        }

        // Berikan permission ke Guru
        $guruRole = Role::findByName('Guru', 'web');
        if ($guruRole) {
            $guruRole->givePermissionTo([
                'lihat-laporan-kerusakan',
                'buat-laporan-kerusakan'
            ]);
        }
    }
}