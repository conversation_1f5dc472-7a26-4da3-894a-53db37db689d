<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UpdateKelasTingkatSeeder extends Seeder
{
    public function run()
    {
        // Define the tingkat values for each kelas ID
        $updates = [
            1 => 'Tingkat 1',
            2 => 'Tingkat 2',
            3 => 'Tingkat 1',
            4 => 'Tingkat 2',
            5 => 'Tingkat 1',
            6 => 'Tingkat 1',
            7 => 'Tingkat 1',
            8 => 'Tingkat 1',
            9 => 'Tingkat Akhir',
        ];
        
        // Update each record individually
        foreach ($updates as $id => $tingkat) {
            DB::table('kelas')
                ->where('id', $id)
                ->update(['tingkat' => $tingkat]);
        }
        
        $this->command->info('Updated tingkat values for kelas records with IDs 1-9.');
    }
}