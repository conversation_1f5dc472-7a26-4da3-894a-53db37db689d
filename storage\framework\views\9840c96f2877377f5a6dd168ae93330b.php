<nav class="main-header navbar
    <?php echo e(config('adminlte.classes_topnav_nav', 'navbar-expand-md')); ?>

    <?php echo e(config('adminlte.classes_topnav', 'navbar-white navbar-light')); ?>">

    <div class="<?php echo e(config('adminlte.classes_topnav_container', 'container')); ?>">
        
        
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link px-1" data-widget="pushmenu" href="#" role="button">
                    <i class="fas fa-bars"></i>
                </a>
            </li>
        </ul>
     
        
        
    
        
        <span class="navbar-text d-none d-md-inline-block" style="font-weight: 600; color: #3c8dbc; font-size: 1.1rem; margin-left: 10px; letter-spacing: 0.5px; text-shadow: 0 1px 1px rgba(0,0,0,0.1);">
            Sistem Informasi Manajemen Akademik Sekolah
        </span>

        
        <button class="navbar-toggler order-1" type="button" data-toggle="collapse" data-target="#navbarCollapse"
                aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        
        <div class="collapse navbar-collapse order-3" id="navbarCollapse">
            
            <ul class="nav navbar-nav">
                
                <?php echo $__env->renderEach('adminlte::partials.navbar.menu-item', $adminlte->menu('navbar-left'), 'item'); ?>

                
                <?php echo $__env->yieldContent('content_top_nav_left'); ?>
            </ul>
        </div>

        
        <ul class="navbar-nav ml-auto order-1 order-md-3 navbar-no-expand">
            
            <?php echo $__env->yieldContent('content_top_nav_right'); ?>
            
            
            <?php if(auth()->guard()->check()): ?>
            <li class="nav-item dropdown">
                <a class="nav-link" data-toggle="dropdown" href="#" aria-expanded="false">
                    <i class="far fa-bell"></i>
                    <span class="badge badge-warning navbar-badge" id="notification-badge" style="display: none;">0</span>
                </a>
                <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right" style="left: inherit; right: 0px;">
                    <span class="dropdown-item dropdown-header">Notifikasi</span>
                    <div class="dropdown-divider"></div>
                    <a href="<?php echo e(route('notifikasi.index')); ?>" class="dropdown-item">
                        <i class="fas fa-envelope mr-2"></i> Lihat Semua Notifikasi
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="<?php echo e(route('notifikasi.mark-all-as-read')); ?>" class="dropdown-item">
                        <i class="fas fa-check mr-2"></i> Tandai Semua Dibaca
                    </a>
                </div>
            </li>
            <?php endif; ?>

            
            <?php echo $__env->renderEach('adminlte::partials.navbar.menu-item', $adminlte->menu('navbar-right'), 'item'); ?>

            
            <?php if(Auth::user()): ?>
                <?php if(config('adminlte.usermenu_enabled')): ?>
                    <?php echo $__env->make('adminlte::partials.navbar.menu-item-dropdown-user-menu', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php else: ?>
                    <?php echo $__env->make('adminlte::partials.navbar.menu-item-logout-link', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php endif; ?>
            <?php endif; ?>

            
            <?php if(config('adminlte.right_sidebar')): ?>
                <?php echo $__env->make('adminlte::partials.navbar.menu-item-right-sidebar-toggler', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>
        </ul>

    </div>

</nav>




<?php /**PATH C:\xampp\htdocs\webplp\resources\views/vendor/adminlte/partials/navbar/navbar.blade.php ENDPATH**/ ?>