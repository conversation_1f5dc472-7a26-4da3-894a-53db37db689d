<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run()
    {
        $this->call([
            SettingsSeeder::class,
            SlideSeeder::class,
            WebsiteSeeder::class,
        ]);
        
        // Seeder untuk mengupdate unit_id
        $this->call([
            UpdateAdmGuruUnitIdSeeder::class,
            UpdateKelasSeeder::class,
            UpdateKelasTingkatSeeder::class,
            // Seeder lainnya...
        ]);
        $this->call(AbsensiPermissionSeeder::class);
        $this->call(PengaturanPermissionSeeder::class);
        $this->call(WebsitePermissionSeeder::class);
        $this->call(SarprasPermissionSeeder::class);
    }
}









