@extends('layouts.admin')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar Event</h3>
        <div class="card-tools">
            <a href="{{ route('admin.website.event.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah Event
            </a>
        </div>
    </div>
    <div class="card-body">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>No</th>
                    <th>Judul</th>
                    <th>Tanggal</th>
                    <th>Lokasi</th>
                    @role('Administrator')
                    <th>Unit</th>
                    @endrole
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
                @foreach($events as $event)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $event->judul }}</td>
                    <td>{{ \Carbon\Carbon::parse($event->tanggal)->format('d/m/Y') }}</td>
                    <td>{{ $event->lokasi }}</td>
                    @role('Administrator')
                    <td>{{ $event->unit->nama_unit ?? '-' }}</td>
                    @endrole
                    <td>
                        <a href="{{ route('admin.website.event.edit', $event->id) }}" 
                           class="btn btn-sm btn-warning">
                            <i class="fas fa-edit"></i>
                        </a>
                        <form action="{{ route('admin.website.event.destroy', $event->id) }}" 
                              method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger" 
                                    onclick="return confirm('Yakin ingin menghapus?')">
                                Hapus
                            </button>
                        </form>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
        {{ $events->links() }}
    </div>
</div>
@endsection


