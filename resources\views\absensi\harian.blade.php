@extends('adminlte::page')

@section('title', 'Absensi Harian')

@section('content_header')
    <h1><PERSON><PERSON><PERSON><PERSON></h1>
@stop

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Form Absensi Harian</h3>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif
        
        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif
        
        <form id="absensiForm">
            @csrf
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="tanggal">Tanggal</label>
                        <input type="date" class="form-control" id="tanggal" name="tanggal" value="{{ $today }}" required>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="kelas_id">Kelas</label>
                        <select class="form-control" id="kelas_id" name="kelas_id" required>
                            <option value="">-- Pilih Kelas --</option>
                            @if($isGuru || isset($isAdmin) && $isAdmin)
                                @foreach($kelasOptions as $id => $nama)
                                    <option value="{{ $id }}">{{ $nama }}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="button" id="btnLoadSiswa" class="btn btn-primary btn-block">
                            <i class="fas fa-search"></i> Tampilkan Siswa
                        </button>
                    </div>
                </div>
            </div>
        </form>
        
        <div id="absensiContainer" class="mt-4" style="display: none;">
            <form id="absensiSiswaForm">
                @csrf
                <input type="hidden" name="kelas_id" id="form_kelas_id">
                <input type="hidden" name="tanggal" id="form_tanggal">
                
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th width="5%">No</th>
                                <th width="15%">NISN</th>
                                <th width="40%">Nama Siswa</th>
                                <th width="20%">Status</th>
                                <th width="20%">Keterangan</th>
                            </tr>
                        </thead>
                        <tbody id="absensiTableBody">
                            <!-- Data siswa akan ditampilkan di sini -->
                        </tbody>
                    </table>
                </div>
                
                <div class="text-right mt-3">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Simpan Absensi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('css')
<style>
    .bg-success.text-white {
        background-color: #28a745 !important;
        color: white !important;
    }
    
    .bg-success.text-white option {
        background-color: white;
        color: black;
    }
    
    .table td {
        vertical-align: middle;
    }
</style>
@stop

@section('js')
<script>
$(function() {
    // Load students when button is clicked
    $('#btnLoadSiswa').click(function() {
        const kelasId = $('#kelas_id').val();
        const tanggal = $('#tanggal').val();
        
        if (!kelasId) {
            alert('Silakan pilih kelas terlebih dahulu');
            return;
        }
        
        // Set form values
        $('#form_kelas_id').val(kelasId);
        $('#form_tanggal').val(tanggal);
        
        // Load students
        $.ajax({
            url: "{{ route('absensi.get-students') }}",
            type: "GET",
            data: {
                kelas_id: kelasId,
                tanggal: tanggal
            },
            dataType: "json",
            beforeSend: function() {
                $('#absensiTableBody').html('<tr><td colspan="5" class="text-center">Loading...</td></tr>');
                $('#absensiContainer').show();
            },
            success: function(response) {
                let html = '';
                
                if (response.length === 0) {
                    html = '<tr><td colspan="5" class="text-center">Tidak ada data siswa</td></tr>';
                } else {
                    $.each(response, function(index, siswa) {
                        // Tentukan apakah siswa sudah diabsen sebelumnya
                        const isRecorded = siswa.status !== null;
                        const statusClass = isRecorded ? 'bg-success text-white' : '';
                        
                        html += `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${siswa.nisn}</td>
                                <td>${siswa.nama}</td>
                                <td>
                                    <input type="hidden" name="absensi[${index}][siswa_id]" value="${siswa.id}">
                                    <select class="form-control ${statusClass}" name="absensi[${index}][status]" required>
                                        <option value="hadir" ${siswa.status === 'hadir' ? 'selected' : ''}>Hadir</option>
                                        <option value="sakit" ${siswa.status === 'sakit' ? 'selected' : ''}>Sakit</option>
                                        <option value="izin" ${siswa.status === 'izin' ? 'selected' : ''}>Izin</option>
                                        <option value="alpa" ${siswa.status === 'alpa' ? 'selected' : ''}>Alpa</option>
                                    </select>
                                </td>
                                <td>
                                    <input type="text" class="form-control ${statusClass}" name="absensi[${index}][keterangan]" 
                                           value="${siswa.keterangan || ''}">
                                </td>
                            </tr>
                        `;
                    });
                }
                
                $('#absensiTableBody').html(html);
            },
            error: function(xhr) {
                alert('Terjadi kesalahan saat memuat data siswa');
                console.error(xhr.responseText);
            }
        });
    });
    
    // Submit attendance form
    $('#absensiSiswaForm').submit(function(e) {
        e.preventDefault();
        
        $.ajax({
            url: "{{ route('absensi.save') }}",
            type: "POST",
            data: $(this).serialize(),
            dataType: "json",
            beforeSend: function() {
                $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Menyimpan...');
            },
            success: function(response) {
                if (response.success) {
                    alert('Absensi berhasil disimpan');
                    // Reload students to show updated data
                    $('#btnLoadSiswa').click();
                } else {
                    alert('Gagal menyimpan absensi: ' + response.message);
                }
            },
            error: function(xhr) {
                alert('Terjadi kesalahan saat menyimpan absensi');
                console.error(xhr.responseText);
            },
            complete: function() {
                $('button[type="submit"]').prop('disabled', false).html('<i class="fas fa-save"></i> Simpan Absensi');
            }
        });
    });
});
</script>
@stop



