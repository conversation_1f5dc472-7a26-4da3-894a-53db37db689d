@extends('layouts.website')

@section('content')
<div class="container py-5">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="sidebar-heading mb-3">
                <h5 class="fw-bold text-dark p-2 border-bottom border-primary">
                    {{ $unit->nama_unit }}
                </h5>
            </div>
            <div class="list-group">
                <a href="{{ route('website.jenjang.profil', $jenjang) }}" 
                   class="list-group-item list-group-item-action">
                    Profil
                </a>
                <a href="{{ route('website.jenjang.visi-misi', $jenjang) }}" 
                   class="list-group-item list-group-item-action">
                    Visi & Misi
                </a>
                <a href="{{ route('website.jenjang.sejarah', $jenjang) }}" 
                   class="list-group-item list-group-item-action">
                    Se<PERSON><PERSON>
                </a>
                <a href="{{ route('website.jenjang.artikel', $jenjang) }}" 
                   class="list-group-item list-group-item-action">
                    Artikel & Pengumuman
                </a>
                <a href="{{ route('website.jenjang.event', $jenjang) }}" 
                   class="list-group-item list-group-item-action">
                    Event
                </a>
                <a href="{{ route('website.jenjang.prestasi', $jenjang) }}" 
                   class="list-group-item list-group-item-action">
                    Prestasi
                </a>
                <a href="{{ route('website.jenjang.ekstrakurikuler', $jenjang) }}" 
                   class="list-group-item list-group-item-action active">
                    Ekstrakurikuler
                </a>
            </div>
        </div>

        <!-- Content -->
        <div class="col-md-9">
            <h2>Ekstrakurikuler {{ $unit->nama_unit }}</h2>
            <div class="row mt-4">
                @forelse($ekstrakurikuler as $ekskul)
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            @if($ekskul->gambar)
                                <img src="{{ Storage::url($ekskul->gambar) }}" 
                                     class="card-img-top" 
                                     alt="{{ $ekskul->nama }}">
                            @endif
                            <div class="card-body">
                                <h5 class="card-title">{{ $ekskul->nama }}</h5>
                                <p class="card-text">{{ $ekskul->deskripsi }}</p>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12">
                        <p class="text-center">Belum ada data ekstrakurikuler untuk {{ $unit->nama_unit }}</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>
@endsection


