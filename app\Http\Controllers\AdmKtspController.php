<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Models\AdmKtsp;
use Carbon\Carbon;
use App\Traits\FiltersByUserUnit;

class AdmKtspController extends Controller
{
    use FiltersByUserUnit;
    
    public function index()
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        $query = AdmKtsp::with('user')->latest();
        
        // Jika user bukan admin, yayasan, pengawas, atau kepala sekolah, hanya tampilkan ADM miliknya
        if (!$user->hasAnyRole(['Administrator', 'Kepala Sekolah', 'Yayasan', 'Pengawas', 'Waka Kurikulum', 'Waka Kesiswaan', 'Waka Sarpras'])) {
            $query->where('user_id', $user->id);
        } else {
            // Terapkan filter unit menggunakan trait
            $query = $this->applyUnitFilter($query);
        }
        
        $admList = $query->get();
        return view('adm.ktsp.index', compact('admList'));
    }

    public function store(Request $request)
    {
        // Validasi apakah user memiliki izin untuk upload
        if (!auth()->user()->hasPermissionTo('upload-adm-ktsp')) {
            return redirect()->route('dashboard')
                ->with('error', 'Anda tidak memiliki izin untuk upload KTSP.');
        }

        $request->validate([
            'judul' => 'required|string|max:255',
            'keterangan' => 'nullable|string',
            'file' => 'required|file|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx|max:10240', // Maksimal 10MB
        ]);

        try {
            $file = $request->file('file');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('adm_ktsp', $fileName, 'public');

            AdmKtsp::create([
                'user_id' => auth()->id(),
                'judul' => $request->judul,
                'keterangan' => $request->keterangan,
                'file_path' => $filePath,
                'status' => 'pending',
            ]);

            return redirect()->route('adm.ktsp.index')
                ->with('success', 'KTSP berhasil diupload');
        } catch (\Exception $e) {
            Log::error('Error in AdmKtspController@store: ' . $e->getMessage());
            return redirect()->route('adm.ktsp.index')
                ->with('error', 'Terjadi kesalahan saat upload KTSP: ' . $e->getMessage());
        }
    }

    public function viewFile($filename)
    {
        try {
            $path = storage_path('app/public/adm_ktsp/' . $filename);
            
            if (!file_exists($path)) {
                abort(404, 'File tidak ditemukan');
            }

            $extension = pathinfo($path, PATHINFO_EXTENSION);
            $contentType = $this->getContentType($extension);

            return response()->file($path, [
                'Content-Type' => $contentType,
                'Content-Disposition' => 'inline; filename="' . $filename . '"'
            ]);
        } catch (\Exception $e) {
            Log::error('Error viewing file: ' . $e->getMessage());
            return response()->json(['error' => 'File tidak dapat diakses'], 404);
        }
    }

    private function getContentType($extension)
    {
        $extension = strtolower($extension);
        
        $contentTypes = [
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt' => 'application/vnd.ms-powerpoint',
            'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        ];
        
        return $contentTypes[$extension] ?? 'application/octet-stream';
    }

    public function viewPage($filename)
    {
        try {
            $filePath = 'adm_ktsp/' . $filename;
            
            if (!Storage::disk('public')->exists($filePath)) {
                abort(404, 'File tidak ditemukan');
            }
            
            return view('adm.ktsp.view-pdf', compact('filename'));
        } catch (\Exception $e) {
            Log::error('Error in viewPage: ' . $e->getMessage());
            return redirect()->route('adm.ktsp.index')
                ->with('error', 'File tidak dapat ditampilkan');
        }
    }

    public function download($filename)
    {
        try {
            $path = storage_path('app/public/adm_ktsp/' . $filename);
            
            if (!file_exists($path)) {
                abort(404, 'File tidak ditemukan');
            }

            return response()->download($path);
        } catch (\Exception $e) {
            Log::error('Error downloading file: ' . $e->getMessage());
            return redirect()->route('adm.ktsp.index')
                ->with('error', 'File tidak dapat diunduh');
        }
    }

    public function delete(AdmKtsp $adm)
    {
        // Cek apakah user adalah pemilik file atau admin
        if (auth()->id() != $adm->user_id && !auth()->user()->hasRole('Administrator')) {
            return redirect()->route('adm.ktsp.index')
                ->with('error', 'Anda tidak memiliki izin untuk menghapus file ini');
        }

        try {
            // Hapus file dari storage
            if (Storage::disk('public')->exists($adm->file_path)) {
                Storage::disk('public')->delete($adm->file_path);
            }
            
            // Hapus record dari database
            $adm->delete();

            return redirect()->route('adm.ktsp.index')
                ->with('success', 'KTSP berhasil dihapus');
        } catch (\Exception $e) {
            Log::error('Error in AdmKtspController@delete: ' . $e->getMessage());
            return redirect()->route('adm.ktsp.index')
                ->with('error', 'Terjadi kesalahan saat menghapus KTSP');
        }
    }
}



