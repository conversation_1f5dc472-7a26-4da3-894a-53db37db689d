@extends('adminlte::page')

@section('title', 'Daftar Jurnal Kegiatan')

@section('content_header')
    <h1>Daftar Jurnal Kegiatan</h1>
@stop

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Jurnal Kegiatan</h3>
                <div class="card-tools">
                    @can('manage-jurnal')
                    <a href="{{ route('jurnal.create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Tambah Jurnal
                    </a>
                    @endcan
                </div>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Tanggal</th>
                            <th>Unit</th>
                            <th>Kegiatan</th>
                            <th>Ada</th>
                            <th>Tidak</th>
                            <th>Keterangan</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($jurnals as $jurnal)
                        <tr>
                            <td>{{ $loop->iteration }}</td>
                            <td>{{ \Carbon\Carbon::parse($jurnal->tanggal)->format('d/m/Y') }}</td>
                            <td>{{ $jurnal->unit->nama_unit ?? '-' }}</td>
                            <td>
                                @if(strlen($jurnal->kegiatan) > 100)
                                    {{ Str::limit($jurnal->kegiatan, 100) }}
                                    <a href="{{ route('jurnal.show', $jurnal->id) }}" class="text-primary">Selengkapnya</a>
                                @else
                                    {{ $jurnal->kegiatan }}
                                @endif
                            </td>
                            <td class="text-center">
                                @if($jurnal->ada)
                                    <i class="fas fa-check text-success"></i>
                                @endif
                            </td>
                            <td class="text-center">
                                @if($jurnal->tidak)
                                    <i class="fas fa-times text-danger"></i>
                                @endif
                            </td>
                            <td>{{ $jurnal->keterangan }}</td>
                            <td>
                                @if($jurnal->status == 'draft')
                                    <span class="badge badge-secondary">Draft</span>
                                @elseif($jurnal->status == 'submitted')
                                    <span class="badge badge-info">Diajukan</span>
                                @elseif($jurnal->status == 'approved')
                                    <span class="badge badge-success">Disetujui</span>
                                @elseif($jurnal->status == 'rejected')
                                    <span class="badge badge-danger" 
                                          data-toggle="tooltip" 
                                          data-html="true"
                                          title="<strong>Ditolak oleh:</strong> {{ $jurnal->rejector->name ?? 'N/A' }}<br>
                                                 <strong>Alasan:</strong> {{ $jurnal->alasan_penolakan ?? 'N/A' }}<br>
                                                 <strong>Pada:</strong> {{ $jurnal->rejected_at ? \Carbon\Carbon::parse($jurnal->rejected_at)->format('d/m/Y H:i') : 'N/A' }}">
                                        Ditolak
                                    </span>
                                @endif
                            </td>
                            <td>
                                <a href="{{ route('jurnal.show', $jurnal->id) }}" class="btn btn-sm btn-info" data-toggle="tooltip" title="Lihat Detail">
                                    <i class="fas fa-eye"></i>
                                </a>
                                
                                @if($jurnal->status == 'draft' || $jurnal->status == 'rejected')
                                    @can('manage-jurnal')
                                    <a href="{{ route('jurnal.edit', $jurnal->id) }}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    
                                    <form action="{{ route('jurnal.destroy', $jurnal->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Yakin ingin menghapus?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    @endcan
                                    
                                    <!-- Tombol submit individual -->
                                    <form action="{{ route('jurnal.submitIndividual', $jurnal->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-sm btn-primary" onclick="return confirm('Yakin ingin mengirim jurnal ini untuk persetujuan?')">
                                            <i class="fas fa-paper-plane"></i> Submit
                                        </button>
                                    </form>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                
                {{ $jurnals->links() }}
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="{{ asset('css/admin_custom.css') }}">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            // Initialize tooltips with HTML support
            $('[data-toggle="tooltip"]').tooltip({
                html: true,
                container: 'body'
            });
            
            $('table').DataTable({
                "paging": false,
                "lengthChange": false,
                "searching": true,
                "ordering": true,
                "info": false,
                "autoWidth": false,
                "responsive": true,
            });
        });
    </script>
@stop





