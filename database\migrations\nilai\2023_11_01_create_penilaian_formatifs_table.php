<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('penilaian_formatifs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('siswa_id')->constrained('peserta_didiks')->onDelete('cascade');
            $table->foreignId('kelas_id')->constrained('kelas')->onDelete('cascade');
            $table->foreignId('mata_pelajaran_id')->constrained('mata_pelajarans')->onDelete('cascade');
            $table->foreignId('tujuan_pembelajaran_id')->constrained('tujuan_pembelajarans')->onDelete('cascade');
            $table->date('tanggal');
            $table->enum('dimensi', ['pengetahuan', 'keterampilan', 'sikap']);
            $table->enum('nilai', ['BP', 'MB', 'BSH', 'SB']); // Skala capaian kompetensi
            $table->text('deskripsi')->nullable();
            $table->text('umpan_balik')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('penilaian_formatifs');
    }
};