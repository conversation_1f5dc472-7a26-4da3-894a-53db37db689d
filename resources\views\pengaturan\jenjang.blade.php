@extends('layouts.admin')

@section('title', 'Data Jenjang')

@section('content')
@if(session('error'))
    <div class="alert alert-danger">
        {{ session('error') }}
    </div>
@endif

@if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
@endif

<div class="card">
    <div class="card-header">
        <h3 class="card-title">Data Jenjang</h3>
        <div class="card-tools">
            <button class="btn btn-primary" data-toggle="modal" data-target="#tambahJenjang">
                Tambah Jenjang
            </button>
        </div>
    </div>
    <div class="card-body">
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>No</th>
                    <th>Jenjang</th>
                    <th>Tingkat</th>
                    <th>Nama Jenjang</th>
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
                @forelse($jenjang as $key => $item)
                    <tr>
                        <td>{{ $key + 1 }}</td>
                        <td>{{ $item->jenjang }}</td>
                        <td>{{ $item->tingkat }}</td>
                        <td>{{ $item->nama_jenjang }}</td>
                        <td>
                            <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#editJenjang{{ $item->id }}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <form action="{{ route('jenjang.destroy', $item->id) }}" method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Yakin hapus data?')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" class="text-center">Tidak ada data</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>

<!-- Modal Tambah -->
<div class="modal fade" id="tambahJenjang" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Jenjang</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('jenjang.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label>Jenjang</label>
                        <select name="jenjang" class="form-control @error('jenjang') is-invalid @enderror" required>
                            <option value="">Pilih Jenjang</option>
                            <option value="PG">PG</option>
                            <option value="SD">SD</option>
                            <option value="SMP">SMP</option>
                            <option value="SMA">SMA</option>
                        </select>
                        @error('jenjang')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label>Tingkat</label>
                        <select name="tingkat" class="form-control @error('tingkat') is-invalid @enderror" required>
                            <option value="">Pilih Tingkat</option>
                            <option value="Tingkat 1">Tingkat 1</option>
                            <option value="Tingkat 2">Tingkat 2</option>
                            <option value="Tingkat 3">Tingkat 3</option>
                            <option value="Tingkat 4">Tingkat 4</option>
                            <option value="Tingkat 5">Tingkat 5</option>
                            <option value="Tingkat 6">Tingkat 6</option>
                            <option value="Tingkat Akhir">Tingkat Akhir</option>
                        </select>
                        @error('tingkat')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit -->
@foreach($jenjang as $item)
<div class="modal fade" id="editJenjang{{ $item->id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Jenjang</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('jenjang.update', $item->id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="form-group">
                        <label>Jenjang</label>
                        <select name="jenjang" class="form-control @error('jenjang') is-invalid @enderror" required>
                            <option value="">Pilih Jenjang</option>
                            <option value="PG" {{ $item->jenjang == 'PG' ? 'selected' : '' }}>PG</option>
                            <option value="SD" {{ $item->jenjang == 'SD' ? 'selected' : '' }}>SD</option>
                            <option value="SMP" {{ $item->jenjang == 'SMP' ? 'selected' : '' }}>SMP</option>
                            <option value="SMA" {{ $item->jenjang == 'SMA' ? 'selected' : '' }}>SMA</option>
                        </select>
                        @error('jenjang')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label>Tingkat</label>
                        <select name="tingkat" class="form-control @error('tingkat') is-invalid @enderror" required>
                            <option value="">Pilih Tingkat</option>
                            <option value="Tingkat 1" {{ $item->tingkat == 'Tingkat 1' ? 'selected' : '' }}>Tingkat 1</option>
                            <option value="Tingkat 2" {{ $item->tingkat == 'Tingkat 2' ? 'selected' : '' }}>Tingkat 2</option>
                            <option value="Tingkat 3" {{ $item->tingkat == 'Tingkat 3' ? 'selected' : '' }}>Tingkat 3</option>
                            <option value="Tingkat 4" {{ $item->tingkat == 'Tingkat 4' ? 'selected' : '' }}>Tingkat 4</option>
                            <option value="Tingkat 5" {{ $item->tingkat == 'Tingkat 5' ? 'selected' : '' }}>Tingkat 5</option>
                            <option value="Tingkat 6" {{ $item->tingkat == 'Tingkat 6' ? 'selected' : '' }}>Tingkat 6</option>
                            <option value="Tingkat Akhir" {{ $item->tingkat == 'Tingkat Akhir' ? 'selected' : '' }}>Tingkat Akhir</option>
                        </select>
                        @error('tingkat')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endforeach
@endsection

@section('css')
<link rel="stylesheet" href="{{ asset('vendor/datatables-bs4/css/dataTables.bootstrap4.min.css') }}">
@endsection

@section('js')
<script src="{{ asset('vendor/datatables/jquery.dataTables.min.js') }}"></script>
<script src="{{ asset('vendor/datatables-bs4/js/dataTables.bootstrap4.min.js') }}"></script>
<script>
    $(function() {
        $('.table').DataTable();
    });
</script>
@endsection
