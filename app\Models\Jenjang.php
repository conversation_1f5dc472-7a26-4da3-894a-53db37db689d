<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Jenjang extends Model
{
    protected $table = 'jenjangs';
    protected $fillable = ['jenjang', 'tingkat'];

    /**
     * Boot method untuk model Jenjang
     * Mengisi nama_jenjang berdasarkan kombinasi jenjang dan tingkat
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($jenjang) {
            $jenjang->nama_jenjang = $jenjang->jenjang . ' ' . $jenjang->tingkat;
        });

        static::updating(function ($jenjang) {
            $jenjang->nama_jenjang = $jenjang->jenjang . ' ' . $jenjang->tingkat;
        });
    }

    public function kelas()
    {
        return $this->hasMany(Kelas::class);
    }
}

