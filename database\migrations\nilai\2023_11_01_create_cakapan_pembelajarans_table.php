<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cakapan_pembelajarans', function (Blueprint $table) {
            $table->id();
            $table->foreignId('kompetensi_dasar_id')->constrained('kompetensi_dasars')->onDelete('cascade');
            $table->text('deskripsi');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cakapan_pembelajarans');
    }
};