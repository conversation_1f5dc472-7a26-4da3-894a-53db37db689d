<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AdmGuru;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateAdmGuruUnitIdSeeder extends Seeder
{
    public function run()
    {
        // Ambil semua record AdmGuru yang belum memiliki unit_id
        $admGurus = AdmGuru::whereNull('unit_id')->get();
        $count = 0;
        
        foreach ($admGurus as $adm) {
            // Cari unit_id dari user terkait
            $user = User::find($adm->user_id);
            
            if ($user && $user->unit_id) {
                // Update record AdmGuru dengan unit_id dari user
                $adm->update(['unit_id' => $user->unit_id]);
                $count++;
            } else {
                Log::info('User tidak memiliki unit_id', [
                    'adm_id' => $adm->id,
                    'user_id' => $adm->user_id
                ]);
            }
        }
        
        $this->command->info("Updated unit_id for {$count} of " . count($admGurus) . " AdmGuru records.");
    }
}