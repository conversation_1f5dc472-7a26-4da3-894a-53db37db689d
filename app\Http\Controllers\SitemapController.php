<?php

namespace App\Http\Controllers;

use App\Models\Article;
use App\Models\Achievement;
use Illuminate\Http\Request;

class SitemapController extends Controller
{
    public function index()
    {
        $articles = Article::where('status', 'published')->get();
        $achievements = Achievement::whereNotNull('slug')->where('slug', '!=', '')->get();

        return response()->view('sitemap.index', compact('articles', 'achievements'))
                         ->header('Content-Type', 'text/xml');
    }
}