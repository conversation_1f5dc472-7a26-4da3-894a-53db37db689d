<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class ExportTemplateController extends Controller
{
    /**
     * Download template Excel untuk import data guru
     */
    public function guruTemplate()
    {
        try {
            Log::info('Starting guru template export');
            
            // Buat spreadsheet baru
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            
            // Tambahkan header sesuai dengan form create
            $headers = [
                // IDENTITAS GURU
                'nama',
                'nik',
                'jenis_kelamin',
                'tempat_lahir',
                'tanggal_lahir',
                'nama_ibu_kandung',
                
                // ALAMAT
                'alamat',
                'kel<PERSON>han',
                'kecamatan',
                'kabupaten',
                'provinsi',
                
                // DATA PRIBADI
                'agama',
                'npwp',
                'nama_wajib_pajak',
                'kewarganegaraan',
                'status_kawin',
                'nama_pasangan',
                'pekerjaan_pasangan',
                
                // KEPEGAWAIAN
                'status_pegawai',
                'niy',
                'nuptk',
                'jenis_ptk',
                'sk_pengangkatan',
                'tmt_pengangkatan',
                'lembaga_pengangkat',
                'pangkat_golongan',
                
                // PENUGASAN
                'sk_penugasan',
                'tmt_penugasan',
                'lembaga_penugasan',
                'status',
                
                // KONTAK
                'no_telp',
                'email',
                
                // MATA PELAJARAN
                'mata_pelajaran',
                
                // UNIT
                'unit',
            ];
            
            // Tulis header ke spreadsheet
            foreach ($headers as $index => $header) {
                $sheet->setCellValue(chr(65 + ($index % 26)) . (floor($index / 26) + 1) . '1', $header);
            }
            
            // Tambahkan contoh data
            $exampleData = [
                // IDENTITAS GURU
                'Nama Guru',
                '1234567890123456',
                'L',
                'Tempat Lahir',
                '1990-01-01',
                'Nama Ibu Kandung',
                
                // ALAMAT
                'Alamat lengkap',
                'Kelurahan',
                'Kecamatan',
                'Kabupaten',
                'Provinsi',
                
                // DATA PRIBADI
                'Islam',
                '12.345.678.9-123.000',
                'Nama Wajib Pajak',
                'Indonesia',
                'Belum Kawin',
                'Nama Pasangan',
                'Pekerjaan Pasangan',
                
                // KEPEGAWAIAN
                'Tetap',
                '12345',
                '1234567890123456',
                'Guru Kelas',
                'SK-123/ABC/2020',
                '2020-01-01',
                'Yayasan Pelopor',
                'III/a',
                
                // PENUGASAN
                'SK-123/DEF/2020',
                '2020-01-01',
                'Yayasan Pelopor',
                'Aktif',
                
                // KONTAK
                '08123456789',
                '<EMAIL>',
                
                // MATA PELAJARAN
                'Matematika',
                
                // UNIT
                'SD Pelopor',
            ];
            
            // Tulis contoh data ke spreadsheet
            foreach ($exampleData as $index => $value) {
                $sheet->setCellValue(chr(65 + ($index % 26)) . (floor($index / 26) + 1) . '2', $value);
            }
            
            // Format header dengan bold
            $sheet->getStyle('A1:' . chr(65 + (count($headers) - 1) % 26) . (floor((count($headers) - 1) / 26) + 1) . '1')->getFont()->setBold(true);
            
            // Auto-size kolom
            foreach (range('A', chr(65 + min(25, count($headers) - 1))) as $column) {
                $sheet->getColumnDimension($column)->setAutoSize(true);
            }
            
            // Jika ada lebih dari 26 kolom, atur kolom AA, AB, dst
            if (count($headers) > 26) {
                foreach (range(0, floor((count($headers) - 1) / 26) - 1) as $i) {
                    foreach (range('A', chr(65 + min(25, count($headers) - 1 - ($i + 1) * 26))) as $column) {
                        $sheet->getColumnDimension(chr(65 + $i) . $column)->setAutoSize(true);
                    }
                }
            }
            
            // Simpan file ke temporary file
            $tempFile = tempnam(sys_get_temp_dir(), 'template-guru-');
            $writer = new Xlsx($spreadsheet);
            $writer->save($tempFile);
            
            Log::info('Template created successfully at: ' . $tempFile);
            
            // Return file sebagai response
            return response()->download($tempFile, 'template-import-guru.xlsx', [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ])->deleteFileAfterSend(true);
            
        } catch (\Exception $e) {
            Log::error('Error creating guru template: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return redirect()->route('gtk.guru.index')
                ->with('error', 'Gagal mengunduh template: ' . $e->getMessage());
        }
    }
}
