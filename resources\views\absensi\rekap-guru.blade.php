@extends('adminlte::page')

@section('title', 'Rekap Absensi Guru')

@section('content_header')
    <h1><PERSON><PERSON><PERSON> Absensi Guru</h1>
@stop

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Filter Rekap Absensi Guru</h3>
    </div>
    <div class="card-body">
        <form id="rekapForm">
            <div class="row">
                @if(auth()->user()->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']))
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="unit_id">Unit</label>
                        <select class="form-control" id="unit_id" name="unit_id">
                            <option value="">-- Semua Unit --</option>
                            @foreach($unitOptions as $id => $nama)
                                <option value="{{ $id }}">{{ $nama }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                @endif
                
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="bulan">Bulan</label>
                        <select class="form-control" id="bulan" name="bulan" required>
                            @foreach($namaBulan as $key => $nama)
                                <option value="{{ $key }}" {{ $bulan == $key ? 'selected' : '' }}>{{ $nama }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="tahun">Tahun</label>
                        <select class="form-control" id="tahun" name="tahun" required>
                            @php
                                $tahunSekarang = date('Y');
                                $tahunMulai = $tahunSekarang - 5;
                            @endphp
                            @for($i = $tahunSekarang; $i >= $tahunMulai; $i--)
                                <option value="{{ $i }}" {{ $tahun == $i ? 'selected' : '' }}>{{ $i }}</option>
                            @endfor
                        </select>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="button" id="btnTampilkanRekap" class="btn btn-primary btn-block">
                            <i class="fas fa-search"></i> Tampilkan Rekap
                        </button>
                    </div>
                </div>
            </div>
        </form>
        
        <div id="rekapContainer" class="mt-4" style="display: none;">
            <div class="text-right mb-3">
                <button type="button" id="btnExportExcel" class="btn btn-success">
                    <i class="fas fa-file-excel"></i> Export Excel
                </button>
                <button type="button" id="btnPrint" class="btn btn-info">
                    <i class="fas fa-print"></i> Cetak
                </button>
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="rekapTable">
                    <thead>
                        <tr>
                            <th rowspan="2" class="text-center align-middle">No</th>
                            <th rowspan="2" class="text-center align-middle">NIP</th>
                            <th rowspan="2" class="text-center align-middle">Nama Guru</th>
                            @if(auth()->user()->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']))
                            <th rowspan="2" class="text-center align-middle">Unit</th>
                            @endif
                            <th colspan="{{ $totalDays ?? 31 }}" class="text-center" id="bulanTahunHeader">
                                {{ isset($namaBulan[$bulan]) ? $namaBulan[$bulan] . ' ' . $tahun : 'Tanggal' }}
                            </th>
                            <th colspan="4" class="text-center">Jumlah</th>
                        </tr>
                        <tr id="tanggalHeader">
                            @if(isset($totalDays))
                                @for($i = 1; $i <= $totalDays; $i++)
                                    <th class="text-center">{{ $i }}</th>
                                @endfor
                            @else
                                <!-- Tanggal akan diisi oleh JavaScript jika tidak ada filter -->
                            @endif
                            <th class="text-center">H</th>
                            <th class="text-center">S</th>
                            <th class="text-center">I</th>
                            <th class="text-center">A</th>
                        </tr>
                    </thead>
                    <tbody id="rekapTableBody">
                        @if(isset($rekapData) && count($rekapData) > 0)
                            @foreach($rekapData as $index => $guru)
                                <tr>
                                    <td class="text-center">{{ $index + 1 }}</td>
                                    <td>{{ $guru->nip ?? '-' }}</td>
                                    <td>{{ $guru->nama }}</td>
                                    @if(auth()->user()->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']))
                                    <td>{{ $guru->unit->nama_unit ?? '-' }}</td>
                                    @endif
                                    @if(isset($totalDays))
                                        @for($i = 1; $i <= $totalDays; $i++)
                                            <td class="text-center">
                                                @if(isset($guru->absensi[$i]))
                                                    @if($guru->absensi[$i] == 'hadir')
                                                        <span class="badge bg-success">H</span>
                                                    @elseif($guru->absensi[$i] == 'sakit')
                                                        <span class="badge bg-warning">S</span>
                                                    @elseif($guru->absensi[$i] == 'izin')
                                                        <span class="badge bg-info">I</span>
                                                    @elseif($guru->absensi[$i] == 'alpa')
                                                        <span class="badge bg-danger">A</span>
                                                    @endif
                                                @else
                                                    -
                                                @endif
                                            </td>
                                        @endfor
                                    @endif
                                    <td class="text-center">{{ $guru->hadir ?? 0 }}</td>
                                    <td class="text-center">{{ $guru->sakit ?? 0 }}</td>
                                    <td class="text-center">{{ $guru->izin ?? 0 }}</td>
                                    <td class="text-center">{{ $guru->alpa ?? 0 }}</td>
                                </tr>
                            @endforeach
                            <tr class="font-weight-bold bg-light">
                                <td colspan="{{ auth()->user()->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']) ? '4' : '3' }}" class="text-right">Total</td>
                                @if(isset($totalDays))
                                    @for($i = 1; $i <= $totalDays; $i++)
                                        <td class="text-center">-</td>
                                    @endfor
                                @endif
                                <td class="text-center">{{ $totalHadir ?? 0 }}</td>
                                <td class="text-center">{{ $totalSakit ?? 0 }}</td>
                                <td class="text-center">{{ $totalIzin ?? 0 }}</td>
                                <td class="text-center">{{ $totalAlpa ?? 0 }}</td>
                            </tr>
                        @else
                            <tr>
                                <td colspan="{{ ($totalDays ?? 31) + (auth()->user()->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']) ? 8 : 7) }}" class="text-center">
                                    Silakan pilih bulan dan tahun untuk melihat rekap absensi guru
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
            
            <div class="mt-4">
                <h5>Keterangan:</h5>
                <div class="row">
                    <div class="col-md-6">
                        <p>H = Hadir</p>
                        <p>S = Sakit</p>
                        <p>I = Izin</p>
                        <p>A = Alpa (Tanpa Keterangan)</p>
                    </div>
                    <div class="col-md-6">
                        @if(isset($totalHadir) && isset($totalSakit) && isset($totalIzin) && isset($totalAlpa))
                            @php
                                $totalKeseluruhan = $totalHadir + $totalSakit + $totalIzin + $totalAlpa;
                                $persenHadir = $totalKeseluruhan > 0 ? round(($totalHadir / $totalKeseluruhan) * 100, 2) : 0;
                                $persenSakit = $totalKeseluruhan > 0 ? round(($totalSakit / $totalKeseluruhan) * 100, 2) : 0;
                                $persenIzin = $totalKeseluruhan > 0 ? round(($totalIzin / $totalKeseluruhan) * 100, 2) : 0;
                                $persenAlpa = $totalKeseluruhan > 0 ? round(($totalAlpa / $totalKeseluruhan) * 100, 2) : 0;
                            @endphp
                            <p>Persentase Hadir: {{ $persenHadir }}% ({{ $totalHadir }})</p>
                            <p>Persentase Sakit: {{ $persenSakit }}% ({{ $totalSakit }})</p>
                            <p>Persentase Izin: {{ $persenIzin }}% ({{ $totalIzin }})</p>
                            <p>Persentase Alpa: {{ $persenAlpa }}% ({{ $totalAlpa }})</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('js')
<script>
$(function() {
    // Tampilkan rekap absensi
    $('#btnTampilkanRekap').click(function() {
        const unitId = $('#unit_id').val() || '';
        const bulan = $('#bulan').val();
        const tahun = $('#tahun').val();
        
        if (!bulan || !tahun) {
            alert('Silakan pilih bulan dan tahun terlebih dahulu');
            return;
        }
        
        // Set header bulan dan tahun
        const namaBulan = $('#bulan option:selected').text();
        $('#bulanTahunHeader').text(`${namaBulan} ${tahun}`);
        
        // Buat header tanggal
        const jumlahHari = new Date(tahun, bulan, 0).getDate();
        let headerTanggal = '';
        
        for (let i = 1; i <= jumlahHari; i++) {
            headerTanggal += `<th class="text-center">${i}</th>`;
        }
        
        // Tambahkan kolom jumlah
        headerTanggal += `
            <th class="text-center">H</th>
            <th class="text-center">S</th>
            <th class="text-center">I</th>
            <th class="text-center">A</th>
        `;
        
        // Update colspan pada header bulan dan tahun
        $('#bulanTahunHeader').attr('colspan', jumlahHari);
        
        // Update header tanggal
        $('#tanggalHeader').html(headerTanggal);
        
        // Load data rekap
        $.ajax({
            url: "{{ route('absensi.get-rekap-guru') }}",
            type: "GET",
            data: {
                unit_id: unitId,
                bulan: bulan,
                tahun: tahun
            },
            dataType: "json",
            beforeSend: function() {
                $('#rekapTableBody').html('<tr><td colspan="' + (jumlahHari + (unitId ? 8 : 7)) + '" class="text-center">Loading...</td></tr>');
                $('#rekapContainer').show();
            },
            success: function(response) {
                if (response.success) {
                    let html = '';
                    let totalHadir = 0;
                    let totalSakit = 0;
                    let totalIzin = 0;
                    let totalAlpa = 0;
                    
                    if (response.data.length > 0) {
                        $.each(response.data, function(index, guru) {
                            html += `<tr>
                                <td class="text-center">${index + 1}</td>
                                <td>${guru.nip || '-'}</td>
                                <td>${guru.nama}</td>`;
                                
                            if ('{{ auth()->user()->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']) }}' === '1') {
                                html += `<td>${guru.unit || '-'}</td>`;
                            }
                            
                            for (let i = 1; i <= jumlahHari; i++) {
                                let status = '-';
                                if (guru.absensi && guru.absensi[i]) {
                                    if (guru.absensi[i] === 'hadir') {
                                        status = '<span class="badge bg-success">H</span>';
                                    } else if (guru.absensi[i] === 'sakit') {
                                        status = '<span class="badge bg-warning">S</span>';
                                    } else if (guru.absensi[i] === 'izin') {
                                        status = '<span class="badge bg-info">I</span>';
                                    } else if (guru.absensi[i] === 'alpa') {
                                        status = '<span class="badge bg-danger">A</span>';
                                    }
                                }
                                html += `<td class="text-center">${status}</td>`;
                            }
                            
                            html += `
                                <td class="text-center">${guru.hadir || 0}</td>
                                <td class="text-center">${guru.sakit || 0}</td>
                                <td class="text-center">${guru.izin || 0}</td>
                                <td class="text-center">${guru.alpa || 0}</td>
                            </tr>`;
                            
                            totalHadir += parseInt(guru.hadir || 0);
                            totalSakit += parseInt(guru.sakit || 0);
                            totalIzin += parseInt(guru.izin || 0);
                            totalAlpa += parseInt(guru.alpa || 0);
                        });
                        
                        // Tambahkan baris total
                        const colSpan = '{{ auth()->user()->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']) }}' === '1' ? 4 : 3;
                        html += `<tr class="font-weight-bold bg-light">
                            <td colspan="${colSpan}" class="text-right">Total</td>`;
                            
                        for (let i = 1; i <= jumlahHari; i++) {
                            html += `<td class="text-center">-</td>`;
                        }
                        
                        html += `
                            <td class="text-center">${totalHadir}</td>
                            <td class="text-center">${totalSakit}</td>
                            <td class="text-center">${totalIzin}</td>
                            <td class="text-center">${totalAlpa}</td>
                        </tr>`;
                        
                        // Tambahkan persentase
                        const totalKeseluruhan = totalHadir + totalSakit + totalIzin + totalAlpa;
                        const persenHadir = totalKeseluruhan > 0 ? ((totalHadir / totalKeseluruhan) * 100).toFixed(2) : 0;
                        const persenSakit = totalKeseluruhan > 0 ? ((totalSakit / totalKeseluruhan) * 100).toFixed(2) : 0;
                        const persenIzin = totalKeseluruhan > 0 ? ((totalIzin / totalKeseluruhan) * 100).toFixed(2) : 0;
                        const persenAlpa = totalKeseluruhan > 0 ? ((totalAlpa / totalKeseluruhan) * 100).toFixed(2) : 0;
                        
                        $('.col-md-6:last-child').html(`
                            <p>Persentase Hadir: ${persenHadir}% (${totalHadir})</p>
                            <p>Persentase Sakit: ${persenSakit}% (${totalSakit})</p>
                            <p>Persentase Izin: ${persenIzin}% (${totalIzin})</p>
                            <p>Persentase Alpa: ${persenAlpa}% (${totalAlpa})</p>
                        `);
                    } else {
                        const colSpan = jumlahHari + ('{{ auth()->user()->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']) }}' === '1' ? 8 : 7);
                        html = `<tr><td colspan="${colSpan}" class="text-center">Tidak ada data absensi guru untuk periode yang dipilih</td></tr>`;
                    }
                    
                    $('#rekapTableBody').html(html);
                } else {
                    const colSpan = jumlahHari + ('{{ auth()->user()->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']) }}' === '1' ? 8 : 7);
                    $('#rekapTableBody').html(`<tr><td colspan="${colSpan}" class="text-center">Error: ${response.message}</td></tr>`);
                }
            },
            error: function(xhr, status, error) {
                const colSpan = jumlahHari + ('{{ auth()->user()->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']) }}' === '1' ? 8 : 7);
                $('#rekapTableBody').html(`<tr><td colspan="${colSpan}" class="text-center">Error: ${error}</td></tr>`);
            }
        });
    });
    
    // Export Excel
    $('#btnExportExcel').click(function() {
        const unitId = $('#unit_id').val() || '';
        const bulan = $('#bulan').val();
        const tahun = $('#tahun').val();
        
        if (!bulan || !tahun) {
            alert('Silakan pilih bulan dan tahun terlebih dahulu');
            return;
        }
        
        window.location.href = `{{ url('absensi/export-excel-guru') }}?unit_id=${unitId}&bulan=${bulan}&tahun=${