<?php

namespace App\Http\Controllers;

use App\Models\AdmGuru;
use Illuminate\Http\Request;
use App\Traits\FiltersByUserUnit;
use Illuminate\Support\Facades\Log;

class AdmGuruController extends Controller
{
    use FiltersByUserUnit;
    
    public function index()
    {
        $query = AdmGuru::with(['guru', 'rejector']);
        
        // Jika user adalah guru, hanya tampilkan ADM miliknya
        if (auth()->user()->hasRole('Guru')) {
            $query->where('user_id', auth()->user()->id);
        } else {
            // Terapkan filter unit menggunakan trait
            $query = $this->applyUnitFilter($query);
        }
        
        $admList = $query->orderBy('created_at', 'desc')->get();
        
        return view('adm.guru.index', compact('admList'));
    }

    public function store(Request $request)
    {
        try {
            $request->validate([
                'judul' => 'required|string',
                'keterangan' => 'nullable|string',
                'file' => 'required|mimes:pdf|max:10240' // 10MB max
            ]);

            $file = $request->file('file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('public/adm-guru', $filename);
            $path = str_replace('public/', '', $path);

            // Ambil user yang sedang login
            $user = auth()->user();
            
            // Tambahkan log untuk debugging unit_id
            Log::info('User unit_id untuk AdmGuru', [
                'user_id' => $user->id,
                'unit_id' => $user->unit_id,
                'has_unit' => !is_null($user->unit_id)
            ]);

            AdmGuru::create([
                'user_id' => auth()->id(),
                'unit_id' => $user->unit_id, // Tambahkan unit_id dari user yang login
                'judul' => $request->judul,
                'keterangan' => $request->keterangan,
                'file_path' => $path,
                'status' => 'pending'
            ]);

            return redirect()->route('adm.guru.index')
                ->with('success', 'ADM Guru berhasil diupload');
        } catch (\Exception $e) {
            Log::error('Error in AdmGuruController@store: ' . $e->getMessage());
            return redirect()->route('adm.guru.index')
                ->with('error', 'Terjadi kesalahan saat upload ADM');
        }
    }

    public function viewFile($filename)
    {
        try {
            $path = storage_path('app/public/adm-guru/' . $filename);
            
            if (!file_exists($path)) {
                abort(404, 'File tidak ditemukan');
            }

            return response()->file($path, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => 'inline; filename="' . $filename . '"'
            ]);
        } catch (\Exception $e) {
            Log::error('Error viewing file: ' . $e->getMessage());
            return response()->json(['error' => 'File tidak dapat diakses'], 404);
        }
    }

    public function approve(AdmGuru $adm)
    {
        try {
            if ($adm->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'ADM sudah diproses sebelumnya'
                ], 400);
            }

            $adm->update([
                'status' => 'approved',
                'approved_by' => auth()->id(),
                'approved_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'ADM berhasil disetujui'
            ]);
        } catch (\Exception $e) {
            Log::error('Error in AdmGuruController@approve: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyetujui ADM'
            ], 500);
        }
    }

    public function reject(Request $request, AdmGuru $adm)
    {
        try {
            $request->validate([
                'alasan_penolakan' => 'required|string|max:255'
            ]);

            if ($adm->status !== 'pending') {
                return redirect()->route('adm.guru.index')
                    ->with('error', 'ADM sudah diproses sebelumnya');
            }

            $adm->update([
                'status' => 'ditangguhkan',
                'rejected_by' => auth()->id(),
                'rejected_at' => now(),
                'alasan_penolakan' => $request->alasan_penolakan
            ]);

            return redirect()->route('adm.guru.index')
                ->with('success', 'ADM berhasil ditangguhkan');
        } catch (\Exception $e) {
            Log::error('Error in AdmGuruController@reject: ' . $e->getMessage());
            return redirect()->route('adm.guru.index')
                ->with('error', 'Terjadi kesalahan saat menolak ADM');
        }
    }
}



