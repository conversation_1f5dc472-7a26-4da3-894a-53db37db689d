-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 04, 2025 at 11:04 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `simaspeloporduri`
--

-- --------------------------------------------------------

--
-- Table structure for table `absensi_siswa`
--

CREATE TABLE `absensi_siswa` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `siswa_id` bigint(20) UNSIGNED NOT NULL,
  `kelas_id` bigint(20) UNSIGNED NOT NULL,
  `tanggal` date NOT NULL,
  `status` enum('hadir','sakit','izin','alpa') NOT NULL,
  `keterangan` text DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `absensi_siswa`
--

INSERT INTO `absensi_siswa` (`id`, `siswa_id`, `kelas_id`, `tanggal`, `status`, `keterangan`, `created_by`, `created_at`, `updated_at`) VALUES
(32, 228, 12, '2025-04-25', 'hadir', NULL, 8, NULL, '2025-04-25 15:24:48'),
(33, 229, 12, '2025-04-25', 'hadir', NULL, 8, NULL, '2025-04-25 15:24:48'),
(34, 230, 12, '2025-04-25', 'hadir', NULL, 8, NULL, '2025-04-25 15:24:48'),
(35, 231, 12, '2025-04-25', 'hadir', NULL, 8, NULL, '2025-04-25 15:24:48'),
(36, 232, 12, '2025-04-25', 'hadir', NULL, 8, NULL, '2025-04-25 15:24:48'),
(37, 228, 12, '2025-04-02', 'alpa', NULL, 8, NULL, '2025-04-25 16:14:22'),
(38, 229, 12, '2025-04-02', 'hadir', NULL, 8, NULL, '2025-04-25 16:14:22'),
(39, 230, 12, '2025-04-02', 'sakit', 'Sakit Perut', 8, NULL, '2025-04-25 16:14:22'),
(40, 231, 12, '2025-04-02', 'hadir', NULL, 8, NULL, '2025-04-25 16:14:22'),
(41, 232, 12, '2025-04-02', 'izin', 'Keluar Kota', 8, NULL, '2025-04-25 16:14:22'),
(42, 228, 12, '2025-03-07', 'hadir', NULL, 8, NULL, '2025-04-25 17:24:51'),
(43, 229, 12, '2025-03-07', 'hadir', NULL, 8, NULL, '2025-04-25 17:24:51'),
(44, 230, 12, '2025-03-07', 'hadir', NULL, 8, NULL, '2025-04-25 17:24:51'),
(45, 231, 12, '2025-03-07', 'hadir', NULL, 8, NULL, '2025-04-25 17:24:51'),
(46, 232, 12, '2025-03-07', 'hadir', NULL, 8, NULL, '2025-04-25 17:24:51'),
(47, 228, 12, '2025-03-19', 'izin', NULL, 8, NULL, '2025-04-25 17:25:10'),
(48, 229, 12, '2025-03-19', 'hadir', NULL, 8, NULL, '2025-04-25 17:25:10'),
(49, 230, 12, '2025-03-19', 'hadir', NULL, 8, NULL, '2025-04-25 17:25:10'),
(50, 231, 12, '2025-03-19', 'sakit', NULL, 8, NULL, '2025-04-25 17:25:10'),
(51, 232, 12, '2025-03-19', 'hadir', NULL, 8, NULL, '2025-04-25 17:25:10'),
(52, 228, 12, '2025-04-26', 'hadir', NULL, 8, NULL, '2025-04-25 17:40:15'),
(53, 229, 12, '2025-04-26', 'hadir', NULL, 8, NULL, '2025-04-25 17:40:15'),
(54, 230, 12, '2025-04-26', 'hadir', NULL, 8, NULL, '2025-04-25 17:40:15'),
(55, 231, 12, '2025-04-26', 'hadir', NULL, 8, NULL, '2025-04-25 17:40:15'),
(56, 232, 12, '2025-04-26', 'hadir', NULL, 8, NULL, '2025-04-25 17:40:15'),
(57, 228, 12, '2025-04-10', 'hadir', NULL, 8, NULL, '2025-04-25 17:59:26'),
(58, 229, 12, '2025-04-10', 'izin', NULL, 8, NULL, '2025-04-25 17:59:26'),
(59, 230, 12, '2025-04-10', 'hadir', NULL, 8, NULL, '2025-04-25 17:59:26'),
(60, 231, 12, '2025-04-10', 'hadir', NULL, 8, NULL, '2025-04-25 17:59:26'),
(61, 232, 12, '2025-04-10', 'hadir', NULL, 8, NULL, '2025-04-25 17:59:26'),
(62, 228, 12, '2025-05-30', 'hadir', NULL, 8, NULL, '2025-05-30 07:48:21'),
(63, 229, 12, '2025-05-30', 'hadir', NULL, 8, NULL, '2025-05-30 07:48:21'),
(64, 230, 12, '2025-05-30', 'hadir', NULL, 8, NULL, '2025-05-30 07:48:21'),
(65, 231, 12, '2025-05-30', 'hadir', NULL, 8, NULL, '2025-05-30 07:48:21'),
(66, 232, 12, '2025-05-30', 'hadir', NULL, 8, NULL, '2025-05-30 07:48:21');

-- --------------------------------------------------------

--
-- Table structure for table `achievements`
--

CREATE TABLE `achievements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `level` varchar(255) NOT NULL,
  `participant` varchar(255) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `achievements`
--

INSERT INTO `achievements` (`id`, `title`, `description`, `level`, `participant`, `image`, `unit_id`, `created_at`, `updated_at`) VALUES
(1, 'DHAMMAPADA SIPPA DHAMMA SAMAJJA IX', 'Juara 2 dalam lomba Dhammapada Sippa Dhamma Samajja IX Tingkat Kabupaten', 'Kabupaten', 'Siswa SD', '1741826765.jpg', 1, '2025-03-12 17:46:05', '2025-03-12 17:46:05'),
(2, 'FASHION SHOW DAN MEWARNAI BERSAMA GROLIER', 'Pemenang juara lomba fashion show dan mewarnai bersama grolier', 'Kecamatan', 'Siswa TK', '1741826864.jpeg', 3, '2025-03-12 17:47:44', '2025-03-12 17:47:44'),
(3, 'ONLIMPIADE ORBIT 2024', 'Juara 2 Onlimpiade Orbit 2024 PIALA HASRI AINUN HABIBIE', 'Nasional', 'CLARA SUNANTO', '1741827233.jpg', 2, '2025-03-12 17:53:53', '2025-03-12 17:53:53'),
(4, 'Juara 1', 'Lomba menyayi', 'Kecamatan', 'Siswa SMA', '1746421603.jpg', 4, '2025-05-05 05:06:43', '2025-05-05 05:06:43');

-- --------------------------------------------------------

--
-- Table structure for table `adm_guru`
--

CREATE TABLE `adm_guru` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `judul` varchar(255) NOT NULL,
  `keterangan` text DEFAULT NULL,
  `file_path` varchar(255) NOT NULL,
  `status` enum('pending','approved','ditangguhkan') DEFAULT 'pending',
  `approved_by` bigint(20) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `rejected_by` bigint(20) UNSIGNED DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL,
  `alasan_penolakan` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `adm_guru`
--

INSERT INTO `adm_guru` (`id`, `user_id`, `unit_id`, `judul`, `keterangan`, `file_path`, `status`, `approved_by`, `approved_at`, `rejected_by`, `rejected_at`, `alasan_penolakan`, `created_at`, `updated_at`) VALUES
(1, 3, 2, 'Feile Tes', NULL, 'adm-guru/NqqDq9Q2iHpkv8F1xpLIce8G5AgLIPPmUxpclKWy.pdf', 'approved', 1, '2025-03-07 04:42:28', NULL, NULL, NULL, '2025-03-07 04:41:49', '2025-04-02 01:42:42'),
(2, 3, 2, 'tes 1', NULL, 'adm-guru/w3JC2ST1lRHdGwxfxbyTIPCguaowaTyCLkjGYb56.pdf', 'ditangguhkan', NULL, NULL, 1, '2025-03-07 04:43:57', 'Kurang tepat', '2025-03-07 04:43:29', '2025-04-02 01:42:42'),
(3, 3, 2, 'Feile Tes2', NULL, 'adm-guru/XS6v5dsnC8VrSrJDPM9qEozUbrQ0TGRJOyJJqn3Y.pdf', 'ditangguhkan', NULL, NULL, 1, '2025-03-07 04:50:55', 'salah', '2025-03-07 04:50:37', '2025-04-02 01:42:42'),
(4, 3, 2, 'Feile Tes2', NULL, 'adm-guru/b8iK7XP0FfuSsDpAuAzVUMMlTCsAACRNI09D9ZLY.pdf', 'ditangguhkan', NULL, NULL, 3, '2025-03-07 13:17:16', 'salah kirim', '2025-03-07 12:53:27', '2025-04-02 01:42:42'),
(5, 3, 2, 'Feile Tes', NULL, 'adm-guru/3Yq9XO965I1q7ZYBdWN3dm9BohD6AXUOmCZi0fz7.pdf', 'pending', NULL, NULL, NULL, NULL, NULL, '2025-03-07 12:57:58', '2025-04-02 01:42:42'),
(6, 3, 2, 'Feile Tess', NULL, 'adm-guru/cTaeSGWllUBcoWu5RQc4mNpRlmACsPwwdcXyQPG1.pdf', 'pending', NULL, NULL, NULL, NULL, NULL, '2025-03-07 13:09:54', '2025-04-02 01:42:42'),
(7, 3, 2, 'teeees', 'tttttttttttttttttttttttttttssssssssssssssssssssssssss', 'adm-guru/1741378674_4_F-SARPRAS_TANAH & BANGUNAN_2020.pdf', 'pending', NULL, NULL, NULL, NULL, NULL, '2025-03-07 13:17:54', '2025-04-02 01:42:42'),
(8, 3, 2, 'sssss', 'saaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa', 'adm-guru/1741378732_4_F-SARPRAS_TANAH & BANGUNAN_2020.pdf', 'pending', NULL, NULL, NULL, NULL, NULL, '2025-03-07 13:18:52', '2025-04-02 01:42:42'),
(9, 8, 1, 'RPP Kelas 1', 'rpp kelas 1', 'adm-guru/1741380131_4_F-SARPRAS_TANAH & BANGUNAN_2020.pdf', 'ditangguhkan', NULL, NULL, 9, '2025-04-02 01:44:29', 'tes tolak', '2025-03-07 13:42:11', '2025-04-02 01:44:29'),
(10, 8, 1, 'tes 1ss', 'sdsaaa', 'adm-guru/1741389480_PENAWARAN WEB PELOPOR 2.pdf', 'approved', 9, '2025-04-02 01:44:16', NULL, NULL, NULL, '2025-03-07 16:18:00', '2025-04-02 01:44:16'),
(11, 8, 1, 'ADM guru SD 1', 'tes guru stelah filter', 'adm-guru/1743581489_Tan Malaka (Bapak Republik yang Dilupakan) by Seri Buku TEMPO  Bapak Bangsa.pdf', 'pending', NULL, NULL, NULL, NULL, NULL, '2025-04-02 01:11:29', '2025-04-02 01:42:42'),
(12, 8, 1, 'rpp', 'pertemuan1', 'adm-guru/1743672473_Bilang Begini Maksudnya Begitu.pdf', 'pending', NULL, NULL, NULL, NULL, NULL, '2025-04-03 02:27:53', '2025-04-03 02:27:53'),
(13, 8, 1, 'rpp', 'setealh perbaikan waktu', 'adm-guru/1743673348_Bilang Begini Maksudnya Begitu.pdf', 'pending', NULL, NULL, NULL, NULL, NULL, '2025-04-03 09:42:28', '2025-04-03 09:42:28');

-- --------------------------------------------------------

--
-- Table structure for table `adm_kepsek`
--

CREATE TABLE `adm_kepsek` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `judul` varchar(255) NOT NULL,
  `keterangan` text DEFAULT NULL,
  `file_path` varchar(255) NOT NULL,
  `status` enum('pending','approved','ditangguhkan') NOT NULL DEFAULT 'pending',
  `approved_by` bigint(20) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `rejected_by` bigint(20) UNSIGNED DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL,
  `alasan_penolakan` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `adm_kepsek`
--

INSERT INTO `adm_kepsek` (`id`, `user_id`, `unit_id`, `judul`, `keterangan`, `file_path`, `status`, `approved_by`, `approved_at`, `rejected_by`, `rejected_at`, `alasan_penolakan`, `created_at`, `updated_at`) VALUES
(1, 9, 1, 'tess', 'tess excel', 'adm_kepsek/1743231242_4_F-SARPRAS_TANAH & BANGUNAN_2020.pdf', 'approved', 11, '2025-03-29 01:23:21', NULL, NULL, NULL, '2025-03-28 23:54:03', '2025-04-02 01:30:19'),
(2, 9, 1, 'tttt', NULL, 'adm_kepsek/1743239120_4_F-SARPRAS_TANAH & BANGUNAN_2020.pdf', 'ditangguhkan', NULL, NULL, 11, '2025-03-29 02:13:27', 'tesss', '2025-03-29 02:05:21', '2025-04-02 01:30:19'),
(3, 9, 1, 'Feile Tes2', 's', 'adm_kepsek/1743638920_Tan Malaka (Bapak Republik yang Dilupakan) by Seri Buku TEMPO  Bapak Bangsa.pdf', 'approved', 11, '2025-04-02 18:04:43', NULL, NULL, NULL, '2025-04-02 17:08:40', '2025-04-02 18:04:43');

-- --------------------------------------------------------

--
-- Table structure for table `adm_ktsp`
--

CREATE TABLE `adm_ktsp` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `judul` varchar(255) NOT NULL,
  `keterangan` text DEFAULT NULL,
  `file_path` varchar(255) NOT NULL,
  `status` enum('pending','approved','ditangguhkan') NOT NULL DEFAULT 'pending',
  `approved_by` bigint(20) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `rejected_by` bigint(20) UNSIGNED DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL,
  `alasan_penolakan` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `adm_ktsp`
--

INSERT INTO `adm_ktsp` (`id`, `user_id`, `unit_id`, `judul`, `keterangan`, `file_path`, `status`, `approved_by`, `approved_at`, `rejected_by`, `rejected_at`, `alasan_penolakan`, `created_at`, `updated_at`) VALUES
(1, 2, 1, 'tes', 'tesss', 'adm_ktsp/1743375751_Permohonan Domain.docx', 'pending', NULL, NULL, NULL, NULL, NULL, '2025-03-30 16:02:32', '2025-04-03 11:56:23'),
(2, 2, 1, 'tes pdf', NULL, 'adm_ktsp/1743376359_Manajemen Keuangan by Suad Husnan.pdf', 'pending', NULL, NULL, NULL, NULL, NULL, '2025-03-30 16:12:39', '2025-04-03 11:56:23');

-- --------------------------------------------------------

--
-- Table structure for table `adm_waka`
--

CREATE TABLE `adm_waka` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `judul` varchar(255) NOT NULL,
  `keterangan` text DEFAULT NULL,
  `file_path` varchar(255) NOT NULL,
  `status` enum('pending','approved','ditangguhkan') NOT NULL DEFAULT 'pending',
  `approved_by` bigint(20) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `rejected_by` bigint(20) UNSIGNED DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL,
  `alasan_penolakan` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `adm_waka`
--

INSERT INTO `adm_waka` (`id`, `user_id`, `unit_id`, `judul`, `keterangan`, `file_path`, `status`, `approved_by`, `approved_at`, `rejected_by`, `rejected_at`, `alasan_penolakan`, `created_at`, `updated_at`) VALUES
(6, 2, 1, 'Tes ADM waka', 'adm waka tes', 'adm-waka/1743252114_Kulinologi Chocolate Time.pdf', 'ditangguhkan', NULL, NULL, 9, '2025-03-29 05:45:16', 'belum lengkap', '2025-03-29 05:41:54', '2025-04-02 00:56:53'),
(7, 2, 1, 'tes 2', 'adm tes 2', 'adm-waka/1743252252_Kuliner Soto Nusantara Kumpulan Resep by Susilaningsih, Martina Andriani, Bara Yudhistira.pdf', 'approved', 9, '2025-03-29 05:44:53', NULL, NULL, NULL, '2025-03-29 05:44:12', '2025-04-02 00:56:53'),
(8, 2, 1, 'tesss', 'tes3', 'adm-waka/1743252348_Koktail _ Minuman Pesta Sensasional.pdf', 'approved', 9, '2025-03-29 05:45:59', NULL, NULL, NULL, '2025-03-29 05:45:48', '2025-04-02 00:56:53'),
(9, 2, 1, 'Feile Tes', 'ssssssssssssssss ssssssssssssssssss sssssssssssssssss ssssssss ssss sssss ssssssss sss ssss ssssssss ssssssssssssssssss ssssssss sssssssssss sssss ssssssssss ssssssssssssssss ssssssssss ssssss sssssss sssss sssssss sssss sssss sssssss s', 'adm-waka/1743544181_Sejarah Indonesia Modern 1200-2004 by M.C. Ricklefs.pdf', 'pending', NULL, NULL, NULL, NULL, NULL, '2025-04-01 14:49:41', '2025-04-02 00:56:53'),
(10, 20, NULL, 'wka sma', 'adm waka sma', 'adm-waka/1743580704_Tan Malaka (Bapak Republik yang Dilupakan) by Seri Buku TEMPO  Bapak Bangsa.pdf', 'pending', NULL, NULL, NULL, NULL, NULL, '2025-04-02 00:58:24', '2025-04-02 00:58:24'),
(11, 2, NULL, 'waka sd 2 maret', 'waka sd tes setelah filter aktif', 'adm-waka/1743581038_Seni Perang Sun Tzu  36 Strategi by Tjio Tjiang Feng.pdf', 'pending', NULL, NULL, NULL, NULL, NULL, '2025-04-02 01:03:58', '2025-04-02 01:03:58'),
(12, 2, 1, 'ke 2waka sd 2 maret', 'ke 2 setelah filter', 'adm-waka/1743581306_A Man Called Ove (Indonesian Version) by Fredrik Backman.pdf', 'approved', 9, '2025-04-02 01:13:08', NULL, NULL, NULL, '2025-04-02 01:08:27', '2025-04-02 01:13:08');

-- --------------------------------------------------------

--
-- Table structure for table `anggota_ekstrakurikulers`
--

CREATE TABLE `anggota_ekstrakurikulers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `rombel_ekstrakurikuler_id` bigint(20) UNSIGNED NOT NULL,
  `siswa_id` bigint(20) UNSIGNED NOT NULL,
  `kelas_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `anggota_ekstrakurikulers`
--

INSERT INTO `anggota_ekstrakurikulers` (`id`, `rombel_ekstrakurikuler_id`, `siswa_id`, `kelas_id`, `created_at`, `updated_at`) VALUES
(1, 1, 104, 23, '2025-04-28 07:11:23', '2025-04-28 07:11:23'),
(2, 1, 233, 10, '2025-04-28 07:11:23', '2025-04-28 07:11:23'),
(3, 1, 224, 1, '2025-04-28 07:11:23', '2025-04-28 07:11:23'),
(4, 2, 228, 20, '2025-05-03 11:16:19', '2025-05-03 11:16:19'),
(5, 2, 233, 10, '2025-05-03 11:16:19', '2025-05-03 11:16:19'),
(6, 2, 229, 20, '2025-05-03 11:16:19', '2025-05-03 11:16:19'),
(7, 2, 230, 20, '2025-05-03 11:16:19', '2025-05-03 11:16:19'),
(8, 2, 237, 10, '2025-05-03 11:16:19', '2025-05-03 11:16:19');

-- --------------------------------------------------------

--
-- Table structure for table `articles`
--

CREATE TABLE `articles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `excerpt` text DEFAULT NULL,
  `content` longtext NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `status` enum('draft','published','archived') NOT NULL DEFAULT 'draft',
  `author_id` bigint(20) UNSIGNED NOT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `published_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `articles`
--

INSERT INTO `articles` (`id`, `title`, `slug`, `excerpt`, `content`, `image`, `status`, `author_id`, `unit_id`, `published_at`, `created_at`, `updated_at`) VALUES
(1, 'Selamat Datang di Website Baru Kami', 'selamat-datang', 'Kami dengan bangga meluncurkan website baru sekolah.', 'Konten lengkap artikel...', NULL, 'published', 1, NULL, '2025-03-08 05:41:15', '2025-03-08 05:41:15', '2025-03-08 05:41:15'),
(4, 'PERAYAAN KATHINA 2568 TB-2024 SEKOLAH PELOPOR', 'perayaan-kathina-2568-tb-2024-sekolah-pelopor', 'Kathina (bahasa Pali) merupakan salah satu hari raya Buddhisme yang biasanya dirayakan pada akhir masa Vassa (biasanya jatuh pada bulan Oktober). Hari raya Ka?hina ini merupakan penanda bahwa masa Vassa telah berakhir dan umat Buddha memasuki masa Ka?hina. Perayaan Ka?hina merupakan waktu bagi para umat awam untuk berderma atau berdana kepada para biksu sebagai tanda rasa syukur mereka', '<p style=\"text-align: justify;\">Kathina (<a href=\"https://id.wikipedia.org/wiki/Bahasa_Pali\">bahasa Pali</a>) merupakan salah satu hari raya <a href=\"https://id.wikipedia.org/wiki/Buddhisme\">Buddhisme</a> yang biasanya dirayakan pada akhir masa <a href=\"https://id.wikipedia.org/wiki/Vassa\"><em>Vassa</em></a> (biasanya jatuh pada bulan Oktober). Hari raya Ka?hina ini merupakan penanda bahwa masa <a href=\"https://id.wikipedia.org/wiki/Vassa\"><em>Vassa</em></a> telah berakhir dan umat Buddha memasuki masa Ka?hina. Perayaan Ka?hina merupakan waktu bagi para umat awam untuk berderma atau berdana kepada para <a href=\"https://id.wikipedia.org/wiki/Biksu\">biksu</a> sebagai tanda rasa syukur mereka <em>(</em><a href=\"https://id.wikipedia.org/wiki/Kathina\"><em>https://id.wikipedia.org/wiki/Kathina</em></a><em>)</em>.</p>\r\n<p>&nbsp;</p>\r\n<p style=\"text-align: justify;\">Hari raya Kathina di Sekolah Pelopor merupakan agenda rutin yang dilaksanakan setiap tahun. Hal ini bertujuan untuk menanamkan nilai religius kepada para murid di Sekolah Pelopor terutama dalam menerapkan praktik berdana kepada Sangha. Dalam perayaan ini, para murid dan guru serta tenaga kependidikan Sekolah Pelopor dari tingkat Taman Kanak-kanak hingga tingkat SMP mengikuti kegiatan tersebut dengan penuh antusias.</p>\r\n<p>&nbsp;</p>\r\n<p style=\"text-align: justify;\">Perayaan Kathina Sekolah Pelopor dilaksanakan pada hari Jumat, 18 Oktober 2024 sekitar pukul 08.00 sampai 09.30 WIB di Gedung Dhammasekha. Hadir dalam acara tersebut 5 orang anggota Sangha yang terdiri dari 2 Bhikkhu, 1 Bhikkhuni, 1 samanera dan 1 samaneri. Para murid dan guru serta tenaga kependidikan dari seluruh unit Sekolah Pelopor (TK, SD, dan SMPS Pelopor Mandau) sekitar 700 orang menghadiri acara Kathina dan melaksanakan dana kepada Sangha. Rangkaian acara Hari Raya Kathina dipandu oleh Ibu Siti Mudrikah, S.Pd, diawali dengan pelafalan Kitab Suci Dhammapada oleh murid SDS Pelopor Duri, puja bhakti dipimpin oleh Bapak Sugiyono, S.Pd, petugas Catupaccaya dari murid SMPS Pelopor Mandau. Saat persembahan dana Kathina diiringi oleh paduan suara SMPS Pelopor Mandau. Seluruh panitia, guru, dan murid saling berkolaborasi untuk mensukseskan acara Kathina Sekolah Pelopor.&nbsp;&nbsp;</p>\r\n<p>&nbsp;</p>\r\n<p style=\"text-align: justify;\">Setelah acara selesai, murid TK Pelopor menunggu jemputan di Dhammasekha sedangkan murid-murid SD dan SMP diarahkan kembali ke sekolah untuk dijemput di sekolah. Penulis berharap dengan adanya acara perayaan Kathina ini dapat meningkatkan pemahaman dan keyakinan terhadap ajaran Buddha terutama bagi murid yang beragama Buddha, semoga dharma tetap lestari dan membawa kebahagiaan bagi semua makhluk. Di tulis oleh: Parwati, S.Ag</p>', 'articles/QGo6FZETy0gJYyJeiNvm8G0Ljd7VthnU1vm4FLLQ.jpg', 'published', 1, NULL, '2025-03-09 06:38:50', '2025-03-09 06:38:50', '2025-03-12 15:31:36'),
(5, 'tess', 'tess', 'asffs', '<p>asdasfasfa</p>', 'articles/PhRSCbQHiNWCCIlf8EC6DJFmD8AjRioM8DmfUr0k.jpg', 'published', 1, NULL, '2025-03-10 23:35:24', '2025-03-10 23:35:24', '2025-03-10 23:35:24'),
(6, 'smp', 'smp', 'smpppppppp', '<p>smpppp ok</p>', 'articles/XDIgiWpvhonrrDIrqcVcCy02pynhd81w6LazOm8k.jpg', 'published', 1, NULL, '2025-03-11 00:07:10', '2025-03-11 00:07:10', '2025-03-11 00:07:10'),
(7, 'dari kepsek', 'dari-kepsek', 'artikel ioni du pubblikasikan lewat akun kepsek', '<p>isi artikel ioni du pubblikasikan lewat akun kepsek&nbsp;</p>', 'articles/ADda08wSM99RCibB2ewmnRnI80q6cyoYkitprras.jpg', 'published', 9, NULL, '2025-03-11 00:13:30', '2025-03-11 00:13:30', '2025-03-11 00:22:52'),
(8, 'wwwwww', 'wwwwww', 'spppppp', '<p>smpppppp</p>', 'articles/qoisQtcys4hNUBp48ky6gtAcwFrt1sLwrCya7bfN.jpg', 'published', 1, 2, '2025-03-11 00:24:15', '2025-03-11 00:24:15', '2025-03-11 00:24:15'),
(9, 'asas', 'asas', 'asdsa', '<p>asdasd</p>', 'articles/PnZBhyVDJvXf7KL4rW1jnKJy243VjZo8LxiyEYHq.jpg', 'published', 1, 1, '2025-03-12 01:26:03', '2025-03-12 01:26:03', '2025-03-12 01:26:03');

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `cache`
--

INSERT INTO `cache` (`key`, `value`, `expiration`) VALUES
('simaspelopor_cache_spatie.permission.cache', 'a:3:{s:5:\"alias\";a:4:{s:1:\"a\";s:2:\"id\";s:1:\"b\";s:4:\"name\";s:1:\"c\";s:10:\"guard_name\";s:1:\"r\";s:5:\"roles\";}s:11:\"permissions\";a:117:{i:0;a:4:{s:1:\"a\";i:1;s:1:\"b\";s:8:\"view-gtk\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:5;i:3;i:8;}}i:1;a:4:{s:1:\"a\";i:2;s:1:\"b\";s:10:\"manage-gtk\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:2;a:4:{s:1:\"a\";i:3;s:1:\"b\";s:18:\"view-peserta-didik\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;i:4;i:8;}}i:3;a:4:{s:1:\"a\";i:4;s:1:\"b\";s:20:\"manage-peserta-didik\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;i:4;i:6;}}i:4;a:4:{s:1:\"a\";i:5;s:1:\"b\";s:8:\"view-adm\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:7:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:5;i:5;i:6;i:6;i:8;}}i:5;a:4:{s:1:\"a\";i:6;s:1:\"b\";s:10:\"manage-adm\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:3;i:2;i:5;}}i:6;a:4:{s:1:\"a\";i:7;s:1:\"b\";s:15:\"manage-adm-waka\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;i:4;i:6;}}i:7;a:4:{s:1:\"a\";i:8;s:1:\"b\";s:15:\"manage-adm-guru\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:5;}}i:8;a:4:{s:1:\"a\";i:9;s:1:\"b\";s:11:\"view-rombel\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:8:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:5;i:5;i:6;i:6;i:8;i:7;i:10;}}i:9;a:4:{s:1:\"a\";i:10;s:1:\"b\";s:13:\"manage-rombel\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;i:4;i:6;}}i:10;a:4:{s:1:\"a\";i:11;s:1:\"b\";s:12:\"view-sarpras\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:7:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:5;i:4;i:7;i:5;i:8;i:6;i:10;}}i:11;a:4:{s:1:\"a\";i:12;s:1:\"b\";s:14:\"manage-sarpras\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:5;i:3;i:7;}}i:12;a:4:{s:1:\"a\";i:13;s:1:\"b\";s:12:\"view-website\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:7:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;i:4;i:6;i:5;i:7;i:6;i:10;}}i:13;a:4:{s:1:\"a\";i:14;s:1:\"b\";s:14:\"manage-website\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:14;a:4:{s:1:\"a\";i:15;s:1:\"b\";s:23:\"manage-website-prestasi\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:4;i:2;i:5;i:3;i:6;}}i:15;a:4:{s:1:\"a\";i:16;s:1:\"b\";s:20:\"manage-website-event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:4;i:2;i:5;i:3;i:6;i:4;i:10;}}i:16;a:4:{s:1:\"a\";i:17;s:1:\"b\";s:30:\"manage-website-ekstrakurikuler\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:4;i:2;i:5;i:3;i:6;}}i:17;a:4:{s:1:\"a\";i:18;s:1:\"b\";s:24:\"manage-website-fasilitas\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:4;i:2;i:5;i:3;i:7;}}i:18;a:4:{s:1:\"a\";i:19;s:1:\"b\";s:14:\"view-elearning\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:19;a:4:{s:1:\"a\";i:20;s:1:\"b\";s:16:\"manage-elearning\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:20;a:4:{s:1:\"a\";i:21;s:1:\"b\";s:11:\"view-jadwal\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:3;i:2;i:5;i:3;i:6;i:4;i:8;}}i:21;a:4:{s:1:\"a\";i:22;s:1:\"b\";s:13:\"manage-jadwal\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:22;a:4:{s:1:\"a\";i:23;s:1:\"b\";s:19:\"view-jadwal-pribadi\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:23;a:4:{s:1:\"a\";i:24;s:1:\"b\";s:7:\"view-bk\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:6:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;i:4;i:6;i:5;i:8;}}i:24;a:4:{s:1:\"a\";i:25;s:1:\"b\";s:9:\"manage-bk\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:5;i:2;i:9;}}i:25;a:4:{s:1:\"a\";i:26;s:1:\"b\";s:10:\"view-nilai\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;i:4;i:8;}}i:26;a:4:{s:1:\"a\";i:27;s:1:\"b\";s:12:\"manage-nilai\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:27;a:4:{s:1:\"a\";i:28;s:1:\"b\";s:12:\"view-absensi\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:9:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:5;i:5;i:6;i:6;i:8;i:7;i:10;i:8;i:12;}}i:28;a:4:{s:1:\"a\";i:29;s:1:\"b\";s:14:\"manage-absensi\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:5;i:4;i:10;}}i:29;a:4:{s:1:\"a\";i:30;s:1:\"b\";s:8:\"view-spp\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:30;a:4:{s:1:\"a\";i:31;s:1:\"b\";s:10:\"manage-spp\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:5;i:2;i:10;}}i:31;a:4:{s:1:\"a\";i:32;s:1:\"b\";s:13:\"view-adm-guru\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:3;}}i:32;a:4:{s:1:\"a\";i:33;s:1:\"b\";s:15:\"upload-adm-guru\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:33;a:4:{s:1:\"a\";i:34;s:1:\"b\";s:13:\"view-adm-waka\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;i:4;i:6;}}i:34;a:4:{s:1:\"a\";i:35;s:1:\"b\";s:15:\"upload-adm-waka\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;i:4;i:6;}}i:35;a:4:{s:1:\"a\";i:36;s:1:\"b\";s:24:\"view-peserta-didik-aktif\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:36;a:4:{s:1:\"a\";i:37;s:1:\"b\";s:20:\"view-jadwal-mengajar\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:5;}}i:37;a:4:{s:1:\"a\";i:38;s:1:\"b\";s:11:\"view-jurnal\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:9:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:5;i:5;i:8;i:6;i:9;i:7;i:10;i:8;i:11;}}i:38;a:4:{s:1:\"a\";i:39;s:1:\"b\";s:13:\"manage-jurnal\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:9:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:5;i:5;i:8;i:6;i:9;i:7;i:10;i:8;i:11;}}i:39;a:4:{s:1:\"a\";i:40;s:1:\"b\";s:14:\"approve-jurnal\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;}}i:40;a:4:{s:1:\"a\";i:41;s:1:\"b\";s:15:\"view-adm-kepsek\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:3;i:2;i:8;}}i:41;a:4:{s:1:\"a\";i:42;s:1:\"b\";s:17:\"upload-adm-kepsek\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:3;}}i:42;a:4:{s:1:\"a\";i:43;s:1:\"b\";s:17:\"manage-adm-kepsek\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:3;i:2;i:5;}}i:43;a:4:{s:1:\"a\";i:44;s:1:\"b\";s:18:\"approve-adm-kepsek\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:5;i:2;i:8;}}i:44;a:4:{s:1:\"a\";i:45;s:1:\"b\";s:17:\"reject-adm-kepsek\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:45;a:4:{s:1:\"a\";i:46;s:1:\"b\";s:13:\"view-adm-ktsp\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:3;}}i:46;a:4:{s:1:\"a\";i:47;s:1:\"b\";s:15:\"upload-adm-ktsp\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:47;a:4:{s:1:\"a\";i:48;s:1:\"b\";s:15:\"manage-adm-ktsp\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:3;}}i:48;a:4:{s:1:\"a\";i:49;s:1:\"b\";s:15:\"view-pengaturan\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:5;i:4;i:10;}}i:49;a:4:{s:1:\"a\";i:50;s:1:\"b\";s:17:\"manage-pengaturan\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:50;a:4:{s:1:\"a\";i:51;s:1:\"b\";s:12:\"manage-users\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:51;a:4:{s:1:\"a\";i:52;s:1:\"b\";s:18:\"manage-permissions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:52;a:4:{s:1:\"a\";i:53;s:1:\"b\";s:16:\"view-tahunajaran\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:53;a:4:{s:1:\"a\";i:54;s:1:\"b\";s:18:\"manage-tahunajaran\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:54;a:4:{s:1:\"a\";i:55;s:1:\"b\";s:10:\"view-users\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:55;a:4:{s:1:\"a\";i:56;s:1:\"b\";s:12:\"create-users\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:56;a:4:{s:1:\"a\";i:57;s:1:\"b\";s:10:\"edit-users\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:57;a:4:{s:1:\"a\";i:58;s:1:\"b\";s:12:\"delete-users\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:58;a:4:{s:1:\"a\";i:59;s:1:\"b\";s:19:\"reset-user-password\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:59;a:4:{s:1:\"a\";i:60;s:1:\"b\";s:10:\"view-roles\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:60;a:4:{s:1:\"a\";i:61;s:1:\"b\";s:12:\"create-roles\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:61;a:4:{s:1:\"a\";i:62;s:1:\"b\";s:10:\"edit-roles\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:62;a:4:{s:1:\"a\";i:63;s:1:\"b\";s:12:\"delete-roles\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:63;a:4:{s:1:\"a\";i:64;s:1:\"b\";s:16:\"view-permissions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:64;a:4:{s:1:\"a\";i:65;s:1:\"b\";s:18:\"create-permissions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:65;a:4:{s:1:\"a\";i:66;s:1:\"b\";s:16:\"edit-permissions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:66;a:4:{s:1:\"a\";i:67;s:1:\"b\";s:18:\"delete-permissions\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:5;}}i:67;a:4:{s:1:\"a\";i:68;s:1:\"b\";s:20:\"manage-laporan-kerja\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:3;}}i:68;a:4:{s:1:\"a\";i:69;s:1:\"b\";s:18:\"view-laporan-kerja\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:3;}}i:69;a:4:{s:1:\"a\";i:70;s:1:\"b\";s:19:\"lihat-laporan-kerja\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:3;i:2;i:4;}}i:70;a:4:{s:1:\"a\";i:71;s:1:\"b\";s:20:\"kelola-laporan-kerja\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:71;a:4:{s:1:\"a\";i:72;s:1:\"b\";s:14:\"create-sarpras\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:7;}}i:72;a:4:{s:1:\"a\";i:73;s:1:\"b\";s:12:\"edit-sarpras\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:3;i:2;i:7;}}i:73;a:4:{s:1:\"a\";i:74;s:1:\"b\";s:14:\"delete-sarpras\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:7;}}i:74;a:4:{s:1:\"a\";i:75;s:1:\"b\";s:23:\"lihat-laporan-kerusakan\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:7;}}i:75;a:4:{s:1:\"a\";i:76;s:1:\"b\";s:22:\"buat-laporan-kerusakan\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:2;}}i:76;a:4:{s:1:\"a\";i:77;s:1:\"b\";s:24:\"kelola-laporan-kerusakan\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:3;i:2;i:7;}}i:77;a:4:{s:1:\"a\";i:78;s:1:\"b\";s:13:\"create-rombel\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:4;i:2;i:10;}}i:78;a:4:{s:1:\"a\";i:79;s:1:\"b\";s:11:\"edit-rombel\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:4;i:2;i:10;}}i:79;a:4:{s:1:\"a\";i:80;s:1:\"b\";s:13:\"delete-rombel\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:80;a:4:{s:1:\"a\";i:81;s:1:\"b\";s:19:\"assign-siswa-rombel\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:6;i:4;i:10;}}i:81;a:4:{s:1:\"a\";i:82;s:1:\"b\";s:19:\"remove-siswa-rombel\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:6;i:4;i:10;}}i:82;a:4:{s:1:\"a\";i:83;s:1:\"b\";s:17:\"view-daftar-kelas\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:6:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:4;i:4;i:6;i:5;i:10;}}i:83;a:4:{s:1:\"a\";i:84;s:1:\"b\";s:14:\"create-absensi\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:10;i:3;i:12;}}i:84;a:4:{s:1:\"a\";i:85;s:1:\"b\";s:12:\"edit-absensi\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:2;i:2;i:10;i:3;i:12;}}i:85;a:4:{s:1:\"a\";i:86;s:1:\"b\";s:14:\"delete-absensi\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:86;a:4:{s:1:\"a\";i:87;s:1:\"b\";s:18:\"view-rekap-absensi\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:6:{i:0;i:1;i:1;i:2;i:2;i:4;i:3;i:6;i:4;i:10;i:5;i:12;}}i:87;a:4:{s:1:\"a\";i:88;s:1:\"b\";s:16:\"view-rekap-admin\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:6;i:4;i:10;}}i:88;a:4:{s:1:\"a\";i:89;s:1:\"b\";s:9:\"view-unit\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:10;}}i:89;a:4:{s:1:\"a\";i:90;s:1:\"b\";s:11:\"manage-unit\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:90;a:4:{s:1:\"a\";i:91;s:1:\"b\";s:12:\"view-jenjang\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:10;}}i:91;a:4:{s:1:\"a\";i:92;s:1:\"b\";s:14:\"manage-jenjang\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:92;a:4:{s:1:\"a\";i:93;s:1:\"b\";s:11:\"view-gedung\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:10;}}i:93;a:4:{s:1:\"a\";i:94;s:1:\"b\";s:13:\"manage-gedung\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:10;}}i:94;a:4:{s:1:\"a\";i:95;s:1:\"b\";s:10:\"view-kelas\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:10;}}i:95;a:4:{s:1:\"a\";i:96;s:1:\"b\";s:12:\"manage-kelas\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:4;i:2;i:10;}}i:96;a:4:{s:1:\"a\";i:97;s:1:\"b\";s:10:\"view-mapel\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:10;}}i:97;a:4:{s:1:\"a\";i:98;s:1:\"b\";s:12:\"manage-mapel\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:4;}}i:98;a:4:{s:1:\"a\";i:99;s:1:\"b\";s:20:\"view-website-artikel\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:10;}}i:99;a:4:{s:1:\"a\";i:100;s:1:\"b\";s:22:\"manage-website-artikel\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:4;i:2;i:10;}}i:100;a:4:{s:1:\"a\";i:101;s:1:\"b\";s:18:\"view-website-event\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:10;}}i:101;a:4:{s:1:\"a\";i:102;s:1:\"b\";s:21:\"view-website-prestasi\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:6;}}i:102;a:4:{s:1:\"a\";i:103;s:1:\"b\";s:22:\"view-website-fasilitas\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:7;}}i:103;a:4:{s:1:\"a\";i:104;s:1:\"b\";s:28:\"view-website-ekstrakurikuler\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:4:{i:0;i:1;i:1;i:3;i:2;i:4;i:3;i:6;}}i:104;a:4:{s:1:\"a\";i:105;s:1:\"b\";s:18:\"view-website-slide\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:3;i:2;i:4;}}i:105;a:4:{s:1:\"a\";i:106;s:1:\"b\";s:20:\"manage-website-slide\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:4;}}i:106;a:4:{s:1:\"a\";i:107;s:1:\"b\";s:20:\"view-website-halaman\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:3;i:2;i:4;}}i:107;a:4:{s:1:\"a\";i:108;s:1:\"b\";s:22:\"manage-website-halaman\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:4;}}i:108;a:4:{s:1:\"a\";i:109;s:1:\"b\";s:22:\"view-laporan-kerusakan\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:5:{i:0;i:1;i:1;i:2;i:2;i:3;i:3;i:7;i:4;i:10;}}i:109;a:4:{s:1:\"a\";i:110;s:1:\"b\";s:24:\"create-laporan-kerusakan\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:2;i:2;i:10;}}i:110;a:4:{s:1:\"a\";i:111;s:1:\"b\";s:22:\"edit-laporan-kerusakan\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:7;}}i:111;a:4:{s:1:\"a\";i:112;s:1:\"b\";s:24:\"delete-laporan-kerusakan\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:112;a:4:{s:1:\"a\";i:113;s:1:\"b\";s:29:\"view-laporan-kerusakan-detail\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:1;}}i:113;a:4:{s:1:\"a\";i:114;s:1:\"b\";s:24:\"manage-laporan-kerusakan\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:7;}}i:114;a:4:{s:1:\"a\";i:115;s:1:\"b\";s:31:\"view-laporan-kerusakan-approval\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:2:{i:0;i:1;i:1;i:7;}}i:115;a:4:{s:1:\"a\";i:116;s:1:\"b\";s:25:\"approve-laporan-kerusakan\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:3:{i:0;i:1;i:1;i:3;i:2;i:7;}}i:116;a:4:{s:1:\"a\";i:117;s:1:\"b\";s:13:\"view-mengajar\";s:1:\"c\";s:3:\"web\";s:1:\"r\";a:1:{i:0;i:2;}}}s:5:\"roles\";a:12:{i:0;a:3:{s:1:\"a\";i:1;s:1:\"b\";s:13:\"Administrator\";s:1:\"c\";s:3:\"web\";}i:1;a:3:{s:1:\"a\";i:3;s:1:\"b\";s:14:\"Kepala Sekolah\";s:1:\"c\";s:3:\"web\";}i:2;a:3:{s:1:\"a\";i:5;s:1:\"b\";s:7:\"Yayasan\";s:1:\"c\";s:3:\"web\";}i:3;a:3:{s:1:\"a\";i:8;s:1:\"b\";s:8:\"Pengawas\";s:1:\"c\";s:3:\"web\";}i:4;a:3:{s:1:\"a\";i:4;s:1:\"b\";s:14:\"Waka Kurikulum\";s:1:\"c\";s:3:\"web\";}i:5;a:3:{s:1:\"a\";i:6;s:1:\"b\";s:14:\"Waka Kesiswaan\";s:1:\"c\";s:3:\"web\";}i:6;a:3:{s:1:\"a\";i:2;s:1:\"b\";s:4:\"Guru\";s:1:\"c\";s:3:\"web\";}i:7;a:3:{s:1:\"a\";i:10;s:1:\"b\";s:10:\"Tata Usaha\";s:1:\"c\";s:3:\"web\";}i:8;a:3:{s:1:\"a\";i:7;s:1:\"b\";s:12:\"Waka Sarpras\";s:1:\"c\";s:3:\"web\";}i:9;a:3:{s:1:\"a\";i:9;s:1:\"b\";s:19:\"Bimbingan Konseling\";s:1:\"c\";s:3:\"web\";}i:10;a:3:{s:1:\"a\";i:12;s:1:\"b\";s:10:\"Wali Kelas\";s:1:\"c\";s:3:\"web\";}i:11;a:3:{s:1:\"a\";i:11;s:1:\"b\";s:10:\"Pustakawan\";s:1:\"c\";s:3:\"web\";}}}', 1749096438);

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `detail_jadwal`
--

CREATE TABLE `detail_jadwal` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `jadwal_pelajaran_id` bigint(20) UNSIGNED NOT NULL,
  `mata_pelajaran_id` bigint(20) UNSIGNED DEFAULT NULL,
  `event_type` varchar(255) DEFAULT NULL,
  `hari` varchar(255) NOT NULL,
  `waktu_mulai` time NOT NULL,
  `waktu_selesai` time NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_istirahat` tinyint(1) NOT NULL DEFAULT 0,
  `keterangan` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `detail_jadwal`
--

INSERT INTO `detail_jadwal` (`id`, `jadwal_pelajaran_id`, `mata_pelajaran_id`, `event_type`, `hari`, `waktu_mulai`, `waktu_selesai`, `created_at`, `updated_at`, `is_istirahat`, `keterangan`) VALUES
(57, 12, 5, NULL, 'Senin', '12:55:00', '13:25:00', '2025-04-09 07:25:15', '2025-04-09 07:25:15', 0, NULL),
(58, 12, 5, NULL, 'Selasa', '12:55:00', '13:25:00', '2025-04-09 07:25:15', '2025-04-09 07:25:15', 0, NULL),
(59, 12, 5, NULL, 'Rabu', '12:55:00', '13:25:00', '2025-04-09 07:25:15', '2025-04-09 07:25:15', 0, NULL),
(60, 12, 5, NULL, 'Kamis', '12:55:00', '13:25:00', '2025-04-09 07:25:15', '2025-04-09 07:25:15', 0, NULL),
(61, 12, 5, NULL, 'Jumat', '12:55:00', '13:25:00', '2025-04-09 07:25:15', '2025-04-09 07:25:15', 0, NULL),
(62, 14, 5, NULL, 'Senin', '08:05:00', '08:40:00', '2025-04-18 16:10:11', '2025-04-18 16:10:11', 0, NULL),
(63, 14, 5, NULL, 'Selasa', '08:05:00', '08:40:00', '2025-04-18 16:10:11', '2025-04-18 16:10:11', 0, NULL),
(64, 14, 5, NULL, 'Rabu', '08:05:00', '08:40:00', '2025-04-18 16:10:11', '2025-04-18 16:10:11', 0, NULL),
(65, 14, 5, NULL, 'Kamis', '08:05:00', '08:40:00', '2025-04-18 16:10:11', '2025-04-18 16:10:11', 0, NULL),
(66, 14, 5, NULL, 'Jumat', '08:05:00', '08:40:00', '2025-04-18 16:10:11', '2025-04-18 16:10:11', 0, NULL),
(67, 15, 8, NULL, 'Senin', '07:15:00', '07:30:00', '2025-04-25 18:35:58', '2025-04-25 18:35:58', 0, NULL),
(68, 15, 8, NULL, 'Selasa', '07:15:00', '07:30:00', '2025-04-25 18:35:58', '2025-04-25 18:35:58', 0, NULL),
(69, 15, 8, NULL, 'Rabu', '07:15:00', '07:30:00', '2025-04-25 18:35:58', '2025-04-25 18:35:58', 0, NULL),
(70, 15, 8, NULL, 'Kamis', '07:15:00', '07:30:00', '2025-04-25 18:35:58', '2025-04-25 18:35:58', 0, NULL),
(71, 15, 8, NULL, 'Jumat', '07:15:00', '07:30:00', '2025-04-25 18:35:58', '2025-04-25 18:35:58', 0, NULL),
(72, 16, 7, NULL, 'Senin', '07:15:00', '07:30:00', '2025-04-25 19:07:17', '2025-04-25 19:07:17', 0, NULL),
(73, 16, 5, NULL, 'Selasa', '07:15:00', '07:30:00', '2025-04-25 19:07:17', '2025-04-25 19:07:17', 0, NULL),
(74, 16, 7, NULL, 'Rabu', '07:15:00', '07:30:00', '2025-04-25 19:07:17', '2025-04-25 19:07:17', 0, NULL),
(75, 16, 9, NULL, 'Kamis', '07:15:00', '07:30:00', '2025-04-25 19:07:17', '2025-04-25 19:07:17', 0, NULL),
(76, 16, 7, NULL, 'Jumat', '07:15:00', '07:30:00', '2025-04-25 19:07:17', '2025-04-25 19:07:17', 0, NULL),
(77, 17, 5, NULL, 'Senin', '09:30:00', '09:45:00', '2025-05-20 22:16:51', '2025-05-20 22:16:51', 0, NULL),
(78, 17, 5, NULL, 'Selasa', '09:00:00', '09:30:00', '2025-05-20 22:16:51', '2025-05-20 22:16:51', 0, NULL),
(79, 17, 5, NULL, 'Rabu', '09:45:00', '10:15:00', '2025-05-20 22:16:51', '2025-05-20 22:16:51', 0, NULL),
(80, 17, 5, NULL, 'Kamis', '07:30:00', '08:00:00', '2025-05-20 22:16:51', '2025-05-20 22:16:51', 0, NULL),
(81, 17, 5, NULL, 'Jumat', '07:30:00', '08:00:00', '2025-05-20 22:16:51', '2025-05-20 22:16:51', 0, NULL),
(82, 19, 5, NULL, 'Senin', '07:15:00', '07:30:00', '2025-05-21 01:55:40', '2025-05-21 01:55:40', 0, NULL),
(83, 19, 6, NULL, 'Selasa', '07:15:00', '07:30:00', '2025-05-21 01:55:40', '2025-05-21 01:55:40', 0, NULL),
(84, 19, 5, NULL, 'Rabu', '07:15:00', '07:30:00', '2025-05-21 01:55:40', '2025-05-21 01:55:40', 0, NULL),
(85, 19, 6, NULL, 'Kamis', '07:15:00', '07:30:00', '2025-05-21 01:55:40', '2025-05-21 01:55:40', 0, NULL),
(86, 19, 5, NULL, 'Jumat', '07:15:00', '07:30:00', '2025-05-21 01:55:40', '2025-05-21 01:55:40', 0, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `ekstrakurikulers`
--

CREATE TABLE `ekstrakurikulers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nama` varchar(255) NOT NULL,
  `unit_id` bigint(20) UNSIGNED NOT NULL,
  `deskripsi` text NOT NULL,
  `gambar` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `ekstrakurikulers`
--

INSERT INTO `ekstrakurikulers` (`id`, `nama`, `unit_id`, `deskripsi`, `gambar`, `created_at`, `updated_at`) VALUES
(1, 'Ekstrakurikuler SD', 1, 'Ekstrakurikuler SD', 'ekstrakurikuler/LuyQWOGEFem0Mz2R9NnDjB9oOtrtGtChWnhXD33s.jpg', '2025-03-11 21:23:11', '2025-05-05 04:48:02'),
(2, 'Ekstrakurikuler SMA', 4, 'Ekstrakurikuler SMA', 'ekstrakurikuler/hDuEnFHI98sbyvvHiDfW5WiDnlprpsW3bIElE1QU.jpg', '2025-03-11 23:11:39', '2025-03-12 18:08:26'),
(3, 'Ekstrakurikuler SMP', 2, 'Ekstrakurikuler SMP', 'ekstrakurikuler/1MJZl7wchR47axIQ27zs0RfNKibNxVKo0vRStk6n.jpg', '2025-03-12 18:05:58', '2025-03-12 18:05:58'),
(4, 'Ekstrakurikuler TK', 3, 'Ekstrakurikuler TK', 'ekstrakurikuler/iGeCOtaYxNwcWa0RD3tmJbmmDa1RZLAANc4QhcZ0.jpg', '2025-03-12 18:09:53', '2025-03-12 18:09:53'),
(5, 'Pramuka', 1, 'Ekstrakurikuler Pramuka', 'ekstrakurikuler/rGsdL1yrxHQzUNWBjJN5JwDHy3ATAlcVkRmJnySu.jpg', '2025-03-12 18:12:27', '2025-03-12 18:12:27'),
(6, 'tari', 2, 'rrrr', 'ekstrakurikuler/1G4t3WdTRjdBCtATe3k3CAmVQszWPKwYguVDEWzL.png', '2025-05-05 04:49:37', '2025-05-05 04:49:37'),
(7, 'tes gambar', 3, 'tes gambar', 'ekstrakurikuler/xzjeqn161mb0KwEyfI3YfO1wVM8o0uVdr7BxLf4Y.jpg', '2025-05-05 05:47:23', '2025-05-05 05:47:23');

-- --------------------------------------------------------

--
-- Table structure for table `events`
--

CREATE TABLE `events` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `judul` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `deskripsi` text NOT NULL,
  `tanggal` date NOT NULL,
  `lokasi` varchar(255) NOT NULL,
  `gambar` varchar(255) DEFAULT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `events`
--

INSERT INTO `events` (`id`, `judul`, `slug`, `deskripsi`, `tanggal`, `lokasi`, `gambar`, `unit_id`, `created_at`, `updated_at`) VALUES
(1, 'teess', 'teess', 'dfdss', '2025-02-24', 'eddd', '1741549699.jpg', NULL, '2025-03-09 12:48:20', '2025-03-09 12:48:20'),
(2, 'Juara', 'juara', 'Memperoleh juara', '2025-02-24', 'bengkalos', '1741550807.jpg', NULL, '2025-03-09 13:06:47', '2025-03-09 13:06:47'),
(3, 'juara nyayi', 'juara-nyayi', 'juara nyayi tingkat kabupaten', '2025-03-03', 'bengkalis', '1741551005.jpg', NULL, '2025-03-09 13:10:05', '2025-03-09 13:10:05'),
(4, 'contoh', 'contoh', 'sss', '2025-03-04', 'ssss', '1741570901.jpg', NULL, '2025-03-09 18:41:42', '2025-03-09 18:41:42'),
(5, 'tek kepsek', 'tek-kepsek', 'tesd', '2025-03-03', 'indo', '1741573660.jpg', 1, '2025-03-09 19:27:40', '2025-03-09 19:27:40'),
(6, 'sdates', 'sdates', 'sd', '2025-03-10', 'sda', '1741574723.jpg', 1, '2025-03-09 19:45:23', '2025-03-09 19:45:23'),
(7, 'tteestk', 'tteestk', 'kkl', '2025-03-19', 'ool', '1741606101.jpeg', 3, '2025-03-10 04:28:22', '2025-03-10 04:28:22');

-- --------------------------------------------------------

--
-- Table structure for table `facilities`
--

CREATE TABLE `facilities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `facilities`
--

INSERT INTO `facilities` (`id`, `title`, `description`, `image`, `created_at`, `updated_at`) VALUES
(1, 'Lapangan Olah Raga', '<p>Lapangan Olahra Tempat untuk berolahraga</p>', '1741684607.jpg', '2025-03-11 02:16:47', '2025-03-11 02:16:47');

-- --------------------------------------------------------

--
-- Table structure for table `gedungs`
--

CREATE TABLE `gedungs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `unit_id` bigint(20) UNSIGNED NOT NULL,
  `nama_gedung` varchar(255) NOT NULL,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `gedungs`
--

INSERT INTO `gedungs` (`id`, `unit_id`, `nama_gedung`, `keterangan`, `created_at`, `updated_at`) VALUES
(1, 1, 'Ruang 1 (TK)', 'Ruang Kelas', '2025-03-05 23:46:58', '2025-04-18 11:18:50'),
(2, 1, 'Ruang 2 (TK)', 'Ruang Kelas', '2025-03-24 22:17:17', '2025-04-18 11:18:59'),
(3, 1, 'Ruang 3 (TK)', 'Ruang Kelas', '2025-03-24 22:17:54', '2025-04-18 11:19:07'),
(4, 2, 'Ruang 1 (SD)', 'Ruang Kelas', '2025-03-24 23:48:29', '2025-04-18 11:19:22'),
(5, 2, 'Ruang 2 (SD)', 'Ruang Kelas', '2025-03-24 23:48:54', '2025-04-18 11:19:33'),
(6, 2, 'Ruang 3 (SD)', 'Ruang Kelas', '2025-03-24 23:49:21', '2025-04-18 11:19:58'),
(7, 2, 'Ruang 4 (SD)', 'Ruang Kelas', '2025-03-24 23:49:34', '2025-04-18 11:22:17'),
(8, 3, 'Ruang 1 (SMP)', 'Ruang Kelas', '2025-03-25 08:39:54', '2025-04-18 11:25:29'),
(9, 1, 'Tingkat Akhir SD', NULL, '2025-03-25 10:07:33', '2025-03-25 10:07:33'),
(10, 3, 'Ruang 2 (SMP)', 'Ruang Kelas', '2025-03-29 14:36:03', '2025-04-18 11:26:02'),
(11, 4, 'Ruang 1 (SMA)', 'Ruang Kelas', '2025-03-31 15:11:41', '2025-04-18 11:24:37'),
(12, 4, 'Ruang 2 (SMA)', 'Ruang Kelas', '2025-03-31 15:13:14', '2025-04-18 11:23:46'),
(13, 4, 'Ruang 3 (SMA)', 'Ruang Kelas', '2025-03-31 15:13:33', '2025-04-18 11:23:29'),
(14, 2, 'Tingkat Akhir SMP', NULL, '2025-03-31 15:14:11', '2025-03-31 15:14:11'),
(15, 4, 'Tingkat Akhir SMA', NULL, '2025-03-31 15:14:28', '2025-03-31 15:14:28'),
(16, 3, 'Tingkat Akhir TK', NULL, '2025-03-31 15:14:45', '2025-03-31 15:14:45');

-- --------------------------------------------------------

--
-- Table structure for table `guru`
--

CREATE TABLE `guru` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `unit_id` bigint(20) UNSIGNED NOT NULL,
  `nip` varchar(255) DEFAULT NULL,
  `nama` varchar(255) NOT NULL,
  `nik` varchar(16) NOT NULL,
  `jenis_kelamin` enum('L','P') NOT NULL,
  `tempat_lahir` varchar(100) NOT NULL,
  `tanggal_lahir` date NOT NULL,
  `nama_ibu_kandung` varchar(255) NOT NULL,
  `alamat` text NOT NULL,
  `kelurahan` varchar(100) NOT NULL,
  `kecamatan` varchar(100) NOT NULL,
  `kabupaten` varchar(100) NOT NULL,
  `provinsi` varchar(100) NOT NULL,
  `agama` varchar(10) NOT NULL,
  `npwp` varchar(30) DEFAULT NULL,
  `nama_wajib_pajak` varchar(255) DEFAULT NULL,
  `kewarganegaraan` varchar(50) NOT NULL,
  `status_kawin` varchar(50) NOT NULL DEFAULT 'Belum Kawin',
  `nama_pasangan` varchar(255) DEFAULT NULL,
  `pekerjaan_pasangan` varchar(100) DEFAULT NULL,
  `status_pegawai` varchar(50) DEFAULT NULL,
  `niy` varchar(30) DEFAULT NULL,
  `nuptk` varchar(30) DEFAULT NULL,
  `jenis_ptk` varchar(50) NOT NULL,
  `sk_pengangkatan` varchar(50) DEFAULT NULL,
  `tmt_pengangkatan` date DEFAULT NULL,
  `lembaga_pengangkat` varchar(100) DEFAULT NULL,
  `pangkat_golongan` varchar(50) DEFAULT NULL,
  `sk_penugasan` varchar(50) DEFAULT NULL,
  `tmt_penugasan` date DEFAULT NULL,
  `lembaga_penugasan` varchar(100) DEFAULT NULL,
  `pangkat_golongan_penugasan` varchar(50) DEFAULT NULL,
  `no_telp` varchar(15) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `mata_pelajaran` varchar(100) DEFAULT NULL,
  `status` enum('Aktif','Non-Aktif') NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `guru`
--

INSERT INTO `guru` (`id`, `unit_id`, `nip`, `nama`, `nik`, `jenis_kelamin`, `tempat_lahir`, `tanggal_lahir`, `nama_ibu_kandung`, `alamat`, `kelurahan`, `kecamatan`, `kabupaten`, `provinsi`, `agama`, `npwp`, `nama_wajib_pajak`, `kewarganegaraan`, `status_kawin`, `nama_pasangan`, `pekerjaan_pasangan`, `status_pegawai`, `niy`, `nuptk`, `jenis_ptk`, `sk_pengangkatan`, `tmt_pengangkatan`, `lembaga_pengangkat`, `pangkat_golongan`, `sk_penugasan`, `tmt_penugasan`, `lembaga_penugasan`, `pangkat_golongan_penugasan`, `no_telp`, `email`, `mata_pelajaran`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, NULL, 'satu', '12344', 'L', 'Duri', '1987-04-02', 'dd', 'jkkjjaa', 'jss', 'dds', 'sd', 'ds', 'Buddha', NULL, NULL, 'Indonesia', 'Belum Kawin', NULL, NULL, 'GTY', NULL, NULL, 'Guru Kelas', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Aktif', '2025-04-12 11:56:42', '2025-04-14 07:05:42'),
(2, 1, NULL, 'Guru SD1', '12', 'L', 'dss', '2025-04-02', 'qdas', 'asfa', 'asfas', 'asfas', 'asfa', 'asf', 'Buddha', NULL, NULL, 'Indonesia', 'Belum Kawin', NULL, NULL, 'Tetap', '23422', NULL, 'Guru Mata Pelajaran', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '4634', '<EMAIL>', NULL, 'Non-Aktif', '2025-04-14 00:17:49', '2025-04-15 06:09:20'),
(4, 4, NULL, 'Guru SMA1', '1232', 'L', 'dfsd', '2025-04-01', 'dsfs', 'sdfss', 'sevb', 'jhjg', 'hjkhg', 'ghkg', 'Katolik', '342', 'zxvz', 'Indonesia', 'Kawin', 'fygh', 'hggh', 'Kontrak', '45654', '3242', 'Guru Mata Pelajaran', NULL, NULL, 'jh', 'hjk', NULL, NULL, NULL, NULL, '231', '<EMAIL>', 'zxx', 'Aktif', '2025-04-14 02:40:33', '2025-04-14 03:43:43'),
(5, 2, NULL, 'Guru SMP1', '2352', 'L', 'sdgfs', '2025-04-02', 'sdg', 'dfd', 'dfdf', 'fgd', 'dfs', 'bxc', 'Islam', NULL, NULL, 'Indonesia', 'Belum Kawin', NULL, NULL, 'Tetap', '1233', NULL, 'Guru Mata Pelajaran', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '234', '<EMAIL>', NULL, 'Aktif', '2025-04-14 02:42:00', '2025-05-20 00:51:59'),
(6, 2, NULL, 'Guru SMP1', '214', 'L', 'DF', '2025-04-01', 'DF', 'DFGD', 'SDG', 'DGS', 'SDG', 'SDG', 'Islam', NULL, NULL, 'Indonesia', 'Belum Kawin', NULL, NULL, 'Tetap', '644', NULL, 'Guru Kelas', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '3252', '<EMAIL>', NULL, 'Aktif', '2025-04-14 02:44:00', '2025-04-14 03:46:14'),
(7, 3, NULL, 'Guru TK1', '234', 'P', 'asfas', '2025-04-03', 'sdfs', 'dssd', 'xcvx', 'cxv', 'cvxvx', 'sdvx', 'Buddha', 'xcv', NULL, 'Indonesia', 'Belum Kawin', NULL, NULL, 'Kontrak', '1221', NULL, 'Guru Kelas', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '32332', '<EMAIL>', NULL, 'Aktif', '2025-04-14 05:14:33', '2025-04-14 05:14:47'),
(8, 1, NULL, 'Tendik SD1', '124', 'L', 'sdfsd', '2025-04-02', 'sdfs', 'asfas', 'sdfs', 'sdf', 'sdf', 'sdf', 'Buddha', NULL, NULL, 'Indonesia', 'Belum Kawin', NULL, NULL, 'Tetap', NULL, NULL, 'Tenaga Kependidikan', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '74545', '<EMAIL>', NULL, 'Aktif', '2025-04-15 03:43:23', '2025-04-15 03:43:23'),
(9, 2, NULL, 'Guru SD1', '2233222', 'P', 'dsd', '2025-05-21', 'sdsdss', 'ffds', 'dfs', 'dfs', 'dfsdf', 'dsfs', 'Buddha', 'sdf', 'sdfsd', 'Indonesia', 'Kawin', 'sdfsdf', 'sdfs', 'Tetap', 'sdfs', 'dsfsd', 'Guru Kelas', 'sdfsd', '2025-05-19', NULL, NULL, 'sdf', NULL, NULL, NULL, '453453', '<EMAIL>', NULL, 'Aktif', '2025-05-19 04:25:27', '2025-05-19 04:25:27'),
(10, 2, NULL, 'Guru SD5', '1234', 'L', 'dfgdfg', '2025-05-06', 'sdsdsd', 'dfsdf', 'sdf', 'sdfsd', 'dfs', 'dfsds', 'Hindu', NULL, NULL, 'Indonesia', 'Kawin', 'sdf', 'sdfsd', 'Tetap', NULL, NULL, 'Guru Mata Pelajaran', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '078777', '<EMAIL>', NULL, 'Aktif', '2025-05-19 05:11:55', '2025-05-19 05:11:55'),
(11, 2, NULL, 'tendik sd3', '21212', 'L', 'assas', '2025-05-13', 'asasa', 'asddsf', 'asdas', 'asdas', 'asdas', 'sda', 'Katolik', NULL, NULL, 'Indonesia', 'Kawin', 'sdf', 'sdfsd', 'Tetap', NULL, NULL, 'Tenaga Kependidikan', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '0987', '<EMAIL>', NULL, 'Aktif', '2025-05-19 05:25:18', '2025-05-19 05:25:18'),
(12, 3, NULL, 'Tendik SMP1', '12233', 'L', 'dfgsdsd', '2025-05-09', 'dfsfsd', 'sdfs', 'dfsdf', 'dfs', 'dfsd', 'fsdfsd', 'Hindu', NULL, NULL, 'Indonesia', 'Belum Kawin', NULL, NULL, 'Kontrak', NULL, NULL, 'Tenaga Kependidikan', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '06767', '<EMAIL>', NULL, 'Aktif', '2025-05-20 00:39:04', '2025-05-20 00:39:04');

-- --------------------------------------------------------

--
-- Table structure for table `guru_tugastambahan`
--

CREATE TABLE `guru_tugastambahan` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `guru_id` bigint(20) UNSIGNED NOT NULL,
  `nama_guru` varchar(255) DEFAULT NULL,
  `tugas_tambahan` varchar(255) NOT NULL,
  `kelas` varchar(255) DEFAULT NULL,
  `tahun_ajaran_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `guru_tugastambahan`
--

INSERT INTO `guru_tugastambahan` (`id`, `guru_id`, `nama_guru`, `tugas_tambahan`, `kelas`, `tahun_ajaran_id`, `created_at`, `updated_at`) VALUES
(1, 1, 'satu', 'Wali Kelas', NULL, 2, '2025-05-09 23:43:42', '2025-05-09 23:53:29'),
(3, 4, 'Guru SMA1', 'Wali Kelas', NULL, 2, '2025-05-09 23:56:37', '2025-05-09 23:56:37'),
(5, 10, 'Guru SD5', 'Waka Kurikulum', NULL, 2, '2025-05-20 21:45:26', '2025-05-20 21:45:26'),
(6, 7, 'Guru TK1', 'Wali Kelas', 'Kela 7A', 4, '2025-05-20 22:01:09', '2025-05-20 22:01:09'),
(7, 11, NULL, 'Kepala sekolah', NULL, 2, '2025-05-30 06:37:03', '2025-05-30 06:37:03');

-- --------------------------------------------------------

--
-- Table structure for table `halaman`
--

CREATE TABLE `halaman` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `judul` varchar(255) NOT NULL,
  `konten` text NOT NULL,
  `slug` varchar(255) NOT NULL,
  `unit_id` bigint(20) UNSIGNED NOT NULL,
  `tipe` varchar(255) NOT NULL DEFAULT 'umum',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `halaman`
--

INSERT INTO `halaman` (`id`, `judul`, `konten`, `slug`, `unit_id`, `tipe`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'Profil SDS Pelopor', '<meta charset=\"UTF-8\">\r\n    <title>Data Sekolah</title>\r\n    <style>\r\n        table {\r\n            border-collapse: collapse;\r\n        }\r\n        td {\r\n            padding: 4px 8px;\r\n            vertical-align: top;\r\n        }\r\n        .label {\r\n            width: 200px;\r\n        }\r\n    </style>\r\n\r\n\r\n\r\n    <table>\r\n        <tbody><tr>\r\n            <td class=\"label\">1. SATUAN KERJA</td>\r\n            <td>:</td>\r\n            <td>SD SWASTA PELOPOR DURI</td>\r\n        </tr>\r\n        <tr>\r\n            <td class=\"label\">2. ALAMAT</td>\r\n            <td>:</td>\r\n            <td>JL. KAMPUNG LALANG KEL. AIR JAMBAN KEC. MANDAU KAB. BENGKALIS</td>\r\n        </tr>\r\n        <tr>\r\n            <td class=\"label\">3. NSS</td>\r\n            <td>:</td>\r\n            <td>104090204088</td>\r\n        </tr>\r\n    </tbody></table>', 'profil-sds-pelopor', 1, 'profil', 1, '2025-03-09 14:15:45', '2025-04-10 05:56:07'),
(3, 'Profil SMP', '<p>ini profil smp</p>', 'profil-smp', 2, 'profil', 1, '2025-03-09 14:38:00', '2025-03-09 14:38:00'),
(4, 'Sejarah', '<div><p>	Sekolah Dasar Pelopor adalah salah satu sekolah swasta di Duri \r\nyang berada di bawah naungan Yayasan Pubbarama. SDS Pelopor mulai \r\nberdiri pada tahun 2008, pada mulanya tempat belajar masih menumpang di \r\nrumah salah seorang anggota Yayasan yang beralamat di Jalan Kayangan \r\nNo.4 – Duri, karena belum mempunyai gedung sendiri. Pada tahun 2009 \r\nmulai dibangun gedung baru untuk SDS Pelopor, kemudian pada tahun 2010 \r\nkeluar ijin operasional sekolah SDS Pelopor, dan gedung sekolah mulai \r\ndibangun di Jalan Kampung Lalang Kelurahan Air Jamban, sehingga pada \r\nsaat mulai masuk tahun ajaran 2010/2011 gedung baru sudah dapat \r\ndigunakan.</p><p>	Tempat baru yang masih terbatas sarana dan prasarana \r\nini dimanfaatkan semaksimal mungkin untuk melaksanakan kegiatan \r\npembelajaran. Ruang kelas yang dibangun di tempat baru tersebut terdiri \r\ndari 8 ruangan dengan dua lantai. Untuk sementara ruangan di lantai 2 \r\n(sebanyak 2 ruangan) digunakan untuk tempat kebaktian bersama setiap \r\npagi sebelum pelajaran dimulai.</p><p>	Pada tahun pelajaran 2021/2022, \r\nSDS Pelopor Duri memiliki 20 rombel dengan jumlah siswa 465 orang dan \r\ntenaga pendidik dan kependidikan sejumlah 41 orang. Sarana dan prasarana\r\n yang sudah memadai mendukung kemajuan proses pendidikan di SDS Pelopor \r\nDuri.</p><p>	Harapan dari seluruh warga sekolah bahwa keberadaan SDS \r\nPelopor ini semoga dapat memberikan manfaat bagi masyarakat sekitar dan \r\njuga dapat memajukan pendidikan di Kabupaten Bengkalis pada umumnya. <br></p></div><p><br></p>', 'sejarah', 1, 'sejarah', 1, '2025-03-09 14:52:04', '2025-03-09 14:52:04'),
(5, 'sejarah2', '<p>ini sejarah sd</p>', 'sejarah2', 1, 'sejarah', 1, '2025-03-09 14:56:26', '2025-03-09 14:56:26'),
(6, 'visi misi', '<p>ini visimisi sd</p>', 'visi-misi', 1, 'visi-misi', 1, '2025-03-09 14:57:22', '2025-03-09 14:57:22'),
(7, 'Visi Misi TK', '<p>ini Visi misi TK</p>', 'visi-misi-tk', 3, 'visi-misi', 1, '2025-03-09 15:10:20', '2025-03-09 15:10:20'),
(9, 'profil', '<p>profil sma</p>', 'profil', 4, 'profil', 1, '2025-03-11 23:51:49', '2025-03-11 23:54:40'),
(13, 'Profil TK Pelopor', '<div class=\"profil-sekolah\">\r\n    <div class=\"label\"><span style=\"font-weight: normal;\">Nama Sekolah</span></div> <div class=\"separator\">:</div> <div class=\"value\">TAMAN KANAK-KANAK PELOPOR</div>\r\n    <div class=\"label\"><span style=\"font-weight: normal;\">No. Statistik Sekolah</span></div> <div class=\"separator\">:</div> <div class=\"value\">002090204022</div>\r\n    <div class=\"label\"><span style=\"font-weight: normal;\">Alamat Sekolah</span></div> <div class=\"separator\">:</div> <div class=\"value\">Jl. Kayangan No. 5 A Duri</div>\r\n    <div class=\"label\"><span style=\"font-weight: normal;\">Telepon / Hp</span></div> <div class=\"separator\">:</div> <div class=\"value\">08127626286</div>\r\n    <div class=\"label\"><span style=\"font-weight: normal;\">Status Sekolah</span></div> <div class=\"separator\">:</div> <div class=\"value\">Swasta</div>\r\n    <div class=\"label\"><span style=\"font-weight: normal;\">Status Pembinaan</span></div> <div class=\"separator\">:</div> <div class=\"value\">Dinas Pendidikan Kab Bengkalis u/p Korwilcam Mandau</div>\r\n    <div class=\"label\"><span style=\"font-weight: normal;\">Luas Tanah</span></div> <div class=\"separator\">:</div> <div class=\"value\">8 x 40 M²</div>\r\n    <div class=\"label\"><span style=\"font-weight: normal;\">Status Kepemilikan</span></div> <div class=\"separator\">:</div> <div class=\"value\">Yayasan Pubbarama Duri</div>\r\n       <div class=\"label\"><span style=\"font-weight: normal;\">Nilai Akreditasi</span></div> <div class=\"separator\">:</div> <div class=\"value\">A</div>\r\n</div>\r\n\r\n<style>\r\n    .profil-sekolah {\r\n        display: grid;\r\n        grid-template-columns: max-content 10px auto;\r\n        gap: 5px;\r\n        font-family: Arial, sans-serif;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .label {\r\n        font-weight: bold;\r\n    }\r\n\r\n    .separator {\r\n        text-align: center;\r\n    }\r\n\r\n    .value {\r\n        font-weight: normal;\r\n    }\r\n</style>', 'profil-tk-pelopor', 3, 'profil', 1, '2025-03-12 00:55:04', '2025-03-12 00:55:04');

-- --------------------------------------------------------

--
-- Table structure for table `jadwal_pelajaran`
--

CREATE TABLE `jadwal_pelajaran` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `kelas_id` bigint(20) UNSIGNED NOT NULL,
  `nama_kelas_text` varchar(255) NOT NULL,
  `wali_kelas` varchar(255) NOT NULL,
  `tahun_ajaran` varchar(255) NOT NULL,
  `layout_type` enum('layout_tk','layout_sd','layout_smp','layout_sma') DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `jadwal_pelajaran`
--

INSERT INTO `jadwal_pelajaran` (`id`, `kelas_id`, `nama_kelas_text`, `wali_kelas`, `tahun_ajaran`, `layout_type`, `is_active`, `created_at`, `updated_at`) VALUES
(12, 20, 'Kelas 5A', 'Doni', '2024/2025', 'layout_sd', 1, '2025-04-09 07:25:15', '2025-04-09 07:25:15'),
(14, 12, 'Kelas 1E', 'santi', '2024/2025', 'layout_sd', 1, '2025-04-18 16:10:11', '2025-04-18 16:10:11'),
(15, 10, 'Kelas 3A', 'tes guru', '2024/2025', 'layout_sd', 1, '2025-04-25 18:35:58', '2025-04-25 18:35:58'),
(16, 14, 'Kelas 9A', 'ss', '2024/2025', 'layout_smp', 1, '2025-04-25 19:07:17', '2025-04-25 19:07:17'),
(17, 2, 'Kelas B2', 'satu', '2024/2025', 'layout_tk', 1, '2025-05-20 22:16:51', '2025-05-20 22:16:51'),
(19, 1, 'Kelas 3Aw', 'Guru SMA1', '2024/2025', 'layout_sd', 1, '2025-05-21 01:55:40', '2025-05-21 01:55:40');

-- --------------------------------------------------------

--
-- Table structure for table `jam_pengganti`
--

CREATE TABLE `jam_pengganti` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `jurnal_kegiatan_id` bigint(20) UNSIGNED NOT NULL,
  `kelas` varchar(255) NOT NULL,
  `jam_ke` varchar(255) NOT NULL,
  `guru_diganti` varchar(255) NOT NULL,
  `jumlah_jam` decimal(5,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `jam_pengganti`
--

INSERT INTO `jam_pengganti` (`id`, `jurnal_kegiatan_id`, `kelas`, `jam_ke`, `guru_diganti`, `jumlah_jam`, `created_at`, `updated_at`) VALUES
(1, 34, '3', '4', 'Yudi', 1.00, '2025-04-08 05:06:22', '2025-04-08 05:06:22'),
(2, 33, '5', '2-4', 'ani', 2.00, '2025-04-08 05:25:48', '2025-04-08 05:25:48'),
(3, 35, '4', '4', 'Yudi', 1.00, '2025-04-08 06:13:15', '2025-04-08 06:13:15');

-- --------------------------------------------------------

--
-- Table structure for table `jenjangs`
--

CREATE TABLE `jenjangs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `jenjang` enum('PG','SD','SMP','SMA') NOT NULL,
  `tingkat` varchar(255) NOT NULL,
  `nama_jenjang` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `jenjangs`
--

INSERT INTO `jenjangs` (`id`, `jenjang`, `tingkat`, `nama_jenjang`, `created_at`, `updated_at`) VALUES
(1, 'PG', 'Tingkat 1', 'PG Tingkat 1', '2025-03-05 23:46:26', '2025-03-29 15:20:13'),
(2, 'PG', 'Tingkat 2', 'PG Tingkat 2', '2025-03-08 03:51:45', '2025-04-18 10:59:39'),
(3, 'PG', 'Tingkat 3', 'PG Tingkat 3', '2025-03-24 21:27:24', '2025-04-18 10:59:55'),
(4, 'SD', 'Tingkat 1', 'SD Tingkat 1', '2025-03-24 21:27:37', '2025-04-18 11:00:20'),
(5, 'SD', 'Tingkat 2', 'SD Tingkat 2', '2025-03-24 21:28:09', '2025-04-18 11:00:38'),
(6, 'SD', 'Tingkat 3', 'SD Tingkat 3', '2025-03-24 21:28:20', '2025-04-18 11:00:49'),
(7, 'SD', 'Tingkat 4', 'SD Tingkat 4', '2025-03-24 21:28:49', '2025-04-18 11:01:10'),
(8, 'SD', 'Tingkat 5', 'SD Tingkat 5', '2025-03-24 21:28:58', '2025-04-18 11:01:27'),
(9, 'SD', 'Tingkat 6', 'SD Tingkat 6', '2025-03-24 21:29:16', '2025-04-18 11:01:49'),
(10, 'SMP', 'Tingkat 1', 'SMP Tingkat 1', '2025-03-24 21:29:53', '2025-04-18 11:03:11'),
(11, 'SMP', 'Tingkat 2', 'SMP Tingkat 2', '2025-03-24 21:30:22', '2025-04-18 11:03:21'),
(12, 'SMP', 'Tingkat 3', 'SMP Tingkat 3', '2025-03-25 00:56:33', '2025-04-18 11:04:20'),
(13, 'SMA', 'Tingkat 1', 'SMA Tingkat 1', '2025-03-25 10:04:08', '2025-04-18 11:04:33'),
(14, 'SMA', 'Tingkat 2', 'SMA Tingkat 2', '2025-03-25 10:04:15', '2025-04-18 11:04:52'),
(15, 'SMA', 'Tingkat 3', 'SMA Tingkat 3', '2025-03-25 10:04:27', '2025-04-18 11:14:24'),
(16, 'PG', 'Tingkat Akhir', 'PG Tingkat Akhir', '2025-03-29 14:33:27', '2025-04-18 11:14:46'),
(17, 'SD', 'Tingkat Akhir', 'SD Tingkat Akhir', '2025-03-29 15:20:28', '2025-04-18 11:15:01'),
(18, 'SMP', 'Tingkat Akhir', 'SMP Tingkat Akhir', '2025-04-09 04:53:43', '2025-04-18 11:15:29'),
(19, 'SMA', 'Tingkat Akhir', 'SMA Tingkat Akhir', '2025-04-09 04:53:54', '2025-04-18 11:15:57');

-- --------------------------------------------------------

--
-- Table structure for table `jurnal_eskul`
--

CREATE TABLE `jurnal_eskul` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `jurnal_kegiatan_id` bigint(20) UNSIGNED NOT NULL,
  `nama_eskul` varchar(255) NOT NULL,
  `kelas` varchar(255) NOT NULL,
  `jumlah_siswa` int(11) NOT NULL DEFAULT 1,
  `kegiatan` varchar(255) NOT NULL,
  `jumlah_jam` decimal(5,2) NOT NULL DEFAULT 1.00,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `jurnal_eskul`
--

INSERT INTO `jurnal_eskul` (`id`, `jurnal_kegiatan_id`, `nama_eskul`, `kelas`, `jumlah_siswa`, `kegiatan`, `jumlah_jam`, `keterangan`, `created_at`, `updated_at`) VALUES
(1, 26, 'kom', '2', 25, 'word', 2.00, NULL, '2025-04-07 02:00:23', '2025-04-07 02:00:23'),
(2, 26, 'pramuka', '4', 18, 'tali temali', 2.00, NULL, '2025-04-07 02:00:23', '2025-04-07 02:00:23'),
(3, 27, 'kom', '3', 4, 'word1', 2.00, NULL, '2025-04-07 02:36:57', '2025-04-07 02:36:57'),
(4, 28, 'kom', '3', 4, 'word2', 1.00, NULL, '2025-04-07 04:47:20', '2025-04-07 04:47:20'),
(5, 29, 'kom', '7', 25, 'excel', 2.00, NULL, '2025-04-07 05:35:04', '2025-04-07 05:35:04'),
(6, 29, 'pramuka', '8', 30, 'pbb', 2.00, NULL, '2025-04-07 05:35:04', '2025-04-07 05:35:04'),
(7, 30, 'q', '2', 1, 'word', 2.00, NULL, '2025-04-07 06:26:22', '2025-04-07 06:26:22'),
(8, 31, '11', '7', 3, '33', 2.00, NULL, '2025-04-07 07:48:37', '2025-04-07 07:48:37'),
(9, 32, 'kom', '2', 1, 'word', 1.00, NULL, '2025-04-08 00:08:17', '2025-04-08 00:08:17'),
(10, 32, 'pramuka', '5', 1, 'tali temali', 1.00, NULL, '2025-04-08 00:08:17', '2025-04-08 00:08:17'),
(12, 33, 'kom', '3', 1, '11', 1.00, NULL, '2025-04-08 05:25:48', '2025-04-08 05:25:48'),
(13, 35, 'tari', '1', 1, 'nari', 1.00, NULL, '2025-04-08 06:13:15', '2025-04-08 06:13:15');

-- --------------------------------------------------------

--
-- Table structure for table `jurnal_kegiatan`
--

CREATE TABLE `jurnal_kegiatan` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `tanggal` date NOT NULL,
  `kegiatan` text NOT NULL,
  `ada` tinyint(1) NOT NULL DEFAULT 0,
  `tidak` tinyint(1) NOT NULL DEFAULT 0,
  `keterangan` text DEFAULT NULL,
  `jam_mengajar` decimal(5,2) NOT NULL DEFAULT 0.00,
  `jam_tambahan` decimal(5,2) NOT NULL DEFAULT 0.00,
  `detail_jam_mengajar` text DEFAULT NULL,
  `detail_jam_tambahan` text DEFAULT NULL,
  `status` enum('draft','submitted','approved','rejected') NOT NULL DEFAULT 'draft',
  `feedback` text DEFAULT NULL,
  `approved_by` bigint(20) UNSIGNED DEFAULT NULL,
  `submitted_at` timestamp NULL DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `rejected_by` bigint(20) UNSIGNED DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL,
  `alasan_penolakan` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `jurnal_kegiatan`
--

INSERT INTO `jurnal_kegiatan` (`id`, `user_id`, `unit_id`, `tanggal`, `kegiatan`, `ada`, `tidak`, `keterangan`, `jam_mengajar`, `jam_tambahan`, `detail_jam_mengajar`, `detail_jam_tambahan`, `status`, `feedback`, `approved_by`, `submitted_at`, `approved_at`, `created_at`, `updated_at`, `rejected_by`, `rejected_at`, `alasan_penolakan`) VALUES
(11, 3, 2, '2025-03-01', 'KBM 1', 0, 0, 'wegrhfg', 0.00, 0.00, NULL, NULL, 'approved', NULL, NULL, '2025-03-13 23:59:07', '2025-03-13 23:59:25', '2025-03-13 23:09:51', '2025-03-13 23:59:25', 1, '2025-03-13 23:50:28', 'belum jelas'),
(12, 3, 2, '2025-03-02', 'kasaskkd;', 1, 0, 'asfaefsdaf', 0.00, 0.00, NULL, NULL, 'approved', NULL, NULL, '2025-03-13 23:49:59', '2025-03-13 23:50:15', '2025-03-13 23:28:41', '2025-03-13 23:50:15', NULL, NULL, NULL),
(13, 3, 2, '2025-03-04', 'sdf df', 1, 0, 'zsgdgzz', 0.00, 0.00, NULL, NULL, 'submitted', NULL, NULL, '2025-03-27 04:12:06', NULL, '2025-03-13 23:49:45', '2025-03-27 04:12:06', 1, '2025-03-13 23:52:24', 'tolak2'),
(14, 3, 2, '2025-03-10', 'najar', 1, 0, 'kelas1', 0.00, 0.00, NULL, NULL, 'approved', NULL, NULL, '2025-03-27 04:08:50', '2025-03-27 04:09:10', '2025-03-27 04:08:31', '2025-03-27 04:09:10', NULL, NULL, NULL),
(15, 8, 1, '2025-04-01', 'Mengajar', 1, 0, 'jjj dfs', 0.00, 0.00, NULL, NULL, 'rejected', NULL, NULL, '2025-04-04 13:06:43', NULL, '2025-04-04 13:06:07', '2025-04-06 00:43:41', 9, '2025-04-06 00:43:41', 'rrrrrrrrr'),
(16, 8, NULL, '2025-04-03', 'tes setlah filtervv', 1, 0, 'tes setalah filter', 0.00, 0.00, NULL, NULL, 'rejected', NULL, NULL, '2025-04-04 14:31:30', NULL, '2025-04-04 14:31:20', '2025-04-08 05:47:27', 9, '2025-04-08 05:46:12', 'tidak dapat di lihat'),
(17, 8, 1, '2025-04-01', 'setalah 2', 0, 0, 'setlah filter2', 0.00, 0.00, NULL, NULL, 'approved', NULL, NULL, '2025-04-04 14:34:58', '2025-04-04 23:34:42', '2025-04-04 14:34:28', '2025-04-04 23:34:42', NULL, NULL, NULL),
(18, 8, 1, '2025-04-03', 'tes3', 1, 0, 'tes 3333', 0.00, 0.00, NULL, NULL, 'draft', NULL, NULL, NULL, NULL, '2025-04-04 14:39:02', '2025-04-04 14:52:09', NULL, NULL, NULL),
(19, 8, 1, '2025-04-05', 'KBM\r\nsenam\r\nkerjakan  rpp\r\nolah raga\r\nkunjungan', 1, 0, 'kegiatan berjalan dengan lancar', 0.00, 0.00, NULL, NULL, 'approved', NULL, NULL, '2025-04-05 00:00:58', '2025-04-08 05:42:38', '2025-04-05 00:00:22', '2025-04-08 05:42:38', NULL, NULL, NULL),
(20, 8, 1, '2025-04-06', 'senam', 1, 0, 'lancar', 0.00, 0.00, NULL, NULL, 'approved', NULL, NULL, '2025-04-06 00:43:18', '2025-04-06 00:43:30', '2025-04-06 00:42:08', '2025-04-06 00:43:30', NULL, NULL, NULL),
(21, 8, 1, '2025-04-06', 'tses jam', 1, 0, NULL, 0.00, 1.00, '[]', '[{\"kelas\":\"kelas\",\"mapel\":\"mtk\",\"waktu_mulai\":\"11:32\",\"waktu_selesai\":\"12:34\",\"waktu\":\"11:32 - 12:34\",\"durasi\":\"1.0\"}]', 'submitted', NULL, NULL, '2025-04-06 03:34:01', NULL, '2025-04-06 03:33:40', '2025-04-06 03:34:01', NULL, NULL, NULL),
(22, 8, 1, '2025-04-06', 'tesa 2', 1, 0, NULL, 0.00, 0.50, '[]', '[{\"kelas\":\"kelas 1A\",\"mapel\":\"MTK\",\"waktu_mulai\":\"07:30\",\"waktu_selesai\":\"08:00\",\"waktu\":\"07:30 - 08:00\",\"durasi\":\"0.5\"}]', 'draft', NULL, NULL, NULL, NULL, '2025-04-06 04:09:44', '2025-04-07 02:35:19', NULL, NULL, NULL),
(23, 8, 1, '2025-04-06', 'kbm', 1, 0, NULL, 0.00, 0.00, '[]', '[]', 'draft', NULL, NULL, NULL, NULL, '2025-04-06 05:32:45', '2025-04-06 05:32:45', NULL, NULL, NULL),
(24, 8, 1, '2025-04-06', 'kbm', 1, 0, NULL, 0.00, 0.00, '[]', '[]', 'draft', NULL, NULL, NULL, NULL, '2025-04-06 06:20:47', '2025-04-06 06:20:47', NULL, NULL, NULL),
(25, 8, 1, '2025-04-06', 'hjkl bb', 1, 0, NULL, 0.00, 0.00, '[]', '[]', 'draft', NULL, NULL, NULL, NULL, '2025-04-06 07:09:49', '2025-04-06 10:29:46', NULL, NULL, NULL),
(26, 8, 1, '2025-04-07', 'kbm 7', 1, 0, NULL, 0.00, 0.00, NULL, NULL, 'approved', NULL, NULL, '2025-04-07 02:34:56', '2025-04-07 02:52:11', '2025-04-07 02:00:23', '2025-04-07 02:52:11', NULL, NULL, NULL),
(27, 8, 1, '2025-04-06', 'kbm -6', 1, 0, NULL, 0.00, 0.00, NULL, NULL, 'approved', NULL, NULL, '2025-04-07 02:37:04', '2025-04-07 02:52:14', '2025-04-07 02:36:57', '2025-04-07 02:52:14', NULL, NULL, NULL),
(28, 8, 1, '2025-04-05', 'kbm-5', 1, 0, NULL, 0.00, 0.00, NULL, NULL, 'approved', NULL, NULL, '2025-04-07 04:48:14', '2025-04-07 04:48:42', '2025-04-07 04:47:20', '2025-04-07 04:48:42', NULL, NULL, NULL),
(29, 22, 2, '2025-04-07', 'kbmsmp 7', 1, 0, NULL, 0.00, 0.00, NULL, NULL, 'approved', NULL, NULL, '2025-04-07 05:35:09', '2025-04-07 05:35:27', '2025-04-07 05:35:04', '2025-04-07 05:35:27', NULL, NULL, NULL),
(30, 22, 2, '2025-04-06', 'kbm 6', 1, 0, NULL, 0.00, 0.00, NULL, NULL, 'draft', NULL, NULL, NULL, NULL, '2025-04-07 06:26:22', '2025-04-07 06:26:22', NULL, NULL, NULL),
(31, 22, 2, '2025-04-05', 'kbm 5', 1, 0, NULL, 0.00, 0.00, NULL, NULL, 'draft', NULL, NULL, NULL, NULL, '2025-04-07 07:48:37', '2025-04-07 07:48:37', NULL, NULL, NULL),
(32, 8, 1, '2025-04-08', 'kbm 8', 1, 0, NULL, 0.00, 0.00, NULL, NULL, 'draft', NULL, NULL, NULL, NULL, '2025-04-08 00:08:17', '2025-04-08 00:08:17', NULL, NULL, NULL),
(33, 8, 1, '2025-04-08', 'kbm 8 1', 1, 0, NULL, 0.00, 0.00, NULL, NULL, 'approved', NULL, NULL, '2025-04-08 05:26:08', '2025-04-08 05:27:07', '2025-04-08 01:06:48', '2025-04-08 05:27:07', NULL, NULL, NULL),
(34, 8, 1, '2025-04-08', 'kbm 8 2', 1, 0, NULL, 0.00, 0.00, NULL, NULL, 'approved', NULL, NULL, '2025-04-08 05:06:31', '2025-04-08 05:13:59', '2025-04-08 05:06:22', '2025-04-08 05:13:59', NULL, NULL, NULL),
(35, 23, 1, '2025-04-08', 'KBM 8', 1, 0, NULL, 0.00, 0.00, NULL, NULL, 'approved', NULL, NULL, '2025-04-08 06:13:27', '2025-04-08 06:13:40', '2025-04-08 06:13:15', '2025-04-08 06:13:40', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `kalender_pendidikan`
--

CREATE TABLE `kalender_pendidikan` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `judul` varchar(255) NOT NULL,
  `tanggal_mulai` date NOT NULL,
  `tanggal_selesai` date NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `warna` varchar(255) NOT NULL DEFAULT '#007bff',
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kalender_pendidikan`
--

INSERT INTO `kalender_pendidikan` (`id`, `judul`, `tanggal_mulai`, `tanggal_selesai`, `deskripsi`, `warna`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'PAS', '2025-05-19', '2025-05-23', 'Ujian Pas SD', '#007bff', 1, '2025-05-08 06:56:03', '2025-05-08 06:56:03'),
(2, 'Libur', '2025-05-20', '2025-05-20', 'Libur tes', '#dc3545', 1, '2025-05-08 07:02:06', '2025-05-08 07:02:06');

-- --------------------------------------------------------

--
-- Table structure for table `kelas`
--

CREATE TABLE `kelas` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `jenjang_id` bigint(20) UNSIGNED NOT NULL,
  `gedung_id` bigint(20) UNSIGNED DEFAULT NULL,
  `nama` varchar(255) NOT NULL,
  `tingkat` varchar(255) DEFAULT NULL,
  `tahun_ajaran` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `kelas`
--

INSERT INTO `kelas` (`id`, `unit_id`, `jenjang_id`, `gedung_id`, `nama`, `tingkat`, `tahun_ajaran`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 1, '1 Kelas A1', 'Tingkat 1', '2024/2025', '2025-03-05 23:46:39', '2025-04-18 15:52:20'),
(2, 1, 2, 2, '4 Kelas B2', 'Tingkat 2', '2024/2025', '2025-03-08 03:50:57', '2025-04-18 15:52:54'),
(3, 1, 1, 1, '2 Kelas A2', 'Tingkat 1', '2024/2025', '2025-03-24 20:50:26', '2025-04-18 15:52:28'),
(4, 1, 2, 1, '3 Kelas B1', 'Tingkat 2', '2024/2025', '2025-03-24 22:19:16', '2025-04-18 15:52:43'),
(10, 2, 6, 6, '8 Kelas 3A', 'Tingkat 2', '2024/2025', '2025-03-29 16:53:19', '2025-04-18 15:51:33'),
(11, 1, 3, 3, '5 Kelas B3', 'Tingkat 2', '2024/2025', '2025-03-29 17:07:21', '2025-04-18 15:53:11'),
(12, 2, 4, 4, '6 Kelas 1e', 'Tingkat 1', '2024/2025', '2025-03-31 15:56:56', '2025-04-18 15:51:14'),
(13, 2, 6, 6, '9 Kelas 3A', 'Tingkat 2', '2024/2025', '2025-03-31 15:57:57', '2025-04-18 15:51:41'),
(14, 3, 12, 10, '11 Kelas 9A smp', 'Tingkat 3', '2024/2025', '2025-03-31 15:58:24', '2025-04-18 15:52:13'),
(15, 2, 5, 5, '7 Kelas 2A', 'Tingkat 1', '2024/2025', '2025-03-31 16:01:21', '2025-04-18 15:51:26'),
(16, 4, 14, 12, '11 Kelas 11', 'Tingkat 2', '2024/2025', '2025-03-31 16:03:15', '2025-04-18 15:50:50'),
(17, 4, 15, 13, '14 Kelas 12', 'Tingkat 3', '2024/2025', '2025-03-31 16:04:18', '2025-04-18 15:51:04'),
(18, 2, 17, 9, '15 SD Angkata 724/25', 'Tingkat Akhir', '2024/2025', '2025-04-01 15:44:55', '2025-04-18 15:53:43'),
(19, 3, 14, 14, '13 SMP Angkatan 7 24', 'Tingkat Akhir', '2024/2025', '2025-04-03 18:58:30', '2025-04-18 15:53:59'),
(20, 2, 8, 7, '10 Kelas 5A', 'Tingkat 5', '2024/2025', '2025-04-09 04:56:56', '2025-04-18 15:51:50'),
(21, 3, 18, 10, 'Kelas 9B', 'Tingkat Akhir', '2024/2025', '2025-04-18 23:39:46', '2025-04-18 23:39:46'),
(22, 2, 4, 4, 'Kelas 1A', 'Tingkat 1', '2025/2026', '2025-04-23 04:20:28', '2025-04-23 04:20:28'),
(23, 2, 5, 5, 'Kelas 2A', 'Tingkat 2', '2025/2026', '2025-04-23 04:26:16', '2025-04-23 04:26:16'),
(24, 1, 2, 2, 'B1', 'Tingkat 2', '2025/2026', '2025-04-23 04:26:39', '2025-04-23 04:26:39'),
(25, 3, 11, 10, 'Kelas 8A', 'Tingkat 2', '2025/2026', '2025-04-23 07:21:02', '2025-04-23 07:21:02');

-- --------------------------------------------------------

--
-- Table structure for table `laporan_kerja`
--

CREATE TABLE `laporan_kerja` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uraian_kerja` text NOT NULL,
  `waktu_mulai` text NOT NULL,
  `waktu_selesai` text DEFAULT NULL,
  `status` enum('selesai','proses','tertunda') NOT NULL,
  `keterangan` text DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `laporan_kerja`
--

INSERT INTO `laporan_kerja` (`id`, `uraian_kerja`, `waktu_mulai`, `waktu_selesai`, `status`, `keterangan`, `user_id`, `created_at`, `updated_at`) VALUES
(3, 'Membuat Menu Guru', '2025-04-03T00:00', '2025-04-08T00:09', 'proses', 'Proses', 1, '2025-04-15 07:10:03', '2025-04-15 07:10:03'),
(4, 'Membuat laporan', '2025-04-04T00:00', '2025-04-04T00:32', 'tertunda', NULL, 1, '2025-04-15 07:13:02', '2025-04-15 07:13:02'),
(5, 'Membuat maping rombel', '2025-04-05 00:23:00', NULL, 'proses', NULL, 1, '2025-04-15 07:18:17', '2025-04-15 07:18:17'),
(6, 'tes 1', '2025-04-06 00:23:00', NULL, 'tertunda', 'tidak jadi', 1, '2025-04-15 07:26:06', '2025-04-15 07:26:06'),
(7, 'tes3', '2025-04-08 00:23:00', NULL, 'proses', 'sedang dalam pengerjaan', 1, '2025-04-15 07:30:18', '2025-04-15 07:30:18'),
(8, 'masik', '2025-04-05 00:45:00', NULL, 'proses', 'proses ini', 1, '2025-04-15 07:30:58', '2025-04-15 07:30:58'),
(9, 'memulai', '2025-04-05 00:56:00', NULL, 'proses', 'prosesl lagi', 1, '2025-04-15 07:34:39', '2025-04-15 07:34:39');

-- --------------------------------------------------------

--
-- Table structure for table `laporan_kerusakan`
--

CREATE TABLE `laporan_kerusakan` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `judul` varchar(255) NOT NULL,
  `deskripsi` text NOT NULL,
  `lokasi` varchar(255) NOT NULL,
  `status` enum('dilaporkan','diproses','selesai') NOT NULL DEFAULT 'dilaporkan',
  `tindakan` text DEFAULT NULL,
  `pelapor_id` bigint(20) UNSIGNED NOT NULL,
  `penindak_id` bigint(20) UNSIGNED DEFAULT NULL,
  `tanggal_lapor` timestamp NULL DEFAULT NULL,
  `tanggal_proses` timestamp NULL DEFAULT NULL,
  `tanggal_selesai` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `laporan_kerusakan`
--

INSERT INTO `laporan_kerusakan` (`id`, `judul`, `deskripsi`, `lokasi`, `status`, `tindakan`, `pelapor_id`, `penindak_id`, `tanggal_lapor`, `tanggal_proses`, `tanggal_selesai`, `created_at`, `updated_at`) VALUES
(1, 'Kerusakan', 'mati', 'Ruang SD', 'selesai', 'Sudah diperbaiki pada hari senin', 8, 1, '2025-05-04 06:05:10', NULL, '2025-05-04 06:05:10', '2025-05-04 05:43:25', '2025-05-04 06:05:10'),
(2, 'Kerusakan kursi', 'patah', 'Ruang SD 1', 'selesai', 'SUdah diperbaiki', 8, 1, '2025-05-30 06:18:46', NULL, '2025-05-30 06:18:46', '2025-05-04 06:06:10', '2025-05-30 06:18:46'),
(3, 'Kerusakan', 'meja kotor', 'kelas 1', 'diproses', NULL, 8, 9, '2025-05-31 00:28:47', NULL, NULL, '2025-05-31 00:21:27', '2025-05-31 00:28:47'),
(4, 'Meja Rusak', 'meja telah rusak', 'kelas 1', 'selesai', 'Sudah di perbaiki', 8, 9, '2025-05-31 00:52:29', '2025-05-31 00:50:13', '2025-05-31 00:52:29', '2025-05-31 00:49:29', '2025-05-31 00:52:29'),
(5, 'Kerusakan', 'lampu mati', 'kelas 2', 'diproses', NULL, 8, 9, '2025-05-31 01:12:58', '2025-05-31 01:13:23', NULL, '2025-05-31 01:12:58', '2025-05-31 01:13:23');

-- --------------------------------------------------------

--
-- Table structure for table `mata_pelajaran`
--

CREATE TABLE `mata_pelajaran` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `nama_mapel` varchar(255) NOT NULL,
  `pengajar_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `mata_pelajaran`
--

INSERT INTO `mata_pelajaran` (`id`, `unit_id`, `nama_mapel`, `pengajar_id`, `created_at`, `updated_at`) VALUES
(5, 2, 'IPAS', 8, '2025-04-01 14:24:19', '2025-04-18 11:48:06'),
(6, 3, 'Matematika', 3, '2025-04-01 14:24:52', '2025-04-18 11:49:00'),
(7, 3, 'IPAS', 22, '2025-04-04 08:44:03', '2025-04-18 11:49:08'),
(8, 2, 'B Indonesia', 23, '2025-04-18 11:49:41', '2025-04-18 11:49:41'),
(9, 2, 'Informatika', 24, '2025-04-18 11:50:04', '2025-04-18 11:50:04');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '2024_03_01_000000_create_cache_tables', 1),
(2, '2024_03_01_000001_create_units_table', 1),
(3, '2024_03_01_000002_create_permission_tables', 1),
(4, '2024_03_01_000003_create_users_table', 1),
(5, '2024_03_01_000004_create_sessions_table', 1),
(6, '2024_03_01_000005_fix_model_has_permissions_structure', 1),
(7, '2024_01_20_create_adm_gurus_table', 2),
(8, '2024_01_21_create_jenjangs_table', 2),
(9, '2024_01_22_create_kelas_table', 2),
(10, '2024_01_23_create_mata_pelajaran_table', 2),
(11, '2024_01_24_create_jadwal_pelajaran_table', 2),
(12, '2024_01_25_add_istirahat_columns_to_detail_jadwal', 2),
(13, '[timestamp]_create_gedungs_table', 3),
(14, '[timestamp]_create_peserta_didik_table', 3),
(15, '[timestamp]_modify_peserta_didik_table', 3),
(16, '2024_01_17_000000_create_adm_guru_table', 4),
(17, '2024_03_15_add_adm_guru_permissions', 5),
(18, '2024_03_04_update_layout_type_enum', 6),
(19, '2024_03_14_update_status_enum_adm_gurus', 6),
(20, '2024_03_16_create_adm_waka_table', 7),
(21, '2024_01_20_create_settings_table', 8),
(22, '2024_01_20_create_slides_table', 8),
(23, '2024_01_20_create_achievements_table', 9),
(24, '2024_01_20_create_articles_table', 9),
(25, '2024_01_20_create_extracurriculars_table', 9),
(26, '2024_01_10_create_events_table', 10),
(27, 'create_halaman_table', 11),
(28, '2024_01_09_create_halaman_table', 12),
(29, '2024_03_21_add_unit_id_to_achievements_table', 13),
(30, '2024_03_21_create_achievements_table', 14),
(31, '2024_03_22_create_achievements_table', 15),
(32, '2024_03_21_add_unit_id_to_articles_table', 16),
(33, '[timestamp]_create_facilities_table', 17),
(34, '2024_01_21_create_extracurriculars_table', 18),
(35, '2024_01_21_create_ekstrakurikuler_table', 19),
(36, '2024_03_23_create_achievements_table', 20),
(37, '2024_03_24_add_unit_id_to_ekstrakurikuler', 21),
(38, '2024_03_12_create_ekstrakurikulers_table', 22),
(39, '[timestamp]_create_jurnal_kegiatan_table', 23),
(40, '2025_03_14_064510_add_rejection_fields_to_jurnal_kegiatan_table', 24),
(41, '2023_08_01_000001_create_tahun_ajaran_table', 25),
(42, '2025_03_25_create_riwayat_kelas_table', 26),
(43, '2025_03_26_create_riwayat_kelas_table', 27),
(44, '2023_10_15_create_sarana_table', 28),
(45, '2025_03_01_create_adm_kepsek_table', 29),
(46, '2025_03_29_235102_add_columns_to_kelas_table', 30),
(47, '2025_04_01_create_adm_ktsp_table', 31),
(48, '2025_04_01_add_unit_id_to_mata_pelajaran_table', 32),
(49, '2024_03_26_remove_jenjang_id_from_mata_pelajaran_table', 33),
(50, '2025_05_01_add_unit_id_to_adm_waka_table', 34),
(51, '2025_05_10_add_unit_id_to_adm_kepsek_table', 35),
(52, '2025_05_15_add_unit_id_to_adm_guru_table', 36),
(53, '2025_05_20_add_unit_id_to_adm_ktsp_table', 37),
(54, '2025_04_03_210532_add_mutasi_keluar_to_jenis_perpindahan_enum', 38),
(55, '2025_04_03_211435_add_status_column_to_peserta_didik_table', 39),
(56, '2025_04_03_213036_allow_null_kelas_id_in_peserta_didik_table', 40),
(57, '2024_03_27_add_sekolah_tujuan_to_riwayat_kelas_table', 41),
(58, '2025_05_25_add_unit_id_to_peserta_didik_table', 42),
(59, '2024_05_01_add_unit_id_to_jurnal_kegiatan_table', 43),
(60, '2025_04_06_add_teaching_hours_to_jurnal_kegiatan', 44),
(61, '2024_05_15_create_jurnal_eskul_table', 45),
(62, '2025_05_10_create_jam_pengganti_table', 46),
(63, '2023_10_15_create_absensi_siswa_table', 47),
(64, '2023_06_15_create_spps_table', 48),
(65, '2023_06_15_create_spp_uploads_table', 49),
(66, '2024_05_20_create_guru_table', 50),
(67, '2025_04_12_add_default_to_status_kawin', 51),
(68, '2024_05_25_create_pengembangan_diri_table', 52),
(69, '2023_10_15_add_last_activity_to_users_table', 53),
(70, '2023_10_15_create_laporan_kerja_table', 54),
(71, '2024_04_23_add_tanggal_lulus_to_siswa_table', 55),
(72, '2024_05_30_add_nama_pd_to_riwayat_kelas_table', 56),
(73, '2024_05_20_create_rombel_ekstrakurikuler_table', 57),
(74, '2024_05_20_create_anggota_ekstrakurikuler_table', 58),
(75, '2024_05_21_add_kelas_to_anggota_ekstrakurikuler_table', 59),
(76, '2023_10_15_create_rombel_ekstrakurikulers_table', 60),
(77, '2023_10_15_create_anggota_ekstrakurikulers_table', 61),
(78, '2025_05_25_add_no_barang_to_sarana_table', 62),
(79, '2023_06_01_add_gedung_id_to_sarana', 63),
(80, '2023_10_20_create_laporan_kerusakan_table', 64),
(81, '2023_10_20_create_notifikasi_table', 65),
(82, '2023_08_01_create_kalender_pendidikan_table', 66),
(83, '2023_08_15_create_guru_tugastambahan_table', 67),
(84, '2023_08_16_add_nama_guru_to_guru_tugastambahan_table', 68),
(85, '2023_08_17_add_kelas_to_guru_tugastambahan_table', 69),
(86, '2025_add_tanggal_proses_to_laporan_kerusakan', 70),
(87, '2025_05_31_080704_fix_tanggal_lapor_column', 71);

-- --------------------------------------------------------

--
-- Table structure for table `model_has_permissions`
--

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `model_has_permissions`
--

INSERT INTO `model_has_permissions` (`permission_id`, `model_type`, `model_id`) VALUES
(32, 'App\\Models\\User', 3),
(32, 'App\\Models\\User', 8),
(33, 'App\\Models\\User', 3),
(33, 'App\\Models\\User', 8);

-- --------------------------------------------------------

--
-- Table structure for table `model_has_roles`
--

CREATE TABLE `model_has_roles` (
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `model_has_roles`
--

INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\User', 1),
(1, 'App\\Models\\User', 4),
(1, 'App\\Models\\User', 5),
(2, 'App\\Models\\User', 3),
(2, 'App\\Models\\User', 8),
(2, 'App\\Models\\User', 10),
(2, 'App\\Models\\User', 22),
(2, 'App\\Models\\User', 23),
(2, 'App\\Models\\User', 24),
(2, 'App\\Models\\User', 25),
(2, 'App\\Models\\User', 26),
(3, 'App\\Models\\User', 9),
(3, 'App\\Models\\User', 15),
(3, 'App\\Models\\User', 18),
(3, 'App\\Models\\User', 21),
(4, 'App\\Models\\User', 2),
(4, 'App\\Models\\User', 16),
(4, 'App\\Models\\User', 20),
(6, 'App\\Models\\User', 13),
(6, 'App\\Models\\User', 17),
(7, 'App\\Models\\User', 12),
(7, 'App\\Models\\User', 14),
(7, 'App\\Models\\User', 19),
(8, 'App\\Models\\User', 11);

-- --------------------------------------------------------

--
-- Table structure for table `notifikasi`
--

CREATE TABLE `notifikasi` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `judul` varchar(255) NOT NULL,
  `pesan` text NOT NULL,
  `jenis` varchar(255) NOT NULL DEFAULT 'info',
  `link` varchar(255) DEFAULT NULL,
  `dibaca` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifikasi`
--

INSERT INTO `notifikasi` (`id`, `user_id`, `judul`, `pesan`, `jenis`, `link`, `dibaca`, `created_at`, `updated_at`) VALUES
(2, 4, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di Ruang SD', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/1', 0, '2025-05-04 05:43:25', '2025-05-04 05:43:25'),
(3, 5, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di Ruang SD', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/1', 0, '2025-05-04 05:43:25', '2025-05-04 05:43:25'),
(4, 9, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di Ruang SD', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/1', 1, '2025-05-04 05:43:25', '2025-05-09 04:35:06'),
(5, 12, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di Ruang SD', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/1', 0, '2025-05-04 05:43:25', '2025-05-04 05:43:25'),
(6, 14, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di Ruang SD', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/1', 0, '2025-05-04 05:43:25', '2025-05-04 05:43:25'),
(7, 15, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di Ruang SD', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/1', 1, '2025-05-04 05:43:25', '2025-05-19 05:13:11'),
(8, 18, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di Ruang SD', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/1', 0, '2025-05-04 05:43:25', '2025-05-04 05:43:25'),
(9, 19, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di Ruang SD', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/1', 0, '2025-05-04 05:43:25', '2025-05-04 05:43:25'),
(10, 21, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di Ruang SD', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/1', 0, '2025-05-04 05:43:25', '2025-05-04 05:43:25'),
(11, 8, 'Laporan Kerusakan Diproses', 'Laporan kerusakan Anda: Kerusakan sedang diproses oleh Administrator', 'info', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/1', 1, '2025-05-04 06:04:28', '2025-05-30 07:45:39'),
(12, 8, 'Laporan Kerusakan Selesai', 'Laporan kerusakan Anda: Kerusakan telah selesai ditangani', 'success', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/1', 1, '2025-05-04 06:05:10', '2025-05-30 07:45:39'),
(14, 4, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan kursi di Ruang SD 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/2', 0, '2025-05-04 06:06:10', '2025-05-04 06:06:10'),
(15, 5, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan kursi di Ruang SD 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/2', 0, '2025-05-04 06:06:10', '2025-05-04 06:06:10'),
(16, 9, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan kursi di Ruang SD 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/2', 1, '2025-05-04 06:06:10', '2025-05-09 04:35:06'),
(17, 12, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan kursi di Ruang SD 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/2', 0, '2025-05-04 06:06:10', '2025-05-04 06:06:10'),
(18, 14, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan kursi di Ruang SD 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/2', 0, '2025-05-04 06:06:10', '2025-05-04 06:06:10'),
(19, 15, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan kursi di Ruang SD 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/2', 1, '2025-05-04 06:06:10', '2025-05-19 05:13:11'),
(20, 18, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan kursi di Ruang SD 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/2', 0, '2025-05-04 06:06:10', '2025-05-04 06:06:10'),
(21, 19, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan kursi di Ruang SD 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/2', 0, '2025-05-04 06:06:10', '2025-05-04 06:06:10'),
(22, 21, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan kursi di Ruang SD 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/2', 0, '2025-05-04 06:06:10', '2025-05-04 06:06:10'),
(23, 8, 'Laporan Kerusakan Diproses', 'Laporan kerusakan Anda: Kerusakan kursi sedang diproses oleh Administrator', 'info', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/2', 1, '2025-05-04 15:29:01', '2025-05-30 07:45:39'),
(24, 8, 'Laporan Kerusakan Selesai', 'Laporan kerusakan Anda: Kerusakan kursi telah selesai ditangani', 'success', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/2', 1, '2025-05-30 06:18:46', '2025-05-30 07:45:39'),
(26, 4, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/3', 0, '2025-05-31 00:21:27', '2025-05-31 00:21:27'),
(27, 5, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/3', 0, '2025-05-31 00:21:27', '2025-05-31 00:21:27'),
(28, 9, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/3', 1, '2025-05-31 00:21:27', '2025-05-31 00:21:56'),
(29, 12, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/3', 0, '2025-05-31 00:21:27', '2025-05-31 00:21:27'),
(30, 14, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/3', 0, '2025-05-31 00:21:27', '2025-05-31 00:21:27'),
(31, 15, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/3', 0, '2025-05-31 00:21:27', '2025-05-31 00:21:27'),
(32, 18, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/3', 0, '2025-05-31 00:21:27', '2025-05-31 00:21:27'),
(33, 19, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/3', 0, '2025-05-31 00:21:27', '2025-05-31 00:21:27'),
(34, 21, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/3', 0, '2025-05-31 00:21:27', '2025-05-31 00:21:27'),
(35, 8, 'Laporan Kerusakan Diproses', 'Laporan kerusakan Anda: Kerusakan sedang diproses oleh Kepala Sekolah SD', 'info', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/3', 1, '2025-05-31 00:28:47', '2025-05-31 00:35:56'),
(36, 1, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Meja Rusak di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/4', 1, '2025-05-31 00:49:29', '2025-05-31 01:12:01'),
(37, 4, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Meja Rusak di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/4', 0, '2025-05-31 00:49:29', '2025-05-31 00:49:29'),
(38, 5, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Meja Rusak di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/4', 0, '2025-05-31 00:49:29', '2025-05-31 00:49:29'),
(39, 9, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Meja Rusak di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/4', 1, '2025-05-31 00:49:29', '2025-05-31 00:49:44'),
(40, 12, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Meja Rusak di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/4', 0, '2025-05-31 00:49:29', '2025-05-31 00:49:29'),
(41, 14, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Meja Rusak di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/4', 0, '2025-05-31 00:49:29', '2025-05-31 00:49:29'),
(42, 15, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Meja Rusak di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/4', 0, '2025-05-31 00:49:29', '2025-05-31 00:49:29'),
(43, 18, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Meja Rusak di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/4', 0, '2025-05-31 00:49:29', '2025-05-31 00:49:29'),
(44, 19, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Meja Rusak di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/4', 0, '2025-05-31 00:49:29', '2025-05-31 00:49:29'),
(45, 21, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Meja Rusak di kelas 1', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/4', 0, '2025-05-31 00:49:29', '2025-05-31 00:49:29'),
(46, 8, 'Laporan Kerusakan Diproses', 'Laporan kerusakan Anda: Meja Rusak sedang diproses oleh Kepala Sekolah SD', 'info', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/4', 1, '2025-05-31 00:50:13', '2025-05-31 01:12:10'),
(47, 8, 'Laporan Kerusakan Selesai', 'Laporan kerusakan Anda: Meja Rusak telah selesai ditangani', 'success', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/4', 1, '2025-05-31 00:52:29', '2025-05-31 01:12:10'),
(48, 1, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 2', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/5', 1, '2025-05-31 01:12:58', '2025-05-31 04:26:48'),
(49, 4, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 2', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/5', 0, '2025-05-31 01:12:58', '2025-05-31 01:12:58'),
(50, 5, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 2', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/5', 0, '2025-05-31 01:12:58', '2025-05-31 01:12:58'),
(51, 9, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 2', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/5', 1, '2025-05-31 01:12:58', '2025-05-31 01:13:08'),
(52, 12, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 2', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/5', 0, '2025-05-31 01:12:58', '2025-05-31 01:12:58'),
(53, 14, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 2', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/5', 0, '2025-05-31 01:12:58', '2025-05-31 01:12:58'),
(54, 15, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 2', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/5', 0, '2025-05-31 01:12:58', '2025-05-31 01:12:58'),
(55, 18, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 2', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/5', 0, '2025-05-31 01:12:58', '2025-05-31 01:12:58'),
(56, 19, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 2', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/5', 0, '2025-05-31 01:12:58', '2025-05-31 01:12:58'),
(57, 21, 'Laporan Kerusakan Baru', 'Laporan kerusakan baru: Kerusakan di kelas 2', 'warning', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/5', 0, '2025-05-31 01:12:58', '2025-05-31 01:12:58'),
(58, 8, 'Laporan Kerusakan Diproses', 'Laporan kerusakan Anda: Kerusakan sedang diproses oleh Kepala Sekolah SD', 'info', 'http://localhost/simaspeloporh2_Jadwal%20Ok2/public/laporan-kerusakan/5', 1, '2025-05-31 01:13:23', '2025-05-31 04:27:01');

-- --------------------------------------------------------

--
-- Table structure for table `pengembangan_diri`
--

CREATE TABLE `pengembangan_diri` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `guru_id` bigint(20) UNSIGNED NOT NULL,
  `jenis_diklat` varchar(255) NOT NULL,
  `nama` varchar(255) NOT NULL,
  `penyelenggara` varchar(255) NOT NULL,
  `tingkat` varchar(255) DEFAULT NULL,
  `tahun` year(4) NOT NULL,
  `peran` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `pengembangan_diri`
--

INSERT INTO `pengembangan_diri` (`id`, `guru_id`, `jenis_diklat`, `nama`, `penyelenggara`, `tingkat`, `tahun`, `peran`, `created_at`, `updated_at`) VALUES
(3, 2, 'Diklat Fungsional', 'sasaa', 'sdaa', 'Kecamatan', '1902', 'Panitia', '2025-04-14 06:29:27', '2025-04-14 06:29:27'),
(4, 1, 'Diklat Fungsional', 'sds', 'sdf', 'Kecamatan', '1901', 'Pemateri', '2025-04-14 07:34:17', '2025-04-14 07:34:17'),
(6, 11, 'Diklat Fungsional', 'Dikelat', 'asasd www', 'Kecamatan', '2023', 'Peserta', '2025-05-30 06:35:54', '2025-05-30 06:36:31');

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `permissions`
--

INSERT INTO `permissions` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'view-gtk', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(2, 'manage-gtk', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(3, 'view-peserta-didik', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(4, 'manage-peserta-didik', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(5, 'view-adm', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(6, 'manage-adm', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(7, 'manage-adm-waka', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(8, 'manage-adm-guru', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(9, 'view-rombel', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(10, 'manage-rombel', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(11, 'view-sarpras', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(12, 'manage-sarpras', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(13, 'view-website', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(14, 'manage-website', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(15, 'manage-website-prestasi', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(16, 'manage-website-event', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(17, 'manage-website-ekstrakurikuler', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(18, 'manage-website-fasilitas', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(19, 'view-elearning', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(20, 'manage-elearning', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(21, 'view-jadwal', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(22, 'manage-jadwal', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(23, 'view-jadwal-pribadi', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(24, 'view-bk', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(25, 'manage-bk', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(26, 'view-nilai', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(27, 'manage-nilai', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(28, 'view-absensi', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(29, 'manage-absensi', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(30, 'view-spp', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(31, 'manage-spp', 'web', '2025-03-06 00:09:14', '2025-03-06 00:09:14'),
(32, 'view-adm-guru', 'web', '2025-03-07 04:40:50', '2025-03-07 04:40:50'),
(33, 'upload-adm-guru', 'web', '2025-03-07 04:40:50', '2025-03-07 04:40:50'),
(34, 'view-adm-waka', 'web', '2025-03-07 14:38:41', '2025-03-07 14:38:41'),
(35, 'upload-adm-waka', 'web', '2025-03-07 14:38:41', '2025-03-07 14:38:41'),
(36, 'view-peserta-didik-aktif', 'web', '2025-03-07 15:32:00', '2025-03-07 15:32:00'),
(37, 'view-jadwal-mengajar', 'web', '2025-03-07 15:51:42', '2025-03-07 15:51:42'),
(38, 'view-jurnal', 'web', '2025-03-13 10:31:18', '2025-03-13 10:31:18'),
(39, 'manage-jurnal', 'web', '2025-03-13 10:31:18', '2025-03-13 10:31:18'),
(40, 'approve-jurnal', 'web', '2025-03-13 10:31:18', '2025-03-13 10:31:18'),
(41, 'view-adm-kepsek', 'web', '2025-03-28 17:07:08', '2025-03-28 17:07:08'),
(42, 'upload-adm-kepsek', 'web', '2025-03-28 17:07:08', '2025-03-28 17:07:08'),
(43, 'manage-adm-kepsek', 'web', '2025-03-28 17:07:08', '2025-03-28 17:07:08'),
(44, 'approve-adm-kepsek', 'web', '2025-03-29 01:23:04', '2025-03-29 01:23:04'),
(45, 'reject-adm-kepsek', 'web', '2025-03-29 02:08:30', '2025-03-29 02:08:30'),
(46, 'view-adm-ktsp', 'web', '2025-03-30 15:12:50', '2025-03-30 15:12:50'),
(47, 'upload-adm-ktsp', 'web', '2025-03-30 15:12:50', '2025-03-30 15:12:50'),
(48, 'manage-adm-ktsp', 'web', '2025-03-30 15:12:50', '2025-03-30 15:12:50'),
(49, 'view-pengaturan', 'web', '2025-04-03 11:36:02', '2025-04-03 11:36:02'),
(50, 'manage-pengaturan', 'web', '2025-04-03 11:36:02', '2025-04-03 11:36:02'),
(51, 'manage-users', 'web', '2025-04-03 11:36:02', '2025-04-03 11:36:02'),
(52, 'manage-permissions', 'web', '2025-04-03 12:17:26', '2025-04-03 12:17:26'),
(53, 'view-tahunajaran', 'web', '2025-04-04 00:38:00', '2025-04-04 00:38:00'),
(54, 'manage-tahunajaran', 'web', '2025-04-04 00:38:19', '2025-04-04 00:38:19'),
(55, 'view-users', 'web', '2025-04-04 10:28:07', '2025-04-04 10:28:07'),
(56, 'create-users', 'web', '2025-04-04 10:28:07', '2025-04-04 10:28:07'),
(57, 'edit-users', 'web', '2025-04-04 10:28:07', '2025-04-04 10:28:07'),
(58, 'delete-users', 'web', '2025-04-04 10:28:07', '2025-04-04 10:28:07'),
(59, 'reset-user-password', 'web', '2025-04-04 10:28:07', '2025-04-04 10:28:07'),
(60, 'view-roles', 'web', '2025-04-04 10:28:07', '2025-04-04 10:28:07'),
(61, 'create-roles', 'web', '2025-04-04 10:28:07', '2025-04-04 10:28:07'),
(62, 'edit-roles', 'web', '2025-04-04 10:28:07', '2025-04-04 10:28:07'),
(63, 'delete-roles', 'web', '2025-04-04 10:28:07', '2025-04-04 10:28:07'),
(64, 'view-permissions', 'web', '2025-04-04 10:28:07', '2025-04-04 10:28:07'),
(65, 'create-permissions', 'web', '2025-04-04 10:28:07', '2025-04-04 10:28:07'),
(66, 'edit-permissions', 'web', '2025-04-04 10:28:07', '2025-04-04 10:28:07'),
(67, 'delete-permissions', 'web', '2025-04-04 10:28:07', '2025-04-04 10:28:07'),
(68, 'manage-laporan-kerja', 'web', '2025-04-15 06:42:04', '2025-04-15 06:42:04'),
(69, 'view-laporan-kerja', 'web', '2025-04-15 06:42:17', '2025-04-15 06:42:17'),
(70, 'lihat-laporan-kerja', 'web', '2025-04-15 06:49:36', '2025-04-15 06:49:36'),
(71, 'kelola-laporan-kerja', 'web', '2025-04-15 06:49:36', '2025-04-15 06:49:36'),
(72, 'create-sarpras', 'web', '2025-05-03 21:42:20', '2025-05-03 21:42:20'),
(73, 'edit-sarpras', 'web', '2025-05-03 21:42:20', '2025-05-03 21:42:20'),
(74, 'delete-sarpras', 'web', '2025-05-03 21:42:20', '2025-05-03 21:42:20'),
(75, 'lihat-laporan-kerusakan', 'web', '2025-05-03 23:20:25', '2025-05-03 23:20:25'),
(76, 'buat-laporan-kerusakan', 'web', '2025-05-03 23:20:25', '2025-05-03 23:20:25'),
(77, 'kelola-laporan-kerusakan', 'web', '2025-05-03 23:20:25', '2025-05-03 23:20:25'),
(78, 'create-rombel', 'web', '2025-05-30 08:18:58', '2025-05-30 08:18:58'),
(79, 'edit-rombel', 'web', '2025-05-30 08:18:58', '2025-05-30 08:18:58'),
(80, 'delete-rombel', 'web', '2025-05-30 08:18:58', '2025-05-30 08:18:58'),
(81, 'assign-siswa-rombel', 'web', '2025-05-30 08:18:58', '2025-05-30 08:18:58'),
(82, 'remove-siswa-rombel', 'web', '2025-05-30 08:18:58', '2025-05-30 08:18:58'),
(83, 'view-daftar-kelas', 'web', '2025-05-30 08:43:11', '2025-05-30 08:43:11'),
(84, 'create-absensi', 'web', '2025-05-30 08:59:52', '2025-05-30 08:59:52'),
(85, 'edit-absensi', 'web', '2025-05-30 08:59:52', '2025-05-30 08:59:52'),
(86, 'delete-absensi', 'web', '2025-05-30 08:59:52', '2025-05-30 08:59:52'),
(87, 'view-rekap-absensi', 'web', '2025-05-30 08:59:52', '2025-05-30 08:59:52'),
(88, 'view-rekap-admin', 'web', '2025-05-30 08:59:52', '2025-05-30 08:59:52'),
(89, 'view-unit', 'web', '2025-05-30 09:10:14', '2025-05-30 09:10:14'),
(90, 'manage-unit', 'web', '2025-05-30 09:10:14', '2025-05-30 09:10:14'),
(91, 'view-jenjang', 'web', '2025-05-30 09:10:14', '2025-05-30 09:10:14'),
(92, 'manage-jenjang', 'web', '2025-05-30 09:10:14', '2025-05-30 09:10:14'),
(93, 'view-gedung', 'web', '2025-05-30 09:10:14', '2025-05-30 09:10:14'),
(94, 'manage-gedung', 'web', '2025-05-30 09:10:14', '2025-05-30 09:10:14'),
(95, 'view-kelas', 'web', '2025-05-30 09:10:14', '2025-05-30 09:10:14'),
(96, 'manage-kelas', 'web', '2025-05-30 09:10:14', '2025-05-30 09:10:14'),
(97, 'view-mapel', 'web', '2025-05-30 09:10:14', '2025-05-30 09:10:14'),
(98, 'manage-mapel', 'web', '2025-05-30 09:10:14', '2025-05-30 09:10:14'),
(99, 'view-website-artikel', 'web', '2025-05-30 23:07:00', '2025-05-30 23:07:00'),
(100, 'manage-website-artikel', 'web', '2025-05-30 23:07:00', '2025-05-30 23:07:00'),
(101, 'view-website-event', 'web', '2025-05-30 23:07:00', '2025-05-30 23:07:00'),
(102, 'view-website-prestasi', 'web', '2025-05-30 23:07:00', '2025-05-30 23:07:00'),
(103, 'view-website-fasilitas', 'web', '2025-05-30 23:07:00', '2025-05-30 23:07:00'),
(104, 'view-website-ekstrakurikuler', 'web', '2025-05-30 23:07:00', '2025-05-30 23:07:00'),
(105, 'view-website-slide', 'web', '2025-05-30 23:07:00', '2025-05-30 23:07:00'),
(106, 'manage-website-slide', 'web', '2025-05-30 23:07:00', '2025-05-30 23:07:00'),
(107, 'view-website-halaman', 'web', '2025-05-30 23:07:00', '2025-05-30 23:07:00'),
(108, 'manage-website-halaman', 'web', '2025-05-30 23:07:00', '2025-05-30 23:07:00'),
(109, 'view-laporan-kerusakan', 'web', '2025-05-30 23:50:59', '2025-05-30 23:50:59'),
(110, 'create-laporan-kerusakan', 'web', '2025-05-30 23:50:59', '2025-05-30 23:50:59'),
(111, 'edit-laporan-kerusakan', 'web', '2025-05-30 23:50:59', '2025-05-30 23:50:59'),
(112, 'delete-laporan-kerusakan', 'web', '2025-05-30 23:50:59', '2025-05-30 23:50:59'),
(113, 'view-laporan-kerusakan-detail', 'web', '2025-05-30 23:50:59', '2025-05-30 23:50:59'),
(114, 'manage-laporan-kerusakan', 'web', '2025-05-30 23:50:59', '2025-05-30 23:50:59'),
(115, 'view-laporan-kerusakan-approval', 'web', '2025-05-30 23:50:59', '2025-05-30 23:50:59'),
(116, 'approve-laporan-kerusakan', 'web', '2025-05-30 23:50:59', '2025-05-30 23:50:59'),
(117, 'view-mengajar', 'web', '2025-05-31 12:10:23', '2025-05-31 12:10:23');

-- --------------------------------------------------------

--
-- Table structure for table `peserta_didik`
--

CREATE TABLE `peserta_didik` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nis` varchar(255) NOT NULL,
  `nisn` varchar(255) NOT NULL,
  `nama` varchar(255) NOT NULL,
  `jenis_kelamin` enum('L','P') NOT NULL,
  `tempat_lahir` varchar(255) NOT NULL,
  `tanggal_lahir` date NOT NULL,
  `alamat` text NOT NULL,
  `nama_ayah` varchar(255) DEFAULT NULL,
  `NIK_ayah` varchar(16) DEFAULT NULL,
  `nama_ibu` varchar(255) DEFAULT NULL,
  `NIK_ibu` varchar(16) DEFAULT NULL,
  `no_telp_ortu` varchar(255) DEFAULT NULL,
  `kelas_id` bigint(20) UNSIGNED DEFAULT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `status` enum('aktif','alumni','mutasi_keluar','tidak_aktif') NOT NULL DEFAULT 'aktif',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `tingkat` varchar(255) DEFAULT NULL,
  `nama_program` varchar(255) DEFAULT NULL,
  `no_reg` varchar(255) DEFAULT NULL,
  `seri_ijasah` varchar(255) DEFAULT NULL,
  `seri_skhun` varchar(255) DEFAULT NULL,
  `no_unas` varchar(255) DEFAULT NULL,
  `nik` varchar(16) DEFAULT NULL,
  `npsn` varchar(255) DEFAULT NULL,
  `sekolah_asal` varchar(255) DEFAULT NULL,
  `agama` varchar(255) DEFAULT NULL,
  `kebutuhan_khusus` varchar(255) DEFAULT NULL,
  `dusun` varchar(255) DEFAULT NULL,
  `rt` varchar(255) DEFAULT NULL,
  `rw` varchar(255) DEFAULT NULL,
  `kelurahan` varchar(255) DEFAULT NULL,
  `kode_pos` varchar(255) DEFAULT NULL,
  `kecamatan` varchar(255) DEFAULT NULL,
  `kabupaten` varchar(255) DEFAULT NULL,
  `provinsi` varchar(255) DEFAULT NULL,
  `transportasi` varchar(255) DEFAULT NULL,
  `jenis_tinggal` varchar(255) DEFAULT NULL,
  `no_telp` varchar(255) DEFAULT NULL,
  `no_handphone` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `no_kks` varchar(255) DEFAULT NULL,
  `status_kps` varchar(255) DEFAULT NULL,
  `no_ksp` varchar(255) DEFAULT NULL,
  `status_pip` varchar(255) DEFAULT NULL,
  `usulan_pip` varchar(255) DEFAULT NULL,
  `status_kip` varchar(255) DEFAULT NULL,
  `penerima_kip` varchar(255) DEFAULT NULL,
  `nama_kip` varchar(255) DEFAULT NULL,
  `keterangan_kip` varchar(255) DEFAULT NULL,
  `rek_akta` varchar(255) DEFAULT NULL,
  `lintang` varchar(255) DEFAULT NULL,
  `bujur` varchar(255) DEFAULT NULL,
  `tahun_lahir_ayah` varchar(255) DEFAULT NULL,
  `kebutuhan_khusus_ayah` varchar(255) DEFAULT NULL,
  `pekerjaan_ayah` varchar(255) DEFAULT NULL,
  `pendidikan_ayah` varchar(255) DEFAULT NULL,
  `penghasilan_ayah` varchar(255) DEFAULT NULL,
  `tahun_lahir_ibu` varchar(255) DEFAULT NULL,
  `kebutuhan_khusus_ibu` varchar(255) DEFAULT NULL,
  `pekerjaan_ibu` varchar(255) DEFAULT NULL,
  `pendidikan_ibu` varchar(255) DEFAULT NULL,
  `penghasilan_ibu` varchar(255) DEFAULT NULL,
  `nama_wali` varchar(255) DEFAULT NULL,
  `NIK_wali` varchar(16) DEFAULT NULL,
  `tahun_lahir_wali` varchar(255) DEFAULT NULL,
  `pekerjaan_wali` varchar(255) DEFAULT NULL,
  `pendidikan_wali` varchar(255) DEFAULT NULL,
  `penghasilan_wali` varchar(255) DEFAULT NULL,
  `tinggi_badan` varchar(255) DEFAULT NULL,
  `berat_badan` varchar(255) DEFAULT NULL,
  `jumlah_saudara` varchar(255) DEFAULT NULL,
  `jarak_rumah` varchar(255) DEFAULT NULL,
  `waktu_tempuh` varchar(255) DEFAULT NULL,
  `tanggal_lulus` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `peserta_didik`
--

INSERT INTO `peserta_didik` (`id`, `nis`, `nisn`, `nama`, `jenis_kelamin`, `tempat_lahir`, `tanggal_lahir`, `alamat`, `nama_ayah`, `NIK_ayah`, `nama_ibu`, `NIK_ibu`, `no_telp_ortu`, `kelas_id`, `unit_id`, `status`, `is_active`, `created_at`, `updated_at`, `tingkat`, `nama_program`, `no_reg`, `seri_ijasah`, `seri_skhun`, `no_unas`, `nik`, `npsn`, `sekolah_asal`, `agama`, `kebutuhan_khusus`, `dusun`, `rt`, `rw`, `kelurahan`, `kode_pos`, `kecamatan`, `kabupaten`, `provinsi`, `transportasi`, `jenis_tinggal`, `no_telp`, `no_handphone`, `email`, `no_kks`, `status_kps`, `no_ksp`, `status_pip`, `usulan_pip`, `status_kip`, `penerima_kip`, `nama_kip`, `keterangan_kip`, `rek_akta`, `lintang`, `bujur`, `tahun_lahir_ayah`, `kebutuhan_khusus_ayah`, `pekerjaan_ayah`, `pendidikan_ayah`, `penghasilan_ayah`, `tahun_lahir_ibu`, `kebutuhan_khusus_ibu`, `pekerjaan_ibu`, `pendidikan_ibu`, `penghasilan_ibu`, `nama_wali`, `NIK_wali`, `tahun_lahir_wali`, `pekerjaan_wali`, `pendidikan_wali`, `penghasilan_wali`, `tinggi_badan`, `berat_badan`, `jumlah_saudara`, `jarak_rumah`, `waktu_tempuh`, `tanggal_lulus`) VALUES
(81, '1332', '3179242260', 'ALBERT NATHANIEL SIREGAR', 'L', 'PEKANBARU', '2017-08-14', 'JL.AMAN GG MELUR', 'MELKY HENDRA', '1271141007780012', 'DEWI YANTI IMELDA SIBORO', '1271147107800006', NULL, 23, 2, 'aktif', 1, '2025-03-22 01:21:53', '2025-04-23 05:00:18', 'SMPS Pelopor Mandau', 'Reguler', 'REG0015', 'IJZ127', 'SKHUN127', NULL, '1271041408170001', NULL, NULL, 'Kristen', NULL, NULL, '2', '3', 'Pematang Pudu', NULL, 'Kec. Mandau', NULL, NULL, 'Mobil/bus antar jemput', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1978', 'Tidak Ada', 'Wiraswasta', 'SMP / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', '1980', 'Tidak Ada', 'Karyawan Swasta', 'D3', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(104, '1040', '3176230656', 'Siswa SD1', 'L', 'DURI', '2017-07-30', 'TRISAKTI', 'Ayah SD1', NULL, 'Ibu SD1', NULL, NULL, NULL, 2, 'mutasi_keluar', 1, '2025-04-18 10:38:58', '2025-05-30 07:25:49', 'SDS Pelopor', 'Reguler', 'Reg1', NULL, NULL, NULL, '1403093007172002', NULL, NULL, 'Kristen', NULL, NULL, '2', '2', 'Desa/Kel. Desa Buluh manis', '28784', 'Kec. Bathin Solapan', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1965', NULL, 'Wiraswasta', 'SMA / sederajat', 'Rp. 5,000,000 - Rp. 20,000,000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(105, '1044', '3175993073', 'Siswa SD2', 'P', 'DURI', '2017-02-28', 'JL MANDAU JAYA', 'Ayah SD2', NULL, 'Ibu SD2', NULL, NULL, 23, 2, 'aktif', 1, '2025-04-18 10:38:58', '2025-04-23 05:00:18', 'SDS Pelopor', 'Reguler', 'Reg2', NULL, NULL, NULL, '1403096802170002', NULL, NULL, 'Budha', NULL, NULL, '3', '2', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1989', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 5,000,000 - Rp. 20,000,000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(106, '1045', '3175240928', 'Siswa SD3', 'L', 'Duri', '2017-02-21', 'JL.Sejahtera', 'Ayah SD3', NULL, 'Ibu SD3', NULL, NULL, 23, 2, 'aktif', 1, '2025-04-18 10:38:58', '2025-04-23 05:00:18', 'SDS Pelopor', 'Reguler', 'Reg3', NULL, NULL, NULL, '1403092102171001', NULL, NULL, 'Kristen', NULL, NULL, '4', '4', 'Desa/Kel. Desa Tambusai Batang Dui', '28882', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1988', NULL, 'Lainnya', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(107, '1050', '3166303685', 'Siswa SD4', 'P', 'Duri', '2016-09-09', 'Jl. Tegal Sari Perummahan Graha Asri 2 No 23', 'Ayah SD4', NULL, 'Ibu SD4', NULL, NULL, 23, 2, 'aktif', 1, '2025-04-18 10:38:58', '2025-04-23 05:00:18', 'SDS Pelopor', 'Reguler', 'Reg4', NULL, NULL, NULL, '1403094909160006', NULL, NULL, 'Kristen', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1980', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(108, '1051', '3166303686', 'Siswa SD5', 'L', 'Duri', '2016-09-10', 'JL.Sejahtera', 'Ayah SD5', NULL, 'Ibu SD5', NULL, NULL, 23, 2, 'aktif', 1, '2025-04-18 10:38:58', '2025-04-23 05:00:18', 'SDS Pelopor', 'Reguler', 'Reg5', NULL, NULL, NULL, '1403094909164007', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1981', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(109, '1052', '3166303687', 'Siswa SD6', 'P', 'Duri', '2016-09-11', 'JL.Sejahtera', 'Ayah SD6', NULL, 'Ibu SD6', NULL, NULL, 23, 2, 'aktif', 1, '2025-04-18 10:38:58', '2025-04-23 05:00:18', 'SDS Pelopor', 'Reguler', 'Reg6', NULL, NULL, NULL, '1403094909166608', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28785', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(110, '1140', '3176230657', 'Siswa SD7', 'L', 'DURI', '2017-07-30', 'TRISAKTI', 'Ayah SD1', NULL, 'Ibu SD1', NULL, NULL, 23, 2, 'aktif', 1, '2025-04-18 16:14:57', '2025-04-23 05:00:18', 'SDS Pelopor', 'Reguler', 'Reg1', NULL, NULL, NULL, '1403093007170002', NULL, NULL, 'Kristen', NULL, NULL, '2', '2', 'Desa/Kel. Desa Buluh manis', '28784', 'Kec. Bathin Solapan', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1965', NULL, 'Wiraswasta', 'SMA / sederajat', 'Rp. 5,000,000 - Rp. 20,000,000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(111, '1141', '3176230658', 'Siswa SD8', 'P', 'DURI', '2017-02-28', 'JL MANDAU JAYA', 'Ayah SD2', NULL, 'Ibu SD2', NULL, NULL, 23, 2, 'aktif', 1, '2025-04-18 16:14:57', '2025-04-23 05:00:18', 'SDS Pelopor', 'Reguler', 'Reg2', NULL, NULL, NULL, '1403096802188002', NULL, NULL, 'Budha', NULL, NULL, '3', '2', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1989', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 5,000,000 - Rp. 20,000,000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(112, '1142', '3176230659', 'Siswa SD9', 'L', 'Duri', '2017-02-21', 'JL.Sejahtera', 'Ayah SD3', NULL, 'Ibu SD3', NULL, NULL, 23, 2, 'aktif', 1, '2025-04-18 16:14:57', '2025-04-23 05:00:18', 'SDS Pelopor', 'Reguler', 'Reg3', NULL, NULL, NULL, '1403092102170001', NULL, NULL, 'Kristen', NULL, NULL, '4', '4', 'Desa/Kel. Desa Tambusai Batang Dui', '28882', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1988', NULL, 'Lainnya', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(113, '1143', '3176230660', 'Siswa SD10', 'P', 'Duri', '2016-09-09', 'Jl. Tegal Sari Perummahan Graha Asri 2 No 23', 'Ayah SD4', NULL, 'Ibu SD4', NULL, NULL, 23, 2, 'aktif', 1, '2025-04-18 16:14:57', '2025-04-23 05:00:18', 'SDS Pelopor', 'Reguler', 'Reg4', NULL, NULL, NULL, '1403094909163006', NULL, NULL, 'Kristen', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1980', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(114, '1144', '3176230661', 'Siswa SD11', 'L', 'Duri', '2016-09-10', 'JL.Sejahtera', 'Ayah SD5', NULL, 'Ibu SD5', NULL, NULL, 23, 2, 'aktif', 1, '2025-04-18 16:14:57', '2025-04-23 05:00:18', 'SDS Pelopor', 'Reguler', 'Reg5', NULL, NULL, NULL, '1403094909165117', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1981', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(115, '1145', '3176230662', 'Siswa SD12', 'P', 'Duri', '2016-09-11', 'JL.Sejahtera', 'Ayah SD6', NULL, 'Ibu SD6', NULL, NULL, 23, 2, 'aktif', 1, '2025-04-18 16:14:57', '2025-04-23 05:00:18', 'SDS Pelopor', 'Reguler', 'Reg6', NULL, NULL, NULL, '1403094909161208', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28785', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(116, '1130', '11', 'Cahyadi', 'L', 'Duri', '2025-04-03', 'Jl kampung lalang', 'Nama Ayah', NULL, 'Nama Ayah', NULL, NULL, 23, 2, 'aktif', 1, '2025-04-18 23:16:14', '2025-04-23 05:00:18', 'SDS pelopor t1', 'Reguler', 'reg2121', NULL, NULL, NULL, '1222', NULL, NULL, 'buddha', NULL, NULL, '1', '1', 'Air Jamban', NULL, 'Mandau', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(117, '1131', '111', 'Cahyadi', 'L', 'Duri', '2025-04-03', 'Jl kampung lalang', NULL, NULL, NULL, NULL, NULL, 23, 2, 'aktif', 1, '2025-04-19 11:13:58', '2025-04-23 05:00:18', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(118, '1', '1221', 'Siswa SMP1', 'L', 'DURI', '2017-07-30', 'TRISAKTI', 'Ayah SMP1', NULL, 'Ibu SMP1', NULL, NULL, 25, 3, 'aktif', 1, '2025-04-19 12:32:44', '2025-04-23 07:22:24', 'SMP Pelopor', 'Reguler', 'Reg1', NULL, NULL, NULL, '1403093007170100', NULL, NULL, 'Kristen', NULL, NULL, '2', '2', 'Desa/Kel. Desa Buluh manis', '28784', 'Kec. Bathin Solapan', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1965', NULL, 'Wiraswasta', 'SMA / sederajat', 'Rp. 5,000,000 - Rp. 20,000,000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(119, '2', '1222', 'Siswa SMP2', 'P', 'DURI', '2017-02-28', 'JL MANDAU JAYA', 'Ayah SMP2', NULL, 'Ibu SMP2', NULL, NULL, 25, 3, 'aktif', 1, '2025-04-19 12:32:45', '2025-04-23 07:42:29', 'SMP Pelopor', 'Reguler', 'Reg2', NULL, NULL, NULL, '1403096802171000', NULL, NULL, 'Budha', NULL, NULL, '3', '2', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1989', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 5,000,000 - Rp. 20,000,000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(120, '3', '1223', 'Siswa SMP3', 'L', 'Duri', '2017-02-21', 'JL.Sejahtera', 'Ayah SMP3', NULL, 'Ibu SMP3', NULL, NULL, 19, 3, 'aktif', 1, '2025-04-19 12:32:45', '2025-04-23 15:50:24', 'SMP Pelopor', 'Reguler', 'Reg3', NULL, NULL, NULL, '1403092102171000', NULL, NULL, 'Kristen', NULL, NULL, '4', '4', 'Desa/Kel. Desa Tambusai Batang Dui', '28882', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1988', NULL, 'Lainnya', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(121, '4', '1224', 'Siswa SMP4', 'P', 'Duri', '2016-09-09', 'Jl. Tegal Sari Perummahan Graha Asri 2 No 23', 'Ayah SMP4', NULL, 'Ibu SMP4', NULL, NULL, NULL, 3, 'alumni', 0, '2025-04-19 12:32:45', '2025-04-23 15:51:38', 'SMP Pelopor', 'Reguler', 'Reg4', NULL, NULL, NULL, '1403094909161000', NULL, NULL, 'Kristen', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1980', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-04-23'),
(122, '5', '1225', 'Siswa SMP5', 'L', 'Duri', '2016-09-10', 'JL.Sejahtera', 'Ayah SMP5', NULL, 'Ibu SMP5', NULL, NULL, 14, 3, 'aktif', 1, '2025-04-19 12:32:45', '2025-04-19 22:27:25', 'SMP Pelopor', 'Reguler', 'Reg5', NULL, NULL, NULL, '1403094909161107', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1981', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(123, '6', '1226', 'Siswa SMP6', 'P', 'Duri', '2016-09-11', 'JL.Sejahtera', 'Ayah SMP6', NULL, 'Ibu SMP6', NULL, NULL, 14, 3, 'aktif', 1, '2025-04-19 12:32:45', '2025-04-25 01:56:33', 'SMP Pelopor', 'Reguler', 'Reg6', NULL, NULL, NULL, '1403094909160108', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28785', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(124, '7', '232', 'Siswa SMP7', 'L', 'dddd', '2025-04-04', 'sds', NULL, NULL, NULL, NULL, NULL, 14, 3, 'aktif', 1, '2025-04-19 12:48:01', '2025-04-19 12:48:01', NULL, NULL, NULL, NULL, NULL, NULL, '2333', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(161, '9', '223', 'Siswa SMP8', 'L', 'DURI', '2017-07-30', 'TRISAKTI', 'Ayah SMP8', NULL, 'Ibu SMP8', NULL, NULL, 14, 3, 'aktif', 1, '2025-04-19 13:09:52', '2025-04-19 13:09:52', 'SMP Pelopor', 'Reguler', 'Reg1', NULL, NULL, NULL, '2404093007172000', NULL, NULL, 'Kristen', NULL, NULL, '2', '2', 'Desa/Kel. Desa Buluh manis', '28784', 'Kec. Bathin Solapan', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1965', NULL, 'Wiraswasta', 'SMA / sederajat', 'Rp. 5,000,000 - Rp. 20,000,000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(176, '14', '227', 'Siswa SMP13', 'P', 'Duri', '2016-09-11', 'JL.Sejahtera', 'Ayah SMP13', NULL, 'Ibu SMP13', NULL, NULL, 19, 3, 'aktif', 1, '2025-04-19 13:11:43', '2025-04-25 01:55:25', 'SMP Pelopor', 'Reguler', 'Reg6', NULL, NULL, NULL, '2404094909163000', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28785', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(187, '10', '228', 'Siswa SMP9', 'P', 'DURI', '2017-02-28', 'JL MANDAU JAYA', 'Ayah SMP9', NULL, 'Ibu SMP9', NULL, NULL, NULL, 3, 'alumni', 1, '2025-04-19 13:29:58', '2025-04-23 15:36:49', 'SMP Pelopor', 'Reguler', 'Reg2', NULL, NULL, NULL, '2404096802173000', NULL, NULL, 'Budha', NULL, NULL, '3', '2', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1989', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 5,000,000 - Rp. 20,000,000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-04-23'),
(188, '11', '224', 'Siswa SMP10', 'L', 'Duri', '2017-02-21', 'JL.Sejahtera', 'Ayah SMP10', NULL, 'Ibu SMP10', NULL, NULL, 25, 3, 'aktif', 1, '2025-04-19 13:29:58', '2025-04-23 07:22:24', 'SMP Pelopor', 'Reguler', 'Reg3', NULL, NULL, NULL, '2404092102171000', NULL, NULL, 'Kristen', NULL, NULL, '4', '4', 'Desa/Kel. Desa Tambusai Batang Dui', '28882', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1988', NULL, 'Lainnya', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(189, '12', '225', 'Siswa SMP11', 'P', 'Duri', '2016-09-09', 'Jl. Tegal Sari Perummahan Graha Asri 2 No 23', 'Ayah SMP11', NULL, 'Ibu SMP11', NULL, NULL, 25, 3, 'aktif', 1, '2025-04-19 13:29:58', '2025-04-23 07:22:24', 'SMP Pelopor', 'Reguler', 'Reg4', NULL, NULL, NULL, '2404094909161000', NULL, NULL, 'Kristen', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1980', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(190, '13', '226', 'Siswa SMP12', 'L', 'Duri', '2016-09-10', 'JL.Sejahtera', 'Ayah SMP12', NULL, 'Ibu SMP12', NULL, NULL, NULL, 3, 'mutasi_keluar', 1, '2025-04-19 13:29:58', '2025-04-23 14:15:05', 'SMP Pelopor', 'Reguler', 'Reg5', NULL, NULL, NULL, '2404094909162000', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1981', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(214, '410', '4228', 'Siswa SMA1', 'P', 'DURI', '2017-02-28', 'JL MANDAU JAYA', 'Ayah SMP9', NULL, 'Ibu SMP9', NULL, NULL, 17, 4, 'aktif', 1, '2025-04-19 23:41:45', '2025-04-19 23:41:45', 'SMA Pelopor', 'Reguler', 'Reg2', NULL, NULL, NULL, '4404096802173000', NULL, NULL, 'Budha', NULL, NULL, '3', '2', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1989', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 5,000,000 - Rp. 20,000,000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(215, '411', '4224', 'Siswa SMA2', 'L', 'Duri', '2017-02-21', 'JL.Sejahtera', 'Ayah SMP10', NULL, 'Ibu SMP10', NULL, NULL, 17, 4, 'aktif', 1, '2025-04-19 23:41:45', '2025-04-19 23:41:45', 'SMA Pelopor', 'Reguler', 'Reg3', NULL, NULL, NULL, '4404092102171000', NULL, NULL, 'Kristen', NULL, NULL, '4', '4', 'Desa/Kel. Desa Tambusai Batang Dui', '28882', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1988', NULL, 'Lainnya', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(216, '412', '4225', 'Siswa SMA3', 'P', 'Duri', '2016-09-09', 'Jl. Tegal Sari Perummahan Graha Asri 2 No 23', 'Ayah SMP11', NULL, 'Ibu SMP11', NULL, NULL, 17, 4, 'aktif', 1, '2025-04-19 23:41:45', '2025-04-19 23:41:45', 'SMA Pelopor', 'Reguler', 'Reg4', NULL, NULL, NULL, '4404094909161000', NULL, NULL, 'Kristen', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1980', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(217, '413', '4226', 'Siswa SMA4', 'L', 'Duri', '2016-09-10', 'JL.Sejahtera', 'Ayah SMP12', NULL, 'Ibu SMP12', NULL, NULL, 17, 4, 'aktif', 1, '2025-04-19 23:41:45', '2025-04-19 23:41:45', 'SMA Pelopor', 'Reguler', 'Reg5', NULL, NULL, NULL, '4404094909162000', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1981', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(218, '414', '4227', 'Siswa SMA5', 'P', 'Duri', '2016-09-11', 'JL.Sejahtera', 'Ayah SMP13', NULL, 'Ibu SMP13', NULL, NULL, 17, 4, 'aktif', 1, '2025-04-19 23:41:45', '2025-04-25 01:56:22', 'SMA Pelopor', 'Reguler', 'Reg6', NULL, NULL, NULL, '4404094909163000', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28785', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(223, '510', '5228', 'Siswa TK1', 'P', 'DURI', '2017-02-28', 'JL MANDAU JAYA', 'Ayah SMP9', NULL, 'Ibu SMP9', NULL, NULL, 1, 1, 'aktif', 1, '2025-04-19 23:49:00', '2025-04-19 23:49:00', 'TK Pelopor', 'Reguler', 'Reg2', NULL, NULL, NULL, '5404096802173000', NULL, NULL, 'Budha', NULL, NULL, '3', '2', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1989', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 5,000,000 - Rp. 20,000,000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(224, '511', '5224', 'Siswa TK2', 'L', 'Duri', '2017-02-21', 'JL.Sejahtera', 'Ayah SMP10', NULL, 'Ibu SMP10', NULL, NULL, 1, 1, 'aktif', 1, '2025-04-19 23:49:00', '2025-04-19 23:49:00', 'TK Pelopor', 'Reguler', 'Reg3', NULL, NULL, NULL, '5404092102171000', NULL, NULL, 'Kristen', NULL, NULL, '4', '4', 'Desa/Kel. Desa Tambusai Batang Dui', '28882', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1988', NULL, 'Lainnya', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(225, '512', '5225', 'Siswa TK3', 'P', 'Duri', '2016-09-09', 'Jl. Tegal Sari Perummahan Graha Asri 2 No 23', 'Ayah SMP11', NULL, 'Ibu SMP11', NULL, NULL, 1, 1, 'aktif', 1, '2025-04-19 23:49:00', '2025-04-19 23:49:00', 'TK Pelopor', 'Reguler', 'Reg4', NULL, NULL, NULL, '5404094909161000', NULL, NULL, 'Kristen', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1980', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(226, '513', '5226', 'Siswa TK4', 'L', 'Duri', '2016-09-10', 'JL.Sejahtera', 'Ayah SMP12', NULL, 'Ibu SMP12', NULL, NULL, 1, 1, 'aktif', 1, '2025-04-19 23:49:00', '2025-04-19 23:49:00', 'TK Pelopor', 'Reguler', 'Reg5', NULL, NULL, NULL, '5404094909162000', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1981', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(227, '514', '5227', 'Siswa TK5', 'P', 'Duri', '2016-09-11', 'JL.Sejahtera', 'Ayah SMP13', NULL, 'Ibu SMP13', NULL, NULL, 1, 1, 'aktif', 1, '2025-04-19 23:49:00', '2025-04-25 01:55:57', 'TK Pelopor', 'Reguler', 'Reg6', NULL, NULL, NULL, '5404094909163000', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28785', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, '0', NULL, '0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(228, '710', '7228', 'Siswa SD11', 'P', 'DURI', '2017-02-28', 'JL MANDAU JAYA', 'Ayah SMP9', NULL, 'Ibu SMP9', NULL, NULL, 20, 2, 'aktif', 1, '2025-04-24 05:48:49', '2025-04-24 05:48:49', 'SDS Pelopor', 'Reguler', 'Reg2', NULL, NULL, NULL, '4704096802173000', NULL, NULL, 'Budha', NULL, NULL, '3', '2', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1989', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 5,000,000 - Rp. 20,000,000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(229, '711', '7224', 'Siswa SD12', 'L', 'Duri', '2017-02-21', 'JL.Sejahtera', 'Ayah SMP10', NULL, 'Ibu SMP10', NULL, NULL, 20, 2, 'aktif', 1, '2025-04-24 05:48:49', '2025-04-24 05:48:49', 'SDS Pelopor', 'Reguler', 'Reg3', NULL, NULL, NULL, '4704092102171000', NULL, NULL, 'Kristen', NULL, NULL, '4', '4', 'Desa/Kel. Desa Tambusai Batang Dui', '28882', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1988', NULL, 'Lainnya', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(230, '712', '7225', 'Siswa SD13', 'P', 'Duri', '2016-09-09', 'Jl. Tegal Sari Perummahan Graha Asri 2 No 23', 'Ayah SMP11', NULL, 'Ibu SMP11', NULL, NULL, 20, 2, 'aktif', 1, '2025-04-24 05:48:49', '2025-04-24 05:48:49', 'SDS Pelopor', 'Reguler', 'Reg4', NULL, NULL, NULL, '4704094909161000', NULL, NULL, 'Kristen', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1980', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(231, '713', '7226', 'Siswa SD14', 'L', 'Duri', '2016-09-10', 'JL.Sejahtera', 'Ayah SMP12', NULL, 'Ibu SMP12', NULL, NULL, 20, 2, 'aktif', 1, '2025-04-24 05:48:49', '2025-04-24 05:48:49', 'SDS Pelopor', 'Reguler', 'Reg5', NULL, NULL, NULL, '4704094909162000', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1981', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(232, '714', '7227', 'Siswa SD15', 'P', 'Duri', '2016-09-11', 'JL.Sejahtera', 'Ayah SMP13', NULL, 'Ibu SMP13', NULL, NULL, 20, 2, 'aktif', 1, '2025-04-24 05:48:49', '2025-04-24 05:48:49', 'SDS Pelopor', 'Reguler', 'Reg6', NULL, NULL, NULL, '4704094909163000', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28785', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(233, '810', '8228', 'Siswa SD11', 'P', 'DURI', '2017-02-28', 'JL MANDAU JAYA', 'Ayah SMP9', NULL, 'Ibu SMP9', NULL, NULL, 10, 2, 'aktif', 1, '2025-04-25 18:34:15', '2025-04-25 18:34:15', 'SDS Pelopor', 'Reguler', 'Reg2', NULL, NULL, NULL, '4804096802173000', NULL, NULL, 'Budha', NULL, NULL, '3', '2', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1989', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 5,000,000 - Rp. 20,000,000', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(234, '811', '8224', 'Siswa SD12', 'L', 'Duri', '2017-02-21', 'JL.Sejahtera', 'Ayah SMP10', NULL, 'Ibu SMP10', NULL, NULL, 10, 2, 'aktif', 1, '2025-04-25 18:34:15', '2025-04-25 18:34:15', 'SDS Pelopor', 'Reguler', 'Reg3', NULL, NULL, NULL, '4804092102171000', NULL, NULL, 'Kristen', NULL, NULL, '4', '4', 'Desa/Kel. Desa Tambusai Batang Dui', '28882', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1988', NULL, 'Lainnya', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(235, '812', '8225', 'Siswa SD13', 'P', 'Duri', '2016-09-09', 'Jl. Tegal Sari Perummahan Graha Asri 2 No 23', 'Ayah SMP11', NULL, 'Ibu SMP11', NULL, NULL, 10, 2, 'aktif', 1, '2025-04-25 18:34:15', '2025-04-25 18:34:15', 'SDS Pelopor', 'Reguler', 'Reg4', NULL, NULL, NULL, '4804094909161000', NULL, NULL, 'Kristen', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Sepeda motor', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1980', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(236, '813', '8226', 'Siswa SD14', 'L', 'Duri', '2016-09-10', 'JL.Sejahtera', 'Ayah SMP12', NULL, 'Ibu SMP12', NULL, NULL, 10, 2, 'aktif', 1, '2025-04-25 18:34:15', '2025-04-25 18:34:15', 'SDS Pelopor', 'Reguler', 'Reg5', NULL, NULL, NULL, '4804094909162000', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28784', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1981', NULL, 'Karyawan Swasta', 'SMA / sederajat', 'Rp. 2,000,000 - Rp. 4,999,999', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(237, '814', '8227', 'Siswa SD15', 'P', 'Duri', '2016-09-11', 'JL.Sejahtera', 'Ayah SMP13', NULL, 'Ibu SMP13', NULL, NULL, 10, 2, 'aktif', 1, '2025-04-25 18:34:15', '2025-04-25 18:34:15', 'SDS Pelopor', 'Reguler', 'Reg6', NULL, NULL, NULL, '4804094909163000', NULL, NULL, 'Budha', NULL, NULL, '3', '20', 'Desa/Kel. Air Jamban', '28785', 'Kec. Mandau', NULL, NULL, 'Mobil pribadi', 'Bersama orang tua', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `riwayat_kelas`
--

CREATE TABLE `riwayat_kelas` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `siswa_id` bigint(20) UNSIGNED NOT NULL,
  `nama_pd` varchar(255) DEFAULT NULL,
  `kelas_lama_id` bigint(20) UNSIGNED DEFAULT NULL,
  `kelas_baru_id` bigint(20) UNSIGNED DEFAULT NULL,
  `tahun_ajaran` varchar(255) NOT NULL,
  `jenis_perpindahan` enum('pindah_kelas','kenaikan_kelas','kelulusan','mutasi_keluar') DEFAULT NULL,
  `tanggal_pindah` date NOT NULL,
  `alasan` varchar(255) DEFAULT NULL,
  `sekolah_tujuan` varchar(255) DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `riwayat_kelas`
--

INSERT INTO `riwayat_kelas` (`id`, `siswa_id`, `nama_pd`, `kelas_lama_id`, `kelas_baru_id`, `tahun_ajaran`, `jenis_perpindahan`, `tanggal_pindah`, `alasan`, `sekolah_tujuan`, `created_by`, `created_at`, `updated_at`) VALUES
(12, 81, 'ALBERT NATHANIEL SIREGAR', 12, 15, '2024/2025', 'pindah_kelas', '2025-04-18', 'Perpindahan kelas individu', NULL, 1, '2025-04-18 16:19:24', '2025-04-24 05:17:44'),
(13, 109, 'Siswa SD6', 12, 15, '2024/2025', 'pindah_kelas', '2025-04-19', 'tes pindah dari 1e ke 2A', NULL, 9, '2025-04-19 11:00:37', '2025-04-24 05:17:44'),
(14, 117, 'Cahyadi', NULL, 15, '2024/2025', 'pindah_kelas', '2025-04-19', 'Pendaftaran siswa baru', NULL, 9, '2025-04-19 11:13:58', '2025-04-24 05:17:44'),
(15, 124, 'Siswa SMP7', NULL, 14, '2024/2025', 'pindah_kelas', '2025-04-19', 'Pendaftaran siswa baru', NULL, 15, '2025-04-19 12:48:01', '2025-04-24 05:17:44'),
(16, 187, 'Siswa SMP9', NULL, 14, '2024/2025', 'pindah_kelas', '2025-04-19', 'Pendaftaran siswa baru melalui impor', NULL, 15, '2025-04-19 13:29:58', '2025-04-24 05:17:44'),
(17, 188, 'Siswa SMP10', NULL, 14, '2024/2025', 'pindah_kelas', '2025-04-19', 'Pendaftaran siswa baru melalui impor', NULL, 15, '2025-04-19 13:29:58', '2025-04-24 05:17:44'),
(18, 189, 'Siswa SMP11', NULL, 14, '2024/2025', 'pindah_kelas', '2025-04-19', 'Pendaftaran siswa baru melalui impor', NULL, 15, '2025-04-19 13:29:58', '2025-04-24 05:17:44'),
(19, 190, 'Siswa SMP12', NULL, 14, '2024/2025', 'pindah_kelas', '2025-04-19', 'Pendaftaran siswa baru melalui impor', NULL, 15, '2025-04-19 13:29:58', '2025-04-24 05:17:44'),
(20, 176, 'Siswa SMP13', NULL, 14, '2024/2025', 'pindah_kelas', '2025-04-19', 'Pendaftaran siswa baru melalui impor', NULL, 15, '2025-04-19 13:29:58', '2025-04-24 05:17:44'),
(21, 214, 'Siswa SMA1', NULL, 17, '2024/2025', 'pindah_kelas', '2025-04-20', 'Pendaftaran siswa baru melalui impor', NULL, 18, '2025-04-19 23:41:45', '2025-04-24 05:17:44'),
(22, 215, 'Siswa SMA2', NULL, 17, '2024/2025', 'pindah_kelas', '2025-04-20', 'Pendaftaran siswa baru melalui impor', NULL, 18, '2025-04-19 23:41:45', '2025-04-24 05:17:44'),
(23, 216, 'Siswa SMA3', NULL, 17, '2024/2025', 'pindah_kelas', '2025-04-20', 'Pendaftaran siswa baru melalui impor', NULL, 18, '2025-04-19 23:41:45', '2025-04-24 05:17:44'),
(24, 217, 'Siswa SMA4', NULL, 17, '2024/2025', 'pindah_kelas', '2025-04-20', 'Pendaftaran siswa baru melalui impor', NULL, 18, '2025-04-19 23:41:45', '2025-04-24 05:17:44'),
(25, 218, 'Siswa SMA5', NULL, 17, '2024/2025', 'pindah_kelas', '2025-04-20', 'Pendaftaran siswa baru melalui impor', NULL, 18, '2025-04-19 23:41:45', '2025-04-24 05:17:44'),
(26, 223, 'Siswa TK1', NULL, 1, '2024/2025', 'pindah_kelas', '2025-04-20', 'Pendaftaran siswa baru melalui impor', NULL, 21, '2025-04-19 23:49:00', '2025-04-24 05:17:44'),
(27, 224, 'Siswa TK2', NULL, 1, '2024/2025', 'pindah_kelas', '2025-04-20', 'Pendaftaran siswa baru melalui impor', NULL, 21, '2025-04-19 23:49:00', '2025-04-24 05:17:44'),
(28, 225, 'Siswa TK3', NULL, 1, '2024/2025', 'pindah_kelas', '2025-04-20', 'Pendaftaran siswa baru melalui impor', NULL, 21, '2025-04-19 23:49:00', '2025-04-24 05:17:44'),
(29, 226, 'Siswa TK4', NULL, 1, '2024/2025', 'pindah_kelas', '2025-04-20', 'Pendaftaran siswa baru melalui impor', NULL, 21, '2025-04-19 23:49:00', '2025-04-24 05:17:44'),
(30, 227, 'Siswa TK5', NULL, 1, '2024/2025', 'pindah_kelas', '2025-04-20', 'Pendaftaran siswa baru melalui impor', NULL, 21, '2025-04-19 23:49:00', '2025-04-24 05:17:44'),
(31, 104, 'Siswa SD1', 12, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(32, 105, 'Siswa SD2', 12, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(33, 106, 'Siswa SD3', 12, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(34, 107, 'Siswa SD4', 12, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(35, 108, 'Siswa SD5', 12, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(36, 81, 'ALBERT NATHANIEL SIREGAR', 15, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(37, 109, 'Siswa SD6', 15, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(38, 116, 'Cahyadi', 15, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(39, 117, 'Cahyadi', 15, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(40, 110, 'Siswa SD7', 13, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(41, 111, 'Siswa SD8', 13, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(42, 112, 'Siswa SD9', 13, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(43, 113, 'Siswa SD10', 13, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(44, 114, 'Siswa SD11', 13, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(45, 115, 'Siswa SD12', 13, 23, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 9, '2025-04-23 05:00:18', '2025-04-24 05:17:44'),
(46, 118, 'Siswa SMP1', 14, 19, '2024/2025', 'pindah_kelas', '2025-04-23', 'pindah', NULL, 15, '2025-04-23 07:18:45', '2025-04-24 05:17:44'),
(47, 188, 'Siswa SMP10', 14, 19, '2024/2025', 'pindah_kelas', '2025-04-23', 'pindah', NULL, 15, '2025-04-23 07:18:45', '2025-04-24 05:17:44'),
(48, 189, 'Siswa SMP11', 14, 19, '2024/2025', 'pindah_kelas', '2025-04-23', 'pindah', NULL, 15, '2025-04-23 07:18:45', '2025-04-24 05:17:44'),
(49, 190, 'Siswa SMP12', 14, 19, '2024/2025', 'pindah_kelas', '2025-04-23', 'pindah', NULL, 15, '2025-04-23 07:18:45', '2025-04-24 05:17:44'),
(50, 118, 'Siswa SMP1', 19, 25, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 15, '2025-04-23 07:22:24', '2025-04-24 05:17:44'),
(51, 188, 'Siswa SMP10', 19, 25, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 15, '2025-04-23 07:22:24', '2025-04-24 05:17:44'),
(52, 189, 'Siswa SMP11', 19, 25, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 15, '2025-04-23 07:22:24', '2025-04-24 05:17:44'),
(53, 190, 'Siswa SMP12', 19, 25, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 15, '2025-04-23 07:22:24', '2025-04-24 05:17:44'),
(54, 176, 'Siswa SMP13', 14, 19, '2024/2025', 'pindah_kelas', '2025-04-23', 'pindah ke 2', NULL, 15, '2025-04-23 07:29:31', '2025-04-24 05:17:44'),
(55, 119, 'Siswa SMP2', 14, 25, '2025/2026', 'kenaikan_kelas', '2025-04-23', 'Kenaikan kelas terpilih', NULL, 15, '2025-04-23 07:42:29', '2025-04-24 05:17:44'),
(56, 187, 'Siswa SMP9', 14, 21, '2024/2025', 'pindah_kelas', '2025-04-23', 'Perpindahan kelas individu', NULL, 15, '2025-04-23 14:00:20', '2025-04-24 05:17:44'),
(57, 190, 'Siswa SMP12', 25, NULL, '2024/2025', 'mutasi_keluar', '2025-04-23', 'tes', 'SMP Sekolah Tujuan', 15, '2025-04-23 14:15:05', '2025-04-24 05:17:44'),
(58, 120, 'Siswa SMP3', 14, 19, '2024/2025', 'pindah_kelas', '2025-04-23', 'Perpindahan kelas individu', NULL, 15, '2025-04-23 15:50:24', '2025-04-24 05:17:44'),
(59, 121, 'Siswa SMP4', 14, 21, '2024/2025', 'pindah_kelas', '2025-04-23', 'Perpindahan kelas individu', NULL, 15, '2025-04-23 15:51:14', '2025-04-24 05:17:44'),
(60, 121, 'Siswa SMP4', 21, NULL, '2024/2025', 'kelulusan', '2025-04-23', 'Kelulusan', NULL, 15, '2025-04-23 15:51:38', '2025-04-24 05:17:44'),
(61, 228, 'Siswa SD11', NULL, 20, '2024/2025', 'pindah_kelas', '2025-04-24', 'Pendaftaran siswa baru melalui impor', NULL, 9, '2025-04-24 05:48:49', '2025-04-24 05:48:49'),
(62, 229, 'Siswa SD12', NULL, 20, '2024/2025', 'pindah_kelas', '2025-04-24', 'Pendaftaran siswa baru melalui impor', NULL, 9, '2025-04-24 05:48:49', '2025-04-24 05:48:49'),
(63, 230, 'Siswa SD13', NULL, 20, '2024/2025', 'pindah_kelas', '2025-04-24', 'Pendaftaran siswa baru melalui impor', NULL, 9, '2025-04-24 05:48:49', '2025-04-24 05:48:49'),
(64, 231, 'Siswa SD14', NULL, 20, '2024/2025', 'pindah_kelas', '2025-04-24', 'Pendaftaran siswa baru melalui impor', NULL, 9, '2025-04-24 05:48:49', '2025-04-24 05:48:49'),
(65, 232, 'Siswa SD15', NULL, 20, '2024/2025', 'pindah_kelas', '2025-04-24', 'Pendaftaran siswa baru melalui impor', NULL, 9, '2025-04-24 05:48:49', '2025-04-24 05:48:49'),
(66, 233, 'Siswa SD11', NULL, 10, '2024/2025', 'pindah_kelas', '2025-04-26', 'Pendaftaran siswa baru melalui impor', NULL, 9, '2025-04-25 18:34:15', '2025-04-25 18:34:15'),
(67, 234, 'Siswa SD12', NULL, 10, '2024/2025', 'pindah_kelas', '2025-04-26', 'Pendaftaran siswa baru melalui impor', NULL, 9, '2025-04-25 18:34:15', '2025-04-25 18:34:15'),
(68, 235, 'Siswa SD13', NULL, 10, '2024/2025', 'pindah_kelas', '2025-04-26', 'Pendaftaran siswa baru melalui impor', NULL, 9, '2025-04-25 18:34:15', '2025-04-25 18:34:15'),
(69, 236, 'Siswa SD14', NULL, 10, '2024/2025', 'pindah_kelas', '2025-04-26', 'Pendaftaran siswa baru melalui impor', NULL, 9, '2025-04-25 18:34:15', '2025-04-25 18:34:15'),
(70, 237, 'Siswa SD15', NULL, 10, '2024/2025', 'pindah_kelas', '2025-04-26', 'Pendaftaran siswa baru melalui impor', NULL, 9, '2025-04-25 18:34:15', '2025-04-25 18:34:15'),
(71, 104, 'Siswa SD1', 23, NULL, '2024/2025', 'mutasi_keluar', '2025-05-30', 'Permintaan Orangtua', 'SDS selanjutnya', 1, '2025-05-30 07:25:50', '2025-05-30 07:25:50');

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'Administrator', 'web', '2025-03-05 22:48:08', '2025-03-05 22:48:08'),
(2, 'Guru', 'web', '2025-03-05 22:48:08', '2025-03-05 22:48:08'),
(3, 'Kepala Sekolah', 'web', '2025-03-05 22:48:08', '2025-03-05 22:48:08'),
(4, 'Waka Kurikulum', 'web', '2025-03-05 22:48:08', '2025-03-05 22:48:08'),
(5, 'Yayasan', 'web', '2025-03-05 22:50:52', '2025-03-05 22:50:52'),
(6, 'Waka Kesiswaan', 'web', '2025-03-05 22:50:52', '2025-03-05 22:50:52'),
(7, 'Waka Sarpras', 'web', '2025-03-05 22:50:52', '2025-03-05 22:50:52'),
(8, 'Pengawas', 'web', '2025-03-05 22:50:52', '2025-03-05 22:50:52'),
(9, 'Bimbingan Konseling', 'web', '2025-03-05 22:50:52', '2025-03-05 22:50:52'),
(10, 'Tata Usaha', 'web', '2025-03-05 22:50:52', '2025-03-05 22:50:52'),
(11, 'Pustakawan', 'web', '2025-03-05 22:50:52', '2025-03-05 22:50:52'),
(12, 'Wali Kelas', 'web', '2025-05-30 08:59:52', '2025-05-30 08:59:52');

-- --------------------------------------------------------

--
-- Table structure for table `role_has_permissions`
--

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `role_has_permissions`
--

INSERT INTO `role_has_permissions` (`permission_id`, `role_id`) VALUES
(1, 1),
(1, 3),
(1, 5),
(1, 8),
(2, 1),
(2, 3),
(2, 4),
(2, 5),
(3, 1),
(3, 3),
(3, 4),
(3, 5),
(3, 8),
(4, 1),
(4, 3),
(4, 4),
(4, 5),
(4, 6),
(5, 1),
(5, 2),
(5, 3),
(5, 4),
(5, 5),
(5, 6),
(5, 8),
(6, 1),
(6, 3),
(6, 5),
(7, 1),
(7, 3),
(7, 4),
(7, 5),
(7, 6),
(8, 1),
(8, 2),
(8, 3),
(8, 5),
(9, 1),
(9, 2),
(9, 3),
(9, 4),
(9, 5),
(9, 6),
(9, 8),
(9, 10),
(10, 1),
(10, 3),
(10, 4),
(10, 5),
(10, 6),
(11, 1),
(11, 2),
(11, 3),
(11, 5),
(11, 7),
(11, 8),
(11, 10),
(12, 1),
(12, 3),
(12, 5),
(12, 7),
(13, 1),
(13, 3),
(13, 4),
(13, 5),
(13, 6),
(13, 7),
(13, 10),
(14, 1),
(14, 3),
(14, 4),
(14, 5),
(15, 1),
(15, 4),
(15, 5),
(15, 6),
(16, 1),
(16, 4),
(16, 5),
(16, 6),
(16, 10),
(17, 1),
(17, 4),
(17, 5),
(17, 6),
(18, 1),
(18, 4),
(18, 5),
(18, 7),
(19, 1),
(19, 5),
(20, 1),
(20, 3),
(20, 4),
(20, 5),
(21, 1),
(21, 3),
(21, 5),
(21, 6),
(21, 8),
(22, 1),
(22, 3),
(22, 4),
(22, 5),
(23, 1),
(23, 2),
(23, 5),
(24, 1),
(24, 3),
(24, 4),
(24, 5),
(24, 6),
(24, 8),
(25, 1),
(25, 5),
(25, 9),
(26, 1),
(26, 3),
(26, 4),
(26, 5),
(26, 8),
(27, 1),
(27, 2),
(27, 5),
(28, 1),
(28, 2),
(28, 3),
(28, 4),
(28, 5),
(28, 6),
(28, 8),
(28, 10),
(28, 12),
(29, 1),
(29, 2),
(29, 4),
(29, 5),
(29, 10),
(30, 1),
(30, 5),
(31, 1),
(31, 5),
(31, 10),
(32, 1),
(32, 2),
(32, 3),
(33, 1),
(33, 2),
(34, 1),
(34, 3),
(34, 4),
(34, 5),
(34, 6),
(35, 1),
(35, 3),
(35, 4),
(35, 5),
(35, 6),
(36, 1),
(36, 3),
(36, 4),
(36, 5),
(37, 1),
(37, 2),
(37, 5),
(38, 1),
(38, 2),
(38, 3),
(38, 4),
(38, 5),
(38, 8),
(38, 9),
(38, 10),
(38, 11),
(39, 1),
(39, 2),
(39, 3),
(39, 4),
(39, 5),
(39, 8),
(39, 9),
(39, 10),
(39, 11),
(40, 1),
(40, 3),
(40, 4),
(40, 5),
(41, 1),
(41, 3),
(41, 8),
(42, 1),
(42, 3),
(43, 1),
(43, 3),
(43, 5),
(44, 1),
(44, 5),
(44, 8),
(45, 1),
(46, 1),
(46, 3),
(47, 1),
(48, 1),
(48, 3),
(49, 1),
(49, 3),
(49, 4),
(49, 5),
(49, 10),
(50, 1),
(50, 5),
(51, 1),
(51, 5),
(52, 1),
(52, 5),
(53, 1),
(54, 1),
(55, 1),
(55, 5),
(56, 1),
(56, 5),
(57, 1),
(57, 5),
(58, 1),
(58, 5),
(59, 1),
(59, 5),
(60, 1),
(60, 5),
(61, 1),
(61, 5),
(62, 1),
(62, 5),
(63, 1),
(63, 5),
(64, 1),
(64, 5),
(65, 1),
(65, 5),
(66, 1),
(66, 5),
(67, 1),
(67, 5),
(68, 1),
(68, 3),
(69, 1),
(69, 3),
(70, 1),
(70, 3),
(70, 4),
(71, 1),
(72, 1),
(72, 7),
(73, 1),
(73, 3),
(73, 7),
(74, 1),
(74, 7),
(75, 1),
(75, 2),
(75, 3),
(75, 7),
(76, 1),
(76, 2),
(77, 1),
(77, 3),
(77, 7),
(78, 1),
(78, 4),
(78, 10),
(79, 1),
(79, 4),
(79, 10),
(80, 1),
(81, 1),
(81, 3),
(81, 4),
(81, 6),
(81, 10),
(82, 1),
(82, 3),
(82, 4),
(82, 6),
(82, 10),
(83, 1),
(83, 2),
(83, 3),
(83, 4),
(83, 6),
(83, 10),
(84, 1),
(84, 2),
(84, 10),
(84, 12),
(85, 1),
(85, 2),
(85, 10),
(85, 12),
(86, 1),
(87, 1),
(87, 2),
(87, 4),
(87, 6),
(87, 10),
(87, 12),
(88, 1),
(88, 3),
(88, 4),
(88, 6),
(88, 10),
(89, 1),
(89, 3),
(89, 4),
(89, 10),
(90, 1),
(91, 1),
(91, 3),
(91, 4),
(91, 10),
(92, 1),
(93, 1),
(93, 3),
(93, 4),
(93, 10),
(94, 1),
(94, 10),
(95, 1),
(95, 3),
(95, 4),
(95, 10),
(96, 1),
(96, 4),
(96, 10),
(97, 1),
(97, 3),
(97, 4),
(97, 10),
(98, 1),
(98, 4),
(99, 1),
(99, 3),
(99, 4),
(99, 10),
(100, 1),
(100, 4),
(100, 10),
(101, 1),
(101, 3),
(101, 4),
(101, 10),
(102, 1),
(102, 3),
(102, 4),
(102, 6),
(103, 1),
(103, 3),
(103, 4),
(103, 7),
(104, 1),
(104, 3),
(104, 4),
(104, 6),
(105, 1),
(105, 3),
(105, 4),
(106, 1),
(106, 4),
(107, 1),
(107, 3),
(107, 4),
(108, 1),
(108, 4),
(109, 1),
(109, 2),
(109, 3),
(109, 7),
(109, 10),
(110, 1),
(110, 2),
(110, 10),
(111, 1),
(111, 7),
(112, 1),
(113, 1),
(114, 1),
(114, 7),
(115, 1),
(115, 7),
(116, 1),
(116, 3),
(116, 7),
(117, 2);

-- --------------------------------------------------------

--
-- Table structure for table `rombel_ekstrakurikulers`
--

CREATE TABLE `rombel_ekstrakurikulers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nama_rombel` varchar(255) NOT NULL,
  `ekstrakurikuler` varchar(255) NOT NULL,
  `tahun_ajaran` varchar(255) NOT NULL,
  `pembina` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `rombel_ekstrakurikulers`
--

INSERT INTO `rombel_ekstrakurikulers` (`id`, `nama_rombel`, `ekstrakurikuler`, `tahun_ajaran`, `pembina`, `created_at`, `updated_at`) VALUES
(1, 'Komputer', 'Leb', '2024/2025', 'Anton', '2025-04-28 07:11:23', '2025-04-28 07:11:23'),
(2, 'Menggambar', 'Menggambar 1', '2024/2025', 'Andi', '2025-05-03 11:16:19', '2025-05-03 11:16:19');

-- --------------------------------------------------------

--
-- Table structure for table `sarana`
--

CREATE TABLE `sarana` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nama_sarana` varchar(255) NOT NULL,
  `no_barang` varchar(255) DEFAULT NULL,
  `jenis` varchar(255) NOT NULL,
  `jumlah` int(11) NOT NULL DEFAULT 1,
  `kondisi` enum('Baik','Rusak Ringan','Rusak Berat') NOT NULL DEFAULT 'Baik',
  `tahun_pengadaan` year(4) DEFAULT NULL,
  `keterangan` text DEFAULT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `gedung_id` bigint(20) UNSIGNED DEFAULT NULL,
  `foto` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sarana`
--

INSERT INTO `sarana` (`id`, `nama_sarana`, `no_barang`, `jenis`, `jumlah`, `kondisi`, `tahun_pengadaan`, `keterangan`, `unit_id`, `gedung_id`, `foto`, `created_at`, `updated_at`) VALUES
(1, 'lemari', NULL, 'Meubelair', 1, 'Baik', '2024', 'bagussss', 1, NULL, 'sarana/Ion789xkvBshetuDmjUgXo8teAx3ZlljgGYEHWDJ.jpg', '2025-03-27 05:12:11', '2025-03-27 05:12:11'),
(2, 'Lemari', NULL, 'Meubelair', 1, 'Baik', '2023', 'Diperoleh dari dana Bos', 1, NULL, NULL, '2025-04-16 00:41:35', '2025-04-16 00:41:35'),
(3, 'Kursi', '02/8992/mj', 'Meja', 1, 'Baik', '2025', 'Bagus', 1, 1, NULL, '2025-05-02 06:11:15', '2025-05-02 06:11:15'),
(4, 'Proyektor', 'no-wjjd', 'Proyektor', 1, 'Baik', '2024', 'focus ss', 1, 1, 'sarana/mdhMYpDEzivlz4WBbvmZFpcozo5OUoSarJYuAthq.png', '2025-05-02 22:43:10', '2025-05-03 12:53:22');

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('7EbVVGiXRlYdm2iDhGCBv1HTzvIU5k7RVwHjQSyH', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiRWN1MlhDUE52dldTNnRFV3lIdTY3SHIwRzNVakFOVGJRYUlXQm5VWSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Njg6Imh0dHA6Ly9sb2NhbGhvc3Qvc2ltYXNwZWxvcG9yaDJfSmFkd2FsJTIwT2syL3B1YmxpYy9ub3RpZmlrYXNpL2NvdW50Ijt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTt9', 1749070970),
('z1zUhyx6If1CWQYwhGjhdvjCBZ5r2IiiRLJuj354', 8, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiNnJuajRibTJtMEI2cmpQV2cwRWUwVjBnQ0diU3E4a210R2x0Z3pHVCI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjY4OiJodHRwOi8vbG9jYWxob3N0L3NpbWFzcGVsb3BvcmgyX0phZHdhbCUyME9rMi9wdWJsaWMvbm90aWZpa2FzaS9jb3VudCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjg7fQ==', 1749071002);

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `key` varchar(255) NOT NULL,
  `value` text DEFAULT NULL,
  `type` varchar(255) NOT NULL DEFAULT 'text',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `key`, `value`, `type`, `created_at`, `updated_at`) VALUES
(1, 'sambutan_kepsek', 'Selamat datang di website sekolah kami. Kami berkomitmen untuk memberikan pendidikan terbaik...', 'textarea', '2025-03-08 05:36:20', '2025-03-08 05:36:20'),
(2, 'profil_sekolah', 'Profil lengkap sekolah kami...', 'richtext', '2025-03-08 05:36:20', '2025-03-08 05:36:20'),
(3, 'visi_misi', 'Visi dan Misi sekolah kami...', 'richtext', '2025-03-08 05:36:20', '2025-03-08 05:36:20'),
(4, 'sejarah', 'Sejarah perkembangan sekolah kami...', 'richtext', '2025-03-08 05:36:20', '2025-03-08 05:36:20');

-- --------------------------------------------------------

--
-- Table structure for table `slides`
--

CREATE TABLE `slides` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `image` varchar(255) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `slides`
--

INSERT INTO `slides` (`id`, `title`, `description`, `image`, `status`, `order`, `created_at`, `updated_at`) VALUES
(9, 'Selamat Datang', 'ucapan selamat datang', 'slides/67cd718343c1f.jpg', 1, 1, '2025-03-09 03:46:27', '2025-03-09 03:46:27'),
(10, 'slide2', 'untu yang ke 2', 'slides/67cd734f85ada.jpg', 1, 1, '2025-03-09 03:54:07', '2025-03-09 03:54:07'),
(11, 'tes2', 'tes2', 'slides/zIUGktFfrDj9uFtBoD4XfytHYh9G95yQzVY9hKWX.jpg', 1, 3, '2025-03-12 04:31:03', '2025-03-12 04:41:20');

-- --------------------------------------------------------

--
-- Table structure for table `spps`
--

CREATE TABLE `spps` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `va_number` varchar(255) NOT NULL,
  `nama` varchar(255) NOT NULL,
  `tanggal_lahir` date NOT NULL,
  `spp_bulan_ini` decimal(12,2) NOT NULL DEFAULT 0.00,
  `tunggakan` decimal(12,2) NOT NULL DEFAULT 0.00,
  `buku` decimal(12,2) NOT NULL DEFAULT 0.00,
  `uang_program` decimal(12,2) NOT NULL DEFAULT 0.00,
  `les` decimal(12,2) NOT NULL DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `spps`
--

INSERT INTO `spps` (`id`, `va_number`, `nama`, `tanggal_lahir`, `spp_bulan_ini`, `tunggakan`, `buku`, `uang_program`, `les`, `created_at`, `updated_at`) VALUES
(1, '12341234', 'Siswa1', '2025-04-03', 100000.00, 0.00, 300000.00, 35000.00, 10000.00, '2025-04-11 01:54:40', '2025-04-11 22:37:46'),
(2, '12341235', 'Siswa2', '2025-04-04', 100000.00, 0.00, 0.00, 0.00, 0.00, '2025-04-11 01:54:40', '2025-04-11 22:37:46'),
(3, '12341236', 'Siswa3', '2025-04-05', 0.00, 0.00, 0.00, 0.00, 0.00, '2025-04-11 01:54:40', '2025-04-11 22:37:46'),
(10, '12341237', 'Siswa4', '2025-04-06', 100000.00, 0.00, 0.00, 0.00, 0.00, '2025-04-11 22:36:04', '2025-04-11 22:37:46');

-- --------------------------------------------------------

--
-- Table structure for table `spp_uploads`
--

CREATE TABLE `spp_uploads` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `keterangan` varchar(255) DEFAULT NULL,
  `uploaded_by` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `spp_uploads`
--

INSERT INTO `spp_uploads` (`id`, `file_name`, `original_name`, `keterangan`, `uploaded_by`, `created_at`, `updated_at`) VALUES
(1, '1744334518_Spp.xlsx', 'Spp.xlsx', 'Bulan April', 1, '2025-04-11 01:21:58', '2025-04-11 01:21:58'),
(2, '1744336479_Spp.xlsx', 'Spp.xlsx', 'Bulan April1', 1, '2025-04-11 01:54:39', '2025-04-11 01:54:39'),
(3, '1744337704_Spp.xlsx', 'Spp.xlsx', 'SPP April2', 1, '2025-04-11 02:15:04', '2025-04-11 02:15:04'),
(4, '1744339515_Spp2.xlsx', 'Spp2.xlsx', 'SPP April3', 1, '2025-04-11 02:45:15', '2025-04-11 02:45:15'),
(5, '1744339831_Spp2.xlsx', 'Spp2.xlsx', 'SPP April3-1', 1, '2025-04-11 02:50:31', '2025-04-11 02:50:31'),
(6, '1744340087_Spp2.xlsx', 'Spp2.xlsx', 'updte', 1, '2025-04-11 02:54:47', '2025-04-11 02:54:47'),
(7, '1744410964_Spp3.xlsx', 'Spp3.xlsx', 'updte2', 1, '2025-04-11 22:36:04', '2025-04-11 22:36:04'),
(8, '1744411066_Spp3.xlsx', 'Spp3.xlsx', 'updte3', 1, '2025-04-11 22:37:46', '2025-04-11 22:37:46');

-- --------------------------------------------------------

--
-- Table structure for table `tahun_ajaran`
--

CREATE TABLE `tahun_ajaran` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `nama` varchar(255) NOT NULL,
  `tanggal_mulai` date DEFAULT NULL,
  `tanggal_selesai` date DEFAULT NULL,
  `aktif` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `tahun_ajaran`
--

INSERT INTO `tahun_ajaran` (`id`, `nama`, `tanggal_mulai`, `tanggal_selesai`, `aktif`, `created_at`, `updated_at`) VALUES
(1, '2023/2024', '2023-07-01', '2024-06-30', 0, '2025-03-22 02:15:53', '2025-03-24 19:27:13'),
(2, '2024/2025', '2024-07-01', '2025-12-30', 1, '2025-03-24 19:26:37', '2025-05-20 22:11:47'),
(3, '2025/2026', '2025-01-01', '2025-06-30', 0, '2025-03-25 08:34:57', '2025-05-20 22:11:47'),
(4, '2026/2027', '2026-05-11', '2027-05-12', 0, '2025-04-23 04:36:58', '2025-05-20 22:10:24');

-- --------------------------------------------------------

--
-- Table structure for table `units`
--

CREATE TABLE `units` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `jenjang_id` varchar(255) NOT NULL,
  `nama_unit` varchar(255) NOT NULL,
  `nss` varchar(255) DEFAULT NULL,
  `npsn` varchar(255) DEFAULT NULL,
  `no_telepon` varchar(255) DEFAULT NULL,
  `alamat` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `units`
--

INSERT INTO `units` (`id`, `jenjang_id`, `nama_unit`, `nss`, `npsn`, `no_telepon`, `alamat`, `created_at`, `updated_at`) VALUES
(1, 'PG', 'TK Pelopor', '123445', '45677', '086555555', 'Jl. Kampung Lalang', '2025-03-05 22:48:08', '2025-04-18 10:51:52'),
(2, 'SD', 'SDS Pelopor Duri', '12345678', '345667', '0875543', 'Jl. Kampung Lalang', '2025-03-05 22:48:08', '2025-04-18 10:53:37'),
(3, 'SMP', 'SMP Pelopor Mandau', '2345', '1234', '4321', 'Jl. Kayangan', '2025-03-09 14:09:38', '2025-04-18 10:52:40'),
(4, 'SMA', 'SMA Pelopor Mandau', '23456', '23456', '123456', 'Jl. Kampung Lalang', '2025-03-10 23:03:18', '2025-03-10 23:03:18');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `role` varchar(255) DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `last_activity` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `password`, `phone`, `unit_id`, `role`, `photo`, `email_verified_at`, `remember_token`, `created_at`, `updated_at`, `last_activity`) VALUES
(1, 'Administrator', '<EMAIL>', '$2y$12$df.GKuFcNUCzFXA2cQuXkebKEJghwA7Wlb7M6XMUpt913vKKb.ucy', '081234567890', 2, 'Administrator', NULL, NULL, NULL, '2025-03-05 22:48:09', '2025-06-04 21:02:50', '2025-06-04 21:02:50'),
(2, 'Waka Kurikulum SD', '<EMAIL>', '$2y$12$gVZPz2HnWl6MEqie8FZ7..P2MF.6Ux.vYZ9JY5rv5ItM9nUIhp0Am', '081234567891', 2, 'Waka Kurikulum', '1746753578.jpg', NULL, NULL, '2025-03-05 22:48:09', '2025-05-09 01:19:38', NULL),
(3, 'Guru Matematika SMP', '<EMAIL>', '$2y$12$EPML8kG6VsXzDmFZ1k5LCOqH0odEJA7XPznzZW/9svtQ.y3O7ZtdG', '081234567892', 3, 'Guru', '1746754493.png', NULL, NULL, '2025-03-05 22:48:09', '2025-05-09 01:34:53', NULL),
(4, 'Administrator 2', '<EMAIL>', '$2y$12$NO1qwmxiQWhrTjY2/vbMXO5dE2qjP7BUqN7YdWFiMHvkjTJf8d69K', '081234567893', 3, 'Administrator', NULL, NULL, NULL, '2025-03-05 22:48:10', '2025-04-18 15:23:26', NULL),
(5, 'dwi wiyono', '<EMAIL>', '$2y$12$tfQ.kd8N9EcEcQKZS24ZXOuptNhuFUgnMUvSmDnVXVtQEcYAl2sXC', '08888888888', 2, 'Administrator', NULL, NULL, NULL, '2025-03-05 22:49:33', '2025-04-18 15:21:38', NULL),
(8, 'Guru SD1', '<EMAIL>', '$2y$12$7dkSo54ZlNDd/cM8fw3Zc.qiJRSF/QdKRfcVDAy1MneXux6qe.aOa', '098765', 2, 'Guru', '1746753046.png', NULL, NULL, '2025-03-05 23:48:11', '2025-06-04 21:03:22', '2025-06-04 21:03:22'),
(9, 'Kepala Sekolah SD', '<EMAIL>', '$2y$12$g16pItaI6paOQZ17PfgZXehbrdMuxYG7u0B5Vj7ZEz3En25xLXV5W', '0876567e4', 2, 'Kepala Sekolah', '1746753603.png', NULL, NULL, '2025-03-07 15:04:22', '2025-05-31 13:28:12', '2025-05-31 13:28:12'),
(10, 'Buddha (Siti) Keristen (evi) Katolok (Revila) Kelas 6', '<EMAIL>', '$2y$12$83iuH4eFs2bbmTTTidSztu29M8SDywmAdQjdFQ4oVostYEYqDebwa', '98765', 1, 'Guru', NULL, NULL, NULL, '2025-03-10 20:25:53', '2025-03-10 20:25:53', NULL),
(11, 'Pengawas', '<EMAIL>', '$2y$12$Bp8LIdqWSLqKkI7likDqMe.IpAw8EUKenixhRrOmvw7fG4nzkDq.q', '0877655422', 1, 'Pengawas', NULL, NULL, NULL, '2025-03-29 00:05:57', '2025-04-15 03:31:43', '2025-04-15 03:31:43'),
(12, 'Waka Sarana SMP', '<EMAIL>', '$2y$12$yHM.ITihIF0Upk0TvJNmruCbfae62ppVjQf6DjX8H1T4wXisr2tgK', '09876', 3, 'Waka Sarpras', NULL, NULL, NULL, '2025-03-29 05:47:54', '2025-05-03 22:08:12', '2025-05-03 22:08:12'),
(13, 'Doni', '<EMAIL>', '$2y$12$cYFgJgxIqXhiO4zrJckV4.3oldPoT9GqXtmFDU1teoUq6dzAqchgS', '08123455', 1, 'Waka Kesiswaan', NULL, NULL, NULL, '2025-03-29 06:32:37', '2025-03-29 06:32:37', NULL),
(14, 'Waka Sarana SD', '<EMAIL>', '$2y$12$DFB1yQBHkpXR46MctAs7f.0ApUnf.q1pZCGrnnnZYRX0y/i8YMC6K', '081233433', 1, 'Waka Sarpras', NULL, NULL, NULL, '2025-04-01 22:51:01', '2025-04-01 22:51:01', NULL),
(15, 'Kepala Sekolah SMP', '<EMAIL>', '$2y$12$YOnCI90hpcQ4XZMidhG41efIWbAS88XsIb6JLHQFVO4IohSR5WAry', '082288586974', 3, 'Kepala Sekolah', NULL, NULL, 'kp31bhHGZewW306ENiU9Vh7ACle6AzaknJDIlLd0BQv5FAcDk8tMddg5k96e', '2025-04-01 22:54:02', '2025-05-22 06:12:58', '2025-05-22 06:12:58'),
(16, 'Waka Kurikulum SMP', '<EMAIL>', '$2y$12$xT8fX4YjwaFFlC2XTDplbuZj2F.ENlOD/zmycJxmFaMcQdqT4yys.', '0887654', 3, 'Waka Kurikulum', NULL, NULL, NULL, '2025-04-01 22:55:33', '2025-04-18 15:24:23', NULL),
(17, 'Waka Kesiswaan SMP', '<EMAIL>', '$2y$12$E6uwQvcGVrIFWd94lWx.B.MXLm9HPD.DLhDHlmv3/JL0Pvs4LKaDq', '088765434', 3, 'Waka Kesiswaan', NULL, NULL, NULL, '2025-04-01 22:58:44', '2025-04-18 15:24:38', NULL),
(18, 'Kepala Sekolah SMA', '<EMAIL>', '$2y$12$BD1J3UPulwucjMDmtWREy.Q.HVHGTOq2aCt2R.P1EZ0Z4ZJPCPrXS', '0887654554', 4, 'Kepala Sekolah', NULL, NULL, NULL, '2025-04-01 22:59:46', '2025-05-19 03:31:40', '2025-05-19 03:31:40'),
(19, 'Waka Sarana Prasarana SMA', '<EMAIL>', '$2y$12$WurbuvC/oGdZgKfuhS6XpegeC2crQ65bbm9GpiBseOuM5.NoGY.NK', '0887654777', 4, 'Waka Sarpras', NULL, NULL, NULL, '2025-04-01 23:01:28', '2025-04-01 23:01:28', NULL),
(20, 'Waka Kurikulum SMA', '<EMAIL>', '$2y$12$FyDb.JaqFM0h8c.94XjI3.fkEpDCkwKBRCUrZ7M9XGae0KmcD8b1C', '0887654444', 4, 'Waka Kurikulum', NULL, NULL, NULL, '2025-04-01 23:03:09', '2025-04-01 23:03:09', NULL),
(21, 'Kepala Sekolah TK', '<EMAIL>', '$2y$12$tI6lFR3ii6e93H1u57gVuecJLjC/F7F5bSXBp55L5nd5PSTsMNQF.', '0887654556', 1, 'Kepala Sekolah', NULL, NULL, NULL, '2025-04-01 23:05:02', '2025-04-21 22:26:49', '2025-04-21 22:26:49'),
(22, 'Guru SMP1', '<EMAIL>', '$2y$12$Rv.8yuhg0hd89JdBTimGDe133NzGFeojjsxkUE5GCDfM668svyvra', '08765', 3, 'Guru', NULL, NULL, NULL, '2025-04-04 08:39:22', '2025-04-18 15:25:42', NULL),
(23, 'Guru SD2', '<EMAIL>', '$2y$12$yVfioOf8kg/P1X4o18.acOSwC7oJbgZfPHK/y4fQfS8XxQL59IDWS', '098777', 2, 'Guru', NULL, NULL, NULL, '2025-04-08 05:56:41', '2025-04-18 15:26:00', NULL),
(24, 'Guru SMP3', '<EMAIL>', '$2y$12$34u1dr.DReikQ6DtW7EkjOzjYEGUQhes24Q9L/B8QkKxxCQLyCWaW', '087654', 3, 'Guru', NULL, NULL, NULL, '2025-04-15 02:04:00', '2025-04-25 18:49:54', '2025-04-17 15:45:48'),
(25, 'Guru TK1', '<EMAIL>', '$2y$12$PDQFRnBiVJGPRyZdVW734Oh7FH0WBmOzXhOgZU2AWAsEB83Dmkhgu', '087433', 1, 'Guru', NULL, NULL, NULL, '2025-04-18 15:28:33', '2025-04-18 15:28:33', NULL),
(26, 'gurusmp3', '<EMAIL>', '$2y$12$9WrDQnZs5H8rdcXcZUOS/uriUPFlrndKYsYJsVcXcN3gpumZX.zjC', '899', 3, 'Guru', '1746753126.jpg', NULL, NULL, '2025-05-09 01:12:06', '2025-05-09 01:12:06', NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `absensi_siswa`
--
ALTER TABLE `absensi_siswa`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `absensi_siswa_siswa_id_tanggal_unique` (`siswa_id`,`tanggal`),
  ADD KEY `absensi_siswa_kelas_id_foreign` (`kelas_id`),
  ADD KEY `absensi_siswa_created_by_foreign` (`created_by`);

--
-- Indexes for table `achievements`
--
ALTER TABLE `achievements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `achievements_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `adm_guru`
--
ALTER TABLE `adm_guru`
  ADD PRIMARY KEY (`id`),
  ADD KEY `adm_guru_user_id_foreign` (`user_id`),
  ADD KEY `adm_guru_approved_by_foreign` (`approved_by`),
  ADD KEY `adm_guru_rejected_by_foreign` (`rejected_by`),
  ADD KEY `adm_guru_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `adm_kepsek`
--
ALTER TABLE `adm_kepsek`
  ADD PRIMARY KEY (`id`),
  ADD KEY `adm_kepsek_user_id_foreign` (`user_id`),
  ADD KEY `adm_kepsek_approved_by_foreign` (`approved_by`),
  ADD KEY `adm_kepsek_rejected_by_foreign` (`rejected_by`),
  ADD KEY `adm_kepsek_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `adm_ktsp`
--
ALTER TABLE `adm_ktsp`
  ADD PRIMARY KEY (`id`),
  ADD KEY `adm_ktsp_user_id_foreign` (`user_id`),
  ADD KEY `adm_ktsp_approved_by_foreign` (`approved_by`),
  ADD KEY `adm_ktsp_rejected_by_foreign` (`rejected_by`),
  ADD KEY `adm_ktsp_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `adm_waka`
--
ALTER TABLE `adm_waka`
  ADD PRIMARY KEY (`id`),
  ADD KEY `adm_waka_user_id_foreign` (`user_id`),
  ADD KEY `adm_waka_approved_by_foreign` (`approved_by`),
  ADD KEY `adm_waka_rejected_by_foreign` (`rejected_by`),
  ADD KEY `adm_waka_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `anggota_ekstrakurikulers`
--
ALTER TABLE `anggota_ekstrakurikulers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `anggota_ekstrakurikulers_rombel_ekstrakurikuler_id_foreign` (`rombel_ekstrakurikuler_id`),
  ADD KEY `anggota_ekstrakurikulers_siswa_id_foreign` (`siswa_id`),
  ADD KEY `anggota_ekstrakurikulers_kelas_id_foreign` (`kelas_id`);

--
-- Indexes for table `articles`
--
ALTER TABLE `articles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `articles_slug_unique` (`slug`),
  ADD KEY `articles_author_id_foreign` (`author_id`),
  ADD KEY `articles_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `detail_jadwal`
--
ALTER TABLE `detail_jadwal`
  ADD PRIMARY KEY (`id`),
  ADD KEY `detail_jadwal_jadwal_pelajaran_id_foreign` (`jadwal_pelajaran_id`),
  ADD KEY `detail_jadwal_mata_pelajaran_id_foreign` (`mata_pelajaran_id`);

--
-- Indexes for table `ekstrakurikulers`
--
ALTER TABLE `ekstrakurikulers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ekstrakurikulers_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `events`
--
ALTER TABLE `events`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `events_slug_unique` (`slug`),
  ADD KEY `events_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `facilities`
--
ALTER TABLE `facilities`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `gedungs`
--
ALTER TABLE `gedungs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `gedungs_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `guru`
--
ALTER TABLE `guru`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `guru_nik_unique` (`nik`),
  ADD KEY `guru_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `guru_tugastambahan`
--
ALTER TABLE `guru_tugastambahan`
  ADD PRIMARY KEY (`id`),
  ADD KEY `guru_tugastambahan_guru_id_index` (`guru_id`),
  ADD KEY `guru_tugastambahan_tahun_ajaran_id_index` (`tahun_ajaran_id`);

--
-- Indexes for table `halaman`
--
ALTER TABLE `halaman`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `halaman_slug_unique` (`slug`),
  ADD KEY `halaman_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `jadwal_pelajaran`
--
ALTER TABLE `jadwal_pelajaran`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jadwal_pelajaran_kelas_id_foreign` (`kelas_id`);

--
-- Indexes for table `jam_pengganti`
--
ALTER TABLE `jam_pengganti`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jam_pengganti_jurnal_kegiatan_id_foreign` (`jurnal_kegiatan_id`);

--
-- Indexes for table `jenjangs`
--
ALTER TABLE `jenjangs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `jurnal_eskul`
--
ALTER TABLE `jurnal_eskul`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jurnal_eskul_jurnal_kegiatan_id_foreign` (`jurnal_kegiatan_id`);

--
-- Indexes for table `jurnal_kegiatan`
--
ALTER TABLE `jurnal_kegiatan`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jurnal_kegiatan_user_id_foreign` (`user_id`),
  ADD KEY `jurnal_kegiatan_approved_by_foreign` (`approved_by`),
  ADD KEY `jurnal_kegiatan_rejected_by_foreign` (`rejected_by`),
  ADD KEY `jurnal_kegiatan_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `kalender_pendidikan`
--
ALTER TABLE `kalender_pendidikan`
  ADD PRIMARY KEY (`id`),
  ADD KEY `kalender_pendidikan_created_by_foreign` (`created_by`);

--
-- Indexes for table `kelas`
--
ALTER TABLE `kelas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `kelas_jenjang_id_foreign` (`jenjang_id`),
  ADD KEY `kelas_unit_id_foreign` (`unit_id`),
  ADD KEY `kelas_gedung_id_foreign` (`gedung_id`);

--
-- Indexes for table `laporan_kerja`
--
ALTER TABLE `laporan_kerja`
  ADD PRIMARY KEY (`id`),
  ADD KEY `laporan_kerja_user_id_foreign` (`user_id`);

--
-- Indexes for table `laporan_kerusakan`
--
ALTER TABLE `laporan_kerusakan`
  ADD PRIMARY KEY (`id`),
  ADD KEY `laporan_kerusakan_pelapor_id_foreign` (`pelapor_id`),
  ADD KEY `laporan_kerusakan_penindak_id_foreign` (`penindak_id`);

--
-- Indexes for table `mata_pelajaran`
--
ALTER TABLE `mata_pelajaran`
  ADD PRIMARY KEY (`id`),
  ADD KEY `mata_pelajaran_pengajar_id_foreign` (`pengajar_id`),
  ADD KEY `mata_pelajaran_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`);

--
-- Indexes for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `notifikasi`
--
ALTER TABLE `notifikasi`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifikasi_user_id_foreign` (`user_id`);

--
-- Indexes for table `pengembangan_diri`
--
ALTER TABLE `pengembangan_diri`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pengembangan_diri_guru_id_foreign` (`guru_id`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `peserta_didik`
--
ALTER TABLE `peserta_didik`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `peserta_didik_nis_unique` (`nis`),
  ADD UNIQUE KEY `peserta_didik_nisn_unique` (`nisn`),
  ADD UNIQUE KEY `idx_nik` (`nik`),
  ADD KEY `peserta_didik_kelas_id_foreign` (`kelas_id`),
  ADD KEY `peserta_didik_unit_id_foreign` (`unit_id`);

--
-- Indexes for table `riwayat_kelas`
--
ALTER TABLE `riwayat_kelas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `riwayat_kelas_siswa_id_foreign` (`siswa_id`),
  ADD KEY `riwayat_kelas_kelas_lama_id_foreign` (`kelas_lama_id`),
  ADD KEY `riwayat_kelas_kelas_baru_id_foreign` (`kelas_baru_id`),
  ADD KEY `riwayat_kelas_created_by_foreign` (`created_by`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

--
-- Indexes for table `rombel_ekstrakurikulers`
--
ALTER TABLE `rombel_ekstrakurikulers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `sarana`
--
ALTER TABLE `sarana`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sarana_unit_id_foreign` (`unit_id`),
  ADD KEY `sarana_gedung_id_foreign` (`gedung_id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `settings_key_unique` (`key`);

--
-- Indexes for table `slides`
--
ALTER TABLE `slides`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `spps`
--
ALTER TABLE `spps`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `spps_va_number_unique` (`va_number`);

--
-- Indexes for table `spp_uploads`
--
ALTER TABLE `spp_uploads`
  ADD PRIMARY KEY (`id`),
  ADD KEY `spp_uploads_uploaded_by_foreign` (`uploaded_by`);

--
-- Indexes for table `tahun_ajaran`
--
ALTER TABLE `tahun_ajaran`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `units`
--
ALTER TABLE `units`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`),
  ADD KEY `users_unit_id_foreign` (`unit_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `absensi_siswa`
--
ALTER TABLE `absensi_siswa`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=67;

--
-- AUTO_INCREMENT for table `achievements`
--
ALTER TABLE `achievements`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `adm_guru`
--
ALTER TABLE `adm_guru`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `adm_kepsek`
--
ALTER TABLE `adm_kepsek`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `adm_ktsp`
--
ALTER TABLE `adm_ktsp`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `adm_waka`
--
ALTER TABLE `adm_waka`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `anggota_ekstrakurikulers`
--
ALTER TABLE `anggota_ekstrakurikulers`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `articles`
--
ALTER TABLE `articles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `detail_jadwal`
--
ALTER TABLE `detail_jadwal`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=87;

--
-- AUTO_INCREMENT for table `ekstrakurikulers`
--
ALTER TABLE `ekstrakurikulers`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `events`
--
ALTER TABLE `events`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `facilities`
--
ALTER TABLE `facilities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `gedungs`
--
ALTER TABLE `gedungs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `guru`
--
ALTER TABLE `guru`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `guru_tugastambahan`
--
ALTER TABLE `guru_tugastambahan`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `halaman`
--
ALTER TABLE `halaman`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `jadwal_pelajaran`
--
ALTER TABLE `jadwal_pelajaran`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `jam_pengganti`
--
ALTER TABLE `jam_pengganti`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `jenjangs`
--
ALTER TABLE `jenjangs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `jurnal_eskul`
--
ALTER TABLE `jurnal_eskul`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `jurnal_kegiatan`
--
ALTER TABLE `jurnal_kegiatan`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=36;

--
-- AUTO_INCREMENT for table `kalender_pendidikan`
--
ALTER TABLE `kalender_pendidikan`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `kelas`
--
ALTER TABLE `kelas`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `laporan_kerja`
--
ALTER TABLE `laporan_kerja`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `laporan_kerusakan`
--
ALTER TABLE `laporan_kerusakan`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `mata_pelajaran`
--
ALTER TABLE `mata_pelajaran`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=88;

--
-- AUTO_INCREMENT for table `notifikasi`
--
ALTER TABLE `notifikasi`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=59;

--
-- AUTO_INCREMENT for table `pengembangan_diri`
--
ALTER TABLE `pengembangan_diri`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=118;

--
-- AUTO_INCREMENT for table `peserta_didik`
--
ALTER TABLE `peserta_didik`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=238;

--
-- AUTO_INCREMENT for table `riwayat_kelas`
--
ALTER TABLE `riwayat_kelas`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=72;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `rombel_ekstrakurikulers`
--
ALTER TABLE `rombel_ekstrakurikulers`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `sarana`
--
ALTER TABLE `sarana`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `slides`
--
ALTER TABLE `slides`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `spps`
--
ALTER TABLE `spps`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `spp_uploads`
--
ALTER TABLE `spp_uploads`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `tahun_ajaran`
--
ALTER TABLE `tahun_ajaran`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `units`
--
ALTER TABLE `units`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `absensi_siswa`
--
ALTER TABLE `absensi_siswa`
  ADD CONSTRAINT `absensi_siswa_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `absensi_siswa_kelas_id_foreign` FOREIGN KEY (`kelas_id`) REFERENCES `jadwal_pelajaran` (`id`),
  ADD CONSTRAINT `absensi_siswa_siswa_id_foreign` FOREIGN KEY (`siswa_id`) REFERENCES `peserta_didik` (`id`);

--
-- Constraints for table `achievements`
--
ALTER TABLE `achievements`
  ADD CONSTRAINT `achievements_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `adm_guru`
--
ALTER TABLE `adm_guru`
  ADD CONSTRAINT `adm_guru_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `adm_guru_rejected_by_foreign` FOREIGN KEY (`rejected_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `adm_guru_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`),
  ADD CONSTRAINT `adm_guru_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `adm_kepsek`
--
ALTER TABLE `adm_kepsek`
  ADD CONSTRAINT `adm_kepsek_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `adm_kepsek_rejected_by_foreign` FOREIGN KEY (`rejected_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `adm_kepsek_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`),
  ADD CONSTRAINT `adm_kepsek_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `adm_ktsp`
--
ALTER TABLE `adm_ktsp`
  ADD CONSTRAINT `adm_ktsp_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `adm_ktsp_rejected_by_foreign` FOREIGN KEY (`rejected_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `adm_ktsp_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`),
  ADD CONSTRAINT `adm_ktsp_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `adm_waka`
--
ALTER TABLE `adm_waka`
  ADD CONSTRAINT `adm_waka_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `adm_waka_rejected_by_foreign` FOREIGN KEY (`rejected_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `adm_waka_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`),
  ADD CONSTRAINT `adm_waka_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `anggota_ekstrakurikulers`
--
ALTER TABLE `anggota_ekstrakurikulers`
  ADD CONSTRAINT `anggota_ekstrakurikulers_kelas_id_foreign` FOREIGN KEY (`kelas_id`) REFERENCES `kelas` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `anggota_ekstrakurikulers_rombel_ekstrakurikuler_id_foreign` FOREIGN KEY (`rombel_ekstrakurikuler_id`) REFERENCES `rombel_ekstrakurikulers` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `anggota_ekstrakurikulers_siswa_id_foreign` FOREIGN KEY (`siswa_id`) REFERENCES `peserta_didik` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `articles`
--
ALTER TABLE `articles`
  ADD CONSTRAINT `articles_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `articles_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `detail_jadwal`
--
ALTER TABLE `detail_jadwal`
  ADD CONSTRAINT `detail_jadwal_jadwal_pelajaran_id_foreign` FOREIGN KEY (`jadwal_pelajaran_id`) REFERENCES `jadwal_pelajaran` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `detail_jadwal_mata_pelajaran_id_foreign` FOREIGN KEY (`mata_pelajaran_id`) REFERENCES `mata_pelajaran` (`id`);

--
-- Constraints for table `ekstrakurikulers`
--
ALTER TABLE `ekstrakurikulers`
  ADD CONSTRAINT `ekstrakurikulers_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `events`
--
ALTER TABLE `events`
  ADD CONSTRAINT `events_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `gedungs`
--
ALTER TABLE `gedungs`
  ADD CONSTRAINT `gedungs_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `guru`
--
ALTER TABLE `guru`
  ADD CONSTRAINT `guru_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`);

--
-- Constraints for table `guru_tugastambahan`
--
ALTER TABLE `guru_tugastambahan`
  ADD CONSTRAINT `guru_tugastambahan_guru_id_foreign` FOREIGN KEY (`guru_id`) REFERENCES `guru` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `guru_tugastambahan_tahun_ajaran_id_foreign` FOREIGN KEY (`tahun_ajaran_id`) REFERENCES `tahun_ajaran` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `halaman`
--
ALTER TABLE `halaman`
  ADD CONSTRAINT `halaman_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `jadwal_pelajaran`
--
ALTER TABLE `jadwal_pelajaran`
  ADD CONSTRAINT `jadwal_pelajaran_kelas_id_foreign` FOREIGN KEY (`kelas_id`) REFERENCES `kelas` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `jam_pengganti`
--
ALTER TABLE `jam_pengganti`
  ADD CONSTRAINT `jam_pengganti_jurnal_kegiatan_id_foreign` FOREIGN KEY (`jurnal_kegiatan_id`) REFERENCES `jurnal_kegiatan` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `jurnal_eskul`
--
ALTER TABLE `jurnal_eskul`
  ADD CONSTRAINT `jurnal_eskul_jurnal_kegiatan_id_foreign` FOREIGN KEY (`jurnal_kegiatan_id`) REFERENCES `jurnal_kegiatan` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `jurnal_kegiatan`
--
ALTER TABLE `jurnal_kegiatan`
  ADD CONSTRAINT `jurnal_kegiatan_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `jurnal_kegiatan_rejected_by_foreign` FOREIGN KEY (`rejected_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `jurnal_kegiatan_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `jurnal_kegiatan_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `kalender_pendidikan`
--
ALTER TABLE `kalender_pendidikan`
  ADD CONSTRAINT `kalender_pendidikan_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `kelas`
--
ALTER TABLE `kelas`
  ADD CONSTRAINT `kelas_gedung_id_foreign` FOREIGN KEY (`gedung_id`) REFERENCES `gedungs` (`id`),
  ADD CONSTRAINT `kelas_jenjang_id_foreign` FOREIGN KEY (`jenjang_id`) REFERENCES `jenjangs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `kelas_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`);

--
-- Constraints for table `laporan_kerja`
--
ALTER TABLE `laporan_kerja`
  ADD CONSTRAINT `laporan_kerja_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `laporan_kerusakan`
--
ALTER TABLE `laporan_kerusakan`
  ADD CONSTRAINT `laporan_kerusakan_pelapor_id_foreign` FOREIGN KEY (`pelapor_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `laporan_kerusakan_penindak_id_foreign` FOREIGN KEY (`penindak_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `mata_pelajaran`
--
ALTER TABLE `mata_pelajaran`
  ADD CONSTRAINT `mata_pelajaran_pengajar_id_foreign` FOREIGN KEY (`pengajar_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `mata_pelajaran_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notifikasi`
--
ALTER TABLE `notifikasi`
  ADD CONSTRAINT `notifikasi_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `pengembangan_diri`
--
ALTER TABLE `pengembangan_diri`
  ADD CONSTRAINT `pengembangan_diri_guru_id_foreign` FOREIGN KEY (`guru_id`) REFERENCES `guru` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `peserta_didik`
--
ALTER TABLE `peserta_didik`
  ADD CONSTRAINT `peserta_didik_kelas_id_foreign` FOREIGN KEY (`kelas_id`) REFERENCES `kelas` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `peserta_didik_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `riwayat_kelas`
--
ALTER TABLE `riwayat_kelas`
  ADD CONSTRAINT `riwayat_kelas_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `riwayat_kelas_kelas_baru_id_foreign` FOREIGN KEY (`kelas_baru_id`) REFERENCES `kelas` (`id`),
  ADD CONSTRAINT `riwayat_kelas_kelas_lama_id_foreign` FOREIGN KEY (`kelas_lama_id`) REFERENCES `kelas` (`id`),
  ADD CONSTRAINT `riwayat_kelas_siswa_id_foreign` FOREIGN KEY (`siswa_id`) REFERENCES `peserta_didik` (`id`);

--
-- Constraints for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `sarana`
--
ALTER TABLE `sarana`
  ADD CONSTRAINT `sarana_gedung_id_foreign` FOREIGN KEY (`gedung_id`) REFERENCES `gedungs` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `sarana_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `spp_uploads`
--
ALTER TABLE `spp_uploads`
  ADD CONSTRAINT `spp_uploads_uploaded_by_foreign` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`);

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_unit_id_foreign` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
