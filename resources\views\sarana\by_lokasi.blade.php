
@extends('adminlte::page')

@section('title', 'Sarana di ' . $gedung->nama_gedung)

@section('content_header')
    <h1><PERSON><PERSON> di {{ $gedung->nama_gedung }}</h1>
@stop

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Daftar Sarana di {{ $gedung->nama_gedung }} ({{ $gedung->unit->nama_unit }})</h3>
                    <div class="card-tools">
                        <a href="javascript:history.back()" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>

                    </div>
                </div>
                <div class="card-body">
                    @if($sarana->isEmpty())
                        <div class="alert alert-info">
                            Belum ada sarana yang ditempatkan di lokasi ini.
                        </div>
                    @else
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th style="width: 10px">#</th>
                                    <th>Nama <PERSON></th>
                                    <th>No. Barang</th>
                                    <th>Jenis</th>
                                    <th>Jumlah</th>
                                    <th>Kondisi</th>
                                    <th>Tahun Pengadaan</th>
                                    <th style="width: 150px">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($sarana as $index => $item)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $item->nama_sarana }}</td>
                                        <td>{{ $item->no_barang ?? '-' }}</td>
                                        <td>{{ $item->jenis }}</td>
                                        <td>{{ $item->jumlah }}</td>
                                        <td>
                                            @if($item->kondisi == 'Baik')
                                                <span class="badge bg-success">{{ $item->kondisi }}</span>
                                            @elseif($item->kondisi == 'Rusak Ringan')
                                                <span class="badge bg-warning">{{ $item->kondisi }}</span>
                                            @else
                                                <span class="badge bg-danger">{{ $item->kondisi }}</span>
                                            @endif
                                        </td>
                                        <td>{{ $item->tahun_pengadaan ?? '-' }}</td>
                                        <td>
                                            {{-- Tombol Lihat Detail - Semua pengguna dengan permission view-sarpras dapat melihat --}}
                                            <a href="{{ route('sarana.showlokasi', $item->id) }}" class="btn btn-info btn-sm" title="Lihat Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            
                                            {{-- Tombol Edit - Hanya muncul jika pengguna memiliki permission edit-sarpras --}}
                                            @can('edit-sarpras')
                                            <a href="{{ route('sarana.edit', $item->id) }}" class="btn btn-warning btn-sm" title="Edit Data">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @endcan
                                            
                                            {{-- Tombol Hapus - Hanya muncul jika pengguna memiliki permission delete-sarpras --}}
                                            @can('delete-sarpras')
                                            <form action="{{ route('sarana.destroy', $item->id) }}" method="POST" style="display: inline-block;">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')" title="Hapus Data">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            @endcan
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @endif
                </div>
            </div>
        </div>
    </div>
@stop


