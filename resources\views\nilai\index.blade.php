@extends('adminlte::page')

@section('title', 'Dashboard Nilai')

@section('content_header')
    <h1>Dashboard Nilai</h1>
@stop

@section('content')
<div class="container-fluid">
    <!-- Kartu Statistik -->
    <div class="row">
        <div class="col-lg-4 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3>{{ $totalFormatif }}</h3>
                    <p>Total Penilaian Formatif</p>
                </div>
                <div class="icon">
                    <i class="fas fa-tasks"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3>{{ $totalSumatif }}</h3>
                    <p>Total Penilaian Sumatif</p>
                </div>
                <div class="icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>{{ $totalProyek }}</h3>
                    <p>Total Penilaian Proyek</p>
                </div>
                <div class="icon">
                    <i class="fas fa-project-diagram"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Menu Navigasi Cepat -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Menu Penilaian</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('penilaian.kompetensi.index') }}" class="btn btn-block btn-lg btn-outline-primary">
                                <i class="fas fa-book mr-2"></i> Kompetensi Dasar
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('penilaian.formatif.index') }}" class="btn btn-block btn-lg btn-outline-info">
                                <i class="fas fa-tasks mr-2"></i> Penilaian Formatif
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('penilaian.sumatif.index') }}" class="btn btn-block btn-lg btn-outline-success">
                                <i class="fas fa-chart-bar mr-2"></i> Penilaian Sumatif
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('penilaian.proyek.index') }}" class="btn btn-block btn-lg btn-outline-warning">
                                <i class="fas fa-project-diagram mr-2"></i> Penilaian Proyek
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Pencarian Laporan -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Laporan Nilai Per Kelas</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('nilai.laporan-kelas') }}" method="GET">
                        <div class="form-group">
                            <label for="kelas_id">Pilih Kelas</label>
                            <select name="kelas_id" id="kelas_id" class="form-control" required>
                                <option value="">-- Pilih Kelas --</option>
                                @foreach($kelas as $k)
                                    <option value="{{ $k->id }}">{{ $k->nama_kelas }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="mata_pelajaran_id">Pilih Mata Pelajaran</label>
                            <select name="mata_pelajaran_id" id="mata_pelajaran_id" class="form-control" required>
                                <option value="">-- Pilih Mata Pelajaran --</option>
                                @foreach($mataPelajaran as $mp)
                                    <option value="{{ $mp->id }}">{{ $mp->nama_mapel }}</option>
                                @endforeach
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">Lihat Laporan</button>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Laporan Nilai Per Siswa</h3>
                </div>
                <div class="card-body">
                    <a href="{{ route('nilai.cari-siswa') }}" class="btn btn-block btn-lg btn-primary">
                        <i class="fas fa-search mr-2"></i> Cari Siswa
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            // Inisialisasi Select2 untuk dropdown
            $('select').select2({
                theme: 'bootstrap4',
                width: '100%'
            });
        });
    </script>
@stop