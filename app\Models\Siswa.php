<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Siswa extends Model
{
    use HasFactory;
    
    // Tentukan tabel yang digunakan
    protected $table = 'peserta_didik';
    
    // <PERSON><PERSON>m yang dapat diisi
    protected $fillable = [
        'nama',
        //'nis',
        'kelas_id',
        'nik', // Tambahkan NIK ke fillable
        // tambahkan kolom lain yang diperlukan
    ];
    
    // Relasi dengan kelas
    public function kelas()
    {
        return $this->belongsTo(Kelas::class);
    }
}





