<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Kelas;
use App\Models\Siswa;
use App\Models\TahunAjaran;
use App\Models\Unit;
use App\Models\RiwayatKelas;
use App\Models\PesertaDidik;
use Illuminate\Support\Facades\Schema;
use App\Traits\FiltersByUserUnit;

class RombelController extends Controller
{
    use FiltersByUserUnit;
    
    public function reguler()
    {
        return redirect()->route('rombel.reguler.daftar');
    }
    
    public function daftarKelas()
    {
        $tahunAjaran = TahunAjaran::where('aktif', true)->first();
        
        if (!$tahunAjaran) {
            return redirect()->back()->with('error', 'Tahun ajaran aktif belum ditentukan');
        }
        
        // Ubah query untuk menggunakan relasi jenjang dan mengurutkan berdasarkan jenjang
        $query = Kelas::with(['siswa', 'jenjang'])
            ->where('tahun_ajaran', $tahunAjaran->nama)
            ->join('jenjangs', 'kelas.jenjang_id', '=', 'jenjangs.id')
            ->orderBy('jenjangs.tingkat')
            ->orderBy('kelas.nama')
            ->select('kelas.*');
        
        // Terapkan filter unit
        $query = $this->applyUnitFilter($query);
        
        $kelas = $query->get();
        
        return view('rombel.reguler.daftar', compact('kelas', 'tahunAjaran'));
    }
    
    public function penempatan()
    {
        $tahunAjaran = TahunAjaran::where('aktif', true)->first();
        
        if (!$tahunAjaran) {
            return redirect()->back()->with('error', 'Tahun ajaran aktif belum ditentukan');
        }
        
        // Query untuk kelas
        $queryKelas = Kelas::where('tahun_ajaran', $tahunAjaran->nama);
        $queryKelas = $this->applyUnitFilter($queryKelas);
        $kelas = $queryKelas->get();
        
        // Ubah query untuk mendapatkan siswa yang belum ditempatkan di kelas
        // atau siswa yang kelas_id-nya tidak ada di daftar kelas tahun ajaran aktif
        $querySiswa = Siswa::where(function($query) use ($kelas) {
                $query->whereNull('kelas_id')
                      ->orWhereNotIn('kelas_id', $kelas->pluck('id'));
            })
            ->orderBy('nama');
        
        // Terapkan filter unit pada siswa
        $querySiswa = $this->applyUnitFilter($querySiswa);
        $siswa = $querySiswa->get();
        
        // Ambil daftar unit untuk filter
        $units = Unit::orderBy('nama_unit')->get();
        
        return view('rombel.reguler.penempatan', compact('kelas', 'siswa', 'tahunAjaran', 'units'));
    }
    
    public function penempatanIndividu(Request $request)
    {
        $request->validate([
            'siswa_id' => 'required|exists:siswa,id',
            'kelas_id' => 'required|exists:kelas,id',
        ]);
        
        $siswa = Siswa::findOrFail($request->siswa_id);
        $siswa->kelas_id = $request->kelas_id;
        $siswa->save();
        
        return redirect()->back()->with('success', 'Siswa berhasil ditempatkan ke kelas');
    }
    
    public function penempatanKelas(Request $request)
    {
        $request->validate([
            'siswa_ids' => 'required|array',
            'siswa_ids.*' => 'exists:siswa,id',
            'kelas_id' => 'required|exists:kelas,id',
        ]);
        
        Siswa::whereIn('id', $request->siswa_ids)->update(['kelas_id' => $request->kelas_id]);
        
        return redirect()->back()->with('success', 'Siswa berhasil ditempatkan ke kelas secara massal');
    }
    
    public function kenaikanView()
    {
        //$user = auth()->user();
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->first();
        
        if (!$tahunAjaranAktif) {
            return redirect()->back()->with('error', 'Tahun ajaran aktif belum ditentukan');
        }
        
        // Ambil daftar kelas pada tahun ajaran aktif
        $query = Kelas::where('tahun_ajaran', $tahunAjaranAktif->nama);
        
        // Terapkan filter unit
        $query = $this->applyUnitFilter($query);
        $kelas = $query->orderBy('nama')->get();
        
        // Hitung tahun ajaran berikutnya
        $tahunAjaranBerikutnya = $this->getTahunAjaranBerikutnya($tahunAjaranAktif->nama);
        
        // Query untuk kelas target (tahun ajaran berikutnya)
        $queryTarget = Kelas::where('tahun_ajaran', $tahunAjaranBerikutnya);
        
        // Terapkan filter unit pada kelas target
        $queryTarget = $this->applyUnitFilter($queryTarget);
        $kelasTarget = $queryTarget->orderBy('nama')->get();
        
        // Dapatkan daftar tahun ajaran untuk dropdown
        $tahunAjaranList = TahunAjaran::orderBy('nama', 'desc')->pluck('nama')->toArray();
        
        return view('rombel.reguler.kenaikan', compact('kelas', 'kelasTarget', 'tahunAjaranAktif', 'tahunAjaranList', 'tahunAjaranBerikutnya'));
    }
    
    public function kenaikanIndividu(Request $request)
    {
        $request->validate([
            'siswa_id' => 'required|exists:peserta_didik,id', // Ubah siswa menjadi peserta_didik
            'kelas_id' => 'required|exists:kelas,id',
            'tahun_ajaran' => 'required|string', // Tambahkan validasi untuk tahun ajaran
        ]);
        
        $siswa = Siswa::findOrFail($request->siswa_id);
        $kelasLama = $siswa->kelas_id;
        
        $siswa->kelas_id = $request->kelas_id;
        $siswa->save();
        
        // Catat riwayat kenaikan kelas
        RiwayatKelas::create([
            'siswa_id' => $siswa->id,
            'kelas_lama_id' => $kelasLama,
            'kelas_baru_id' => $request->kelas_id,
            'tahun_ajaran' => $request->tahun_ajaran, // Gunakan tahun ajaran dari form
            'jenis_perpindahan' => 'kenaikan_kelas',
            'tanggal_pindah' => now(),
            'alasan' => 'Kenaikan kelas individu',
            'created_by' => auth()->id()
        ]);
        
        return redirect()->back()->with('success', 'Siswa berhasil dinaikkan ke kelas baru');
    }
    
    public function kenaikanKelas(Request $request)
    {
        $request->validate([
            'kelas_asal_id' => 'required|exists:kelas,id',
            'kelas_tujuan_id' => 'required|exists:kelas,id',
            'tahun_ajaran' => 'required|string',
        ]);
        
        // Ambil data kelas tujuan
        $kelasTujuan = Kelas::findOrFail($request->kelas_tujuan_id);
        
        // Gunakan model PesertaDidik dan tabel riwayat_kelas untuk mendapatkan siswa
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->value('nama');
        
        $siswaList = PesertaDidik::join('riwayat_kelas', 'peserta_didik.id', '=', 'riwayat_kelas.siswa_id')
            ->where('riwayat_kelas.kelas_baru_id', $request->kelas_asal_id)
            ->where('riwayat_kelas.tahun_ajaran', $tahunAjaranAktif)
            ->where('peserta_didik.status', 'aktif')
            ->select('peserta_didik.*')
            ->distinct()
            ->get();
        
        foreach ($siswaList as $siswa) {
            $kelasLama = $siswa->kelas_id;
            $siswa->kelas_id = $request->kelas_tujuan_id;
            $siswa->save();
            
            // Catat riwayat kenaikan kelas
            RiwayatKelas::create([
                'siswa_id' => $siswa->id,
                'kelas_lama_id' => $kelasLama,
                'kelas_baru_id' => $request->kelas_tujuan_id,
                'tahun_ajaran' => $request->tahun_ajaran,
                'jenis_perpindahan' => 'kenaikan_kelas',
                'tanggal_pindah' => now(),
                'alasan' => 'Kenaikan kelas massal',
                'created_by' => auth()->id()
            ]);
        }
        
        return redirect()->back()->with('success', 'Seluruh siswa berhasil dinaikkan ke kelas baru');
    }
    
    public function kelulusan(Request $request)
    {
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->first();
        
        if (!$tahunAjaranAktif) {
            return redirect()->back()->with('error', 'Tahun ajaran aktif belum ditentukan');
        }
        
        // Join dengan tabel jenjangs untuk memfilter kelas tingkat akhir
        $query = Kelas::with(['siswa' => function($query) {
                $query->where('status', 'aktif');
            }])
            ->join('jenjangs', 'kelas.jenjang_id', '=', 'jenjangs.id')
            ->where('kelas.tahun_ajaran', $tahunAjaranAktif->nama)
            ->where('jenjangs.tingkat', 'Tingkat Akhir')
            ->select('kelas.*');
        
        // Terapkan filter unit
        $query = $this->applyUnitFilter($query);
        $kelas = $query->get();
        
        // Ambil daftar siswa jika kelas_id ada di request
        $siswaList = collect();
        if ($request->has('kelas_id') && $request->kelas_id) {
            // Gunakan model PesertaDidik yang sesuai dengan tabel peserta_didik
            $siswaList = PesertaDidik::where('kelas_id', $request->kelas_id)
                            ->where('status', 'aktif')
                            ->orderBy('nama')
                            ->get();
        }
        
        return view('rombel.reguler.kelulusan', compact('kelas', 'tahunAjaranAktif', 'siswaList'));
    }
    
    public function kelulusanIndividu(Request $request)
    {
        $request->validate([
            'siswa_id' => 'required|exists:peserta_didik,id',
        ]);
        
        // Dapatkan tahun ajaran aktif
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->first();
        
        if (!$tahunAjaranAktif) {
            return redirect()->back()->with('error', 'Tidak ada tahun ajaran aktif. Silakan aktifkan tahun ajaran terlebih dahulu.');
        }
        
        $siswa = PesertaDidik::findOrFail($request->siswa_id);
        $kelasLama = $siswa->kelas_id;
        
        // Update status siswa menjadi alumni dan nonaktifkan
        $siswa->status = 'alumni';
        $siswa->is_active = false; // Set is_active menjadi false
        $siswa->tanggal_lulus = now();
        $siswa->kelas_id = null;
        $siswa->save();
        
        // Catat riwayat kelulusan
        RiwayatKelas::create([
            'siswa_id' => $siswa->id,
            'kelas_lama_id' => $kelasLama,
            'kelas_baru_id' => null,
            'tahun_ajaran' => $tahunAjaranAktif->nama,
            'jenis_perpindahan' => 'kelulusan', // Pastikan jenis_perpindahan adalah 'kelulusan'
            'tanggal_pindah' => now(),
            'alasan' => 'Kelulusan',
            'created_by' => auth()->id()
        ]);
        
        return redirect()->back()->with('success', 'Siswa berhasil diluluskan');
    }
    
    public function kelulusanKelas(Request $request)
    {
        $request->validate([
            'kelas_id' => 'required|exists:kelas,id',
        ]);
        
        $siswa = Siswa::where('kelas_id', $request->kelas_id)->get();
        
        foreach ($siswa as $s) {
            $kelasLama = $s->kelas_id;
            $s->status = 'alumni';
            $s->tanggal_lulus = now();
            $s->kelas_id = null;
            $s->save();
            
            // Catat riwayat kelulusan
            RiwayatKelas::create([
                'siswa_id' => $s->id,
                'kelas_lama_id' => $kelasLama,
                'kelas_baru_id' => null,
                'tanggal_pindah' => now(),
                'alasan' => 'Kelulusan massal',
                'created_by' => auth()->id()
            ]);
        }
        
        return redirect()->back()->with('success', 'Seluruh siswa di kelas berhasil diluluskan');
    }
    

    public function pindahKelas()
    {
        $tahunAjaran = TahunAjaran::where('aktif', true)->first();
        
        if (!$tahunAjaran) {
            return redirect()->back()->with('error', 'Tahun ajaran aktif belum ditentukan');
        }
        
        // Query untuk kelas
        $queryKelas = Kelas::where('tahun_ajaran', $tahunAjaran->nama)
                      ->orderBy('nama');
        
        // Terapkan filter unit
        $queryKelas = $this->applyUnitFilter($queryKelas);
        $kelas = $queryKelas->get();
        
        // Ambil siswa yang sudah memiliki kelas pada tahun ajaran aktif
        $querySiswa = Siswa::whereIn('kelas_id', $kelas->pluck('id'))
                       ->orderBy('nama');
        
        // Terapkan filter unit pada siswa
        $querySiswa = $this->applyUnitFilter($querySiswa, 'kelas');
        $siswa = $querySiswa->get();
        
        // Ambil daftar unit untuk filter
        $units = Unit::orderBy('nama_unit')->get();
        
        return view('rombel.reguler.pindah-kelas', compact('kelas', 'siswa', 'tahunAjaran', 'units'));
    }

    public function pindahKelasIndividu(Request $request)
    {
        $request->validate([
            'siswa_id' => 'required|exists:peserta_didik,id',
            'kelas_id' => 'required|exists:kelas,id',
        ]);
        
        // Dapatkan tahun ajaran aktif
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->first();
        
        if (!$tahunAjaranAktif) {
            return redirect()->back()->with('error', 'Tidak ada tahun ajaran aktif. Silakan aktifkan tahun ajaran terlebih dahulu.');
        }
        
        // Debugging
        \Log::info('Siswa ID: ' . $request->siswa_id);
        \Log::info('Kelas ID: ' . $request->kelas_id);
        
        // Gunakan model Siswa yang sudah dikonfigurasi untuk tabel peserta_didik
        $siswa = Siswa::findOrFail($request->siswa_id);
        $kelasLama = $siswa->kelas_id;
        $siswa->kelas_id = $request->kelas_id;
        $siswa->save();
        
        // Data untuk riwayat kelas
        $riwayatData = [
            'siswa_id' => $siswa->id,
            'kelas_lama_id' => $kelasLama,
            'kelas_baru_id' => $request->kelas_id,
            'tahun_ajaran' => $tahunAjaranAktif->nama,
            'jenis_perpindahan' => 'pindah_kelas',
            'tanggal_pindah' => now(),
            'alasan' => $request->alasan ?? 'Perpindahan kelas individu',
            'created_by' => auth()->id()
        ];
        
        // Debugging
        \Log::info('Data Riwayat Kelas: ', $riwayatData);
        
        // Catat riwayat perpindahan kelas
        RiwayatKelas::create($riwayatData);
        
        return redirect()->back()->with('success', 'Siswa berhasil dipindahkan ke kelas baru');
    }

    public function pindahKelasMassal(Request $request)
    {
        $request->validate([
            'siswa_ids' => 'required|array',
            'siswa_ids.*' => 'exists:peserta_didik,id',
            'kelas_id' => 'required|exists:kelas,id',
        ]);
        
        // Dapatkan tahun ajaran aktif
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->first();
        
        if (!$tahunAjaranAktif) {
            return redirect()->back()->with('error', 'Tidak ada tahun ajaran aktif. Silakan aktifkan tahun ajaran terlebih dahulu.');
        }
        
        // Debugging - cek nilai tahun ajaran
        \Log::info('Tahun Ajaran Aktif: ' . $tahunAjaranAktif->nama);
        
        foreach ($request->siswa_ids as $siswaId) {
            $siswa = Siswa::findOrFail($siswaId);
            $kelasLama = $siswa->kelas_id;
            $siswa->kelas_id = $request->kelas_id;
            $siswa->save();
            
            // Data untuk riwayat kelas
            $riwayatData = [
                'siswa_id' => $siswa->id,
                'kelas_lama_id' => $kelasLama,
                'kelas_baru_id' => $request->kelas_id,
                'tahun_ajaran' => $tahunAjaranAktif->nama,
                'jenis_perpindahan' => 'pindah_kelas',
                'tanggal_pindah' => now(),
                'alasan' => $request->alasan ?? 'Perpindahan kelas massal',
                'created_by' => auth()->id()
            ];
            
            // Debugging - cek data yang akan disimpan
            \Log::info('Data Riwayat Kelas: ', $riwayatData);
            
            // Catat riwayat perpindahan kelas
            RiwayatKelas::create($riwayatData);
        }
        
        return redirect()->back()->with('success', 'Siswa berhasil dipindahkan ke kelas baru secara massal');
    }

    /**
     * Mengambil daftar siswa berdasarkan kelas
     */
    public function getSiswaByKelas($id)
    {
        try {
            // Ambil tahun ajaran aktif
            $tahunAjaranAktif = \App\Models\TahunAjaran::where('aktif', true)->first();
            
            if (!$tahunAjaranAktif) {
                return response()->json(['error' => 'Tahun ajaran aktif belum ditentukan'], 400);
            }
            
            // Gunakan model PesertaDidik dan tabel riwayat_kelas
            $siswa = \App\Models\PesertaDidik::join('riwayat_kelas', 'peserta_didik.id', '=', 'riwayat_kelas.siswa_id')
                ->where('riwayat_kelas.kelas_baru_id', $id)
                ->where('riwayat_kelas.tahun_ajaran', $tahunAjaranAktif->nama)
                ->where('peserta_didik.status', 'aktif')
                ->select('peserta_didik.id', 'peserta_didik.nis', 'peserta_didik.nama')
                ->distinct()
                ->orderBy('peserta_didik.nama')
                ->get();
            
            \Log::info('Berhasil mengambil ' . $siswa->count() . ' siswa dari kelas ' . $id);
            
            return response()->json($siswa);
        } catch (\Exception $e) {
            \Log::error('Error saat mengambil data siswa: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    private function getTahunAjaranBerikutnya($tahunAjaranSaat)
    {
        // Format tahun ajaran diasumsikan seperti "2023/2024"
        $parts = explode('/', $tahunAjaranSaat);
        if (count($parts) == 2) {
            $tahunAwal = (int)$parts[0];
            $tahunAkhir = (int)$parts[1];
            
            return ($tahunAwal + 1) . '/' . ($tahunAkhir + 1);
        }
        
        // Fallback jika format berbeda
        return $tahunAjaranSaat;
    }

    // Tambahkan method alias
    public function kenaikan()
    {
        return $this->kenaikanView();
    }
//mutasi atau keluar
    public function mutasiKeluar(Request $request)
    {
        $request->validate([
            'siswa_id' => 'required|exists:peserta_didik,id',
            'alasan' => 'required|string',
            'tanggal_mutasi' => 'required|date',
            'sekolah_tujuan' => 'required|string',
        ]);
        
        $siswa = PesertaDidik::findOrFail($request->siswa_id);
        $kelasLama = $siswa->kelas_id;
        
        // Update status siswa
        $siswa->status = 'mutasi_keluar';
        $siswa->kelas_id = null;
        $siswa->save();
        
        // Dapatkan tahun ajaran aktif
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->first();
        $tahunAjaranNama = $tahunAjaranAktif ? $tahunAjaranAktif->nama : date('Y') . '/' . (date('Y') + 1);
        
        // Catat riwayat mutasi
        RiwayatKelas::create([
            'siswa_id' => $siswa->id,
            'kelas_lama_id' => $kelasLama,
            'kelas_baru_id' => null,
            'tahun_ajaran' => $request->tahun_ajaran ?? $tahunAjaranNama,
            'jenis_perpindahan' => 'mutasi_keluar',
            'tanggal_pindah' => $request->tanggal_mutasi,
            'alasan' => $request->alasan,
            'sekolah_tujuan' => $request->sekolah_tujuan, // Simpan sekolah tujuan di kolom yang benar
            'created_by' => auth()->id()
        ]);
        
        return redirect()->back()->with('success', 'Siswa berhasil dimutasi keluar');
    }

    /**
     * Proses kenaikan kelas untuk siswa terpilih
     */
    public function kenaikanKelasTerpilih(Request $request)
    {
        // Debug: log request data
        \Log::info('Kenaikan Kelas Terpilih Request:', $request->all());
        
        try {
            $request->validate([
                'kelas_asal_id' => 'required|exists:kelas,id',
                'kelas_tujuan_id' => 'required|exists:kelas,id',
                'tahun_ajaran' => 'required|string',
                'siswa_ids' => 'required|array',
                'siswa_ids.*' => 'exists:peserta_didik,id',
            ]);
            
            // Ambil data kelas tujuan
            $kelasTujuan = Kelas::findOrFail($request->kelas_tujuan_id);
            $jumlahSiswaNaik = 0;
            
            \Log::info('Memproses kenaikan kelas untuk ' . count($request->siswa_ids) . ' siswa');
            
            foreach ($request->siswa_ids as $siswaId) {
                try {
                    $siswa = PesertaDidik::findOrFail($siswaId);
                    $kelasLama = $siswa->kelas_id;
                    
                    \Log::info("Memproses siswa ID: {$siswaId}, Nama: {$siswa->nama}, Kelas lama: {$kelasLama}");
                    
                    // Update kelas siswa
                    $siswa->kelas_id = $request->kelas_tujuan_id;
                    $siswa->save();
                    
                    // Catat riwayat kenaikan kelas
                    $riwayat = RiwayatKelas::create([
                        'siswa_id' => $siswa->id,
                        'kelas_lama_id' => $kelasLama,
                        'kelas_baru_id' => $request->kelas_tujuan_id,
                        'tahun_ajaran' => $request->tahun_ajaran,
                        'jenis_perpindahan' => 'kenaikan_kelas',
                        'tanggal_pindah' => now(),
                        'alasan' => 'Kenaikan kelas terpilih',
                        'created_by' => auth()->id()
                    ]);
                    
                    \Log::info("Riwayat kelas berhasil dibuat dengan ID: {$riwayat->id}");
                    
                    $jumlahSiswaNaik++;
                } catch (\Exception $e) {
                    \Log::error("Error saat memproses siswa ID {$siswaId}: " . $e->getMessage());
                }
            }
            
            \Log::info("Total {$jumlahSiswaNaik} siswa berhasil dinaikkan ke kelas {$kelasTujuan->nama}");
            
            return redirect()->back()->with('success', $jumlahSiswaNaik . ' siswa berhasil dinaikkan ke kelas ' . $kelasTujuan->nama);
        } catch (\Exception $e) {
            \Log::error('Error pada kenaikan kelas terpilih: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    // Menampilkan halaman ekstrakurikuler
    public function eksul()
    {
        // Ambil data ekstrakurikuler
        $ekstrakurikuler = \App\Models\Ekstrakurikuler::with('unit')->get();
        
        // Ambil data siswa aktif
        $siswa = \App\Models\PesertaDidik::where('status', 'aktif')
                    ->orderBy('nama')
                    ->get();
        
        // Ambil data rombel ekstrakurikuler yang sudah ada (jika ada)
        // Hapus relasi ekstrakurikuler yang tidak ada
        $rombelEksul = \App\Models\RombelEkstrakurikuler::with(['anggota.siswa'])
                    ->get();
        
        return view('rombel.eksul.index', compact('ekstrakurikuler', 'siswa', 'rombelEksul'));
    }

    // Menyimpan rombel ekstrakurikuler baru
    public function storeEksul(Request $request)
    {
        $request->validate([
            'nama_rombel' => 'required|string|max:255',
            'ekstrakurikuler' => 'required|string|max:255', // Ubah nama field dan validasi
            'tahun_ajaran' => 'required|string|max:255',
            'pembina' => 'required|string|max:255',
            'siswa_ids' => 'required|array',
            'siswa_ids.*' => 'exists:peserta_didik,id'
        ]);
        
        // Buat rombel ekstrakurikuler baru
        $rombelEksul = \App\Models\RombelEkstrakurikuler::create([
            'nama_rombel' => $request->nama_rombel,
            'ekstrakurikuler' => $request->ekstrakurikuler, // Simpan sebagai teks
            'tahun_ajaran' => $request->tahun_ajaran,
            'pembina' => $request->pembina,
        ]);
        
        // Tambahkan anggota ke rombel
        foreach ($request->siswa_ids as $siswaId) {
            // Cari siswa untuk mendapatkan informasi kelas saat ini
            $siswa = \App\Models\PesertaDidik::find($siswaId);
            
            if (!$siswa) {
                \Log::warning("Siswa dengan ID {$siswaId} tidak ditemukan");
                continue;
            }
            
            // Coba dapatkan kelas dari riwayat kelas
            $riwayatKelas = \App\Models\RiwayatKelas::where('siswa_id', $siswaId)
                ->where('tahun_ajaran', $request->tahun_ajaran)
                ->orderBy('created_at', 'desc')
                ->first();
                
            // Log untuk debugging
            \Log::info("Mencari riwayat kelas untuk siswa ID: {$siswaId}, Nama: {$siswa->nama}, Tahun Ajaran: {$request->tahun_ajaran}");
            
            if ($riwayatKelas) {
                \Log::info("Riwayat kelas ditemukan: ID: {$riwayatKelas->id}, Kelas Baru ID: {$riwayatKelas->kelas_baru_id}");
                $kelasId = $riwayatKelas->kelas_baru_id;
            } else {
                // Jika tidak ada di riwayat, gunakan kelas_id dari tabel peserta_didik
                \Log::info("Riwayat kelas tidak ditemukan, menggunakan kelas_id dari peserta_didik: {$siswa->kelas_id}");
                $kelasId = $siswa->kelas_id;
            }
            
            // Buat anggota ekstrakurikuler dengan kelas yang ditemukan
            \App\Models\AnggotaEkstrakurikuler::create([
                'rombel_ekstrakurikuler_id' => $rombelEksul->id,
                'siswa_id' => $siswaId,
                'kelas_id' => $kelasId // Tambahkan kelas_id
            ]);
            
            \Log::info("Anggota ekstrakurikuler dibuat untuk siswa ID: {$siswaId} dengan kelas_id: {$kelasId}");
        }
        
        return redirect()->route('rombel.eksul')
            ->with('success', 'Rombongan belajar ekstrakurikuler berhasil dibuat');
    }

    // Menampilkan detail rombel ekstrakurikuler
    public function showEksul($id)
    {
        $rombelEksul = \App\Models\RombelEkstrakurikuler::with(['anggota.siswa'])
                    ->findOrFail($id);
        
        return view('rombel.eksul.show', compact('rombelEksul'));
    }

    // Menghapus rombel ekstrakurikuler
    public function destroyEksul($id)
    {
        $rombelEksul = \App\Models\RombelEkstrakurikuler::findOrFail($id);
        
        // Hapus semua anggota terlebih dahulu
        \App\Models\AnggotaEkstrakurikuler::where('rombel_ekstrakurikuler_id', $id)->delete();
        
        // Hapus rombel
        $rombelEksul->delete();
        
        return redirect()->route('rombel.eksul')
            ->with('success', 'Rombongan belajar ekstrakurikuler berhasil dihapus');
    }
}































