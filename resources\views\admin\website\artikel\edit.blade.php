@extends('adminlte::page')

@section('title', 'Edit Artikel')

@section('content_header')
    <h1>Edit Artikel</h1>
@stop

@section('content')
<div class="card">
    <div class="card-body">
        <form action="{{ route('admin.website.artikel.update', $artikel) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            
            <div class="form-group">
                <label for="title">Judul</label>
                <input type="text" 
                       class="form-control @error('title') is-invalid @enderror" 
                       id="title" 
                       name="title" 
                       value="{{ old('title', $artikel->title) }}" 
                       required>
                @error('title')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="excerpt">Ringkasan (opsional)</label>
                <textarea class="form-control @error('excerpt') is-invalid @enderror" 
                          id="excerpt" 
                          name="excerpt" 
                          rows="3">{{ old('excerpt', $artikel->excerpt) }}</textarea>
                @error('excerpt')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="content">Konten</label>
                <textarea class="form-control @error('content') is-invalid @enderror" 
                          id="content" 
                          name="content" 
                          rows="10">{{ old('content', $artikel->content) }}</textarea>
                @error('content')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="image">Gambar</label>
                <div class="mb-2">
                    <img src="{{ asset('storage/' . $artikel->image) }}" 
                         alt="{{ $artikel->title }}" 
                         class="img-thumbnail" 
                         style="max-height: 200px">
                </div>
                <input type="file" 
                       class="form-control-file @error('image') is-invalid @enderror" 
                       id="image" 
                       name="image" 
                       accept="image/*">
                <small class="form-text text-muted">Biarkan kosong jika tidak ingin mengubah gambar</small>
                @error('image')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="status">Status</label>
                <select class="form-control @error('status') is-invalid @enderror" 
                        id="status" 
                        name="status" 
                        required>
                    <option value="draft" {{ old('status', $artikel->status) === 'draft' ? 'selected' : '' }}>Draft</option>
                    <option value="published" {{ old('status', $artikel->status) === 'published' ? 'selected' : '' }}>Published</option>
                    <option value="archived" {{ old('status', $artikel->status) === 'archived' ? 'selected' : '' }}>Archived</option>
                </select>
                @error('status')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="published_at">Tanggal Publikasi</label>
                <input type="date" 
                       class="form-control @error('published_at') is-invalid @enderror" 
                       id="published_at" 
                       name="published_at" 
                       value="{{ old('published_at', $artikel->published_at ? $artikel->published_at->format('Y-m-d') : '') }}">
                <small class="form-text text-muted">Kosongkan jika ingin menggunakan tanggal saat ini (untuk status Published)</small>
                @error('published_at')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">Simpan</button>
                <a href="{{ route('admin.website.artikel.index') }}" class="btn btn-secondary">Batal</a>
            </div>
        </form>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <!-- Load TinyMCE -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/7.6.0/tinymce.min.js"></script>
    <script>
        tinymce.init({
            selector: '#content',
            height: 500,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | ' +
                'bold italic forecolor | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | ' +
                'removeformat | help',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:16px }',
            images_upload_url: '/upload/image', // Adjust this to your image upload endpoint
            automatic_uploads: true,
            images_reuse_filename: true,
            relative_urls: false,
            remove_script_host: false,
            convert_urls: true
        });
    </script>
@stop


