<?php

namespace App\Models;
 
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PenilaianSumatif extends Model
{
    use HasFactory;

    protected $fillable = [
        'siswa_id',
        'kelas_id',
        'mata_pelajaran_id',
        'kompetensi_dasar_id',
        'tanggal',
        'jenis_penilaian',
        'dimensi',
        'nilai_angka',
        'nilai_huruf',
        'deskripsi',
        'created_by',
    ];

    protected $casts = [
        'tanggal' => 'date',
    ];

    public function siswa()
    {
        return $this->belongsTo(PesertaDidik::class, 'siswa_id');
    }

    public function kelas()
    {
        return $this->belongsTo(Kelas::class);
    }

    public function mataPelajaran()
    {
        return $this->belongsTo(MataPelajaran::class);
    }

    public function kompetensiDasar()
    {
        return $this->belongsTo(KompetensiDasar::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}