<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
//use App\Models\User;

class AdmKepsek extends Model
{
    use HasFactory;

    protected $table = 'adm_kepsek';
    
    protected $fillable = [
        'user_id', 'unit_id', 'judul', 'keterangan', 'file_path', 'status',
        'approved_by', 'approved_at', 'rejected_by', 'rejected_at', 'alasan_penolakan'
    ];

    protected $dates = [
        'approved_at',
        'rejected_at',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class, 'unit_id');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function rejecter()
    {
        return $this->belongsTo(User::class, 'rejected_by');
    }
}




