<?php

namespace App\Http\Controllers;

use App\Models\LaporanKerusakan;
use App\Models\Notifikasi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LaporanKerusakanController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Ambil semua laporan kerusakan dan urutkan berdasarkan tanggal terbaru
        $laporanKerusakan = LaporanKerusakan::with('pelapor')
            ->orderBy('tanggal_lapor', 'desc')
            ->get();
            
        return view('laporan-kerusakan.index', compact('laporanKerusakan'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('laporan-kerusakan.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'judul' => 'required|string|max:255',
            'lokasi' => 'required|string|max:255',
            'deskripsi' => 'required|string',
        ]);
        
        // Buat laporan kerusakan baru
        $laporanKerusakan = LaporanKerusakan::create([
            'judul' => $validated['judul'],
            'lokasi' => $validated['lokasi'],
            'deskripsi' => $validated['deskripsi'],
            'status' => 'dilaporkan',
            'pelapor_id' => Auth::id(),
            'tanggal_lapor' => now(), // Eksplisit set tanggal_lapor
        ]);
        
        // Kirim notifikasi ke admin/petugas
        $this->kirimNotifikasiKeAdmin($laporanKerusakan);
        
        return redirect()->route('laporan-kerusakan.index')
            ->with('success', 'Laporan kerusakan berhasil dibuat.');
    }

    /**
     * Display the specified resource.
     */
    public function show(LaporanKerusakan $laporanKerusakan)
    {
        // Load relasi yang diperlukan
        $laporanKerusakan->load(['pelapor', 'penindak']);
        
        return view('laporan-kerusakan.show', compact('laporanKerusakan'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(LaporanKerusakan $laporanKerusakan)
    {
        return view('laporan-kerusakan.edit', compact('laporanKerusakan'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, LaporanKerusakan $laporanKerusakan)
    {
        $request->validate([
            'judul' => 'required|string|max:255',
            'lokasi' => 'required|string|max:255',
            'deskripsi' => 'required|string',
        ]);
        
        $laporanKerusakan->update([
            'judul' => $request->judul,
            'lokasi' => $request->lokasi,
            'deskripsi' => $request->deskripsi,
        ]);
        
        return redirect()->route('laporan-kerusakan.show', $laporanKerusakan)
            ->with('success', 'Laporan kerusakan berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(LaporanKerusakan $laporanKerusakan)
    {
        $laporanKerusakan->delete();
        
        return redirect()->route('laporan-kerusakan.index')
            ->with('success', 'Laporan kerusakan berhasil dihapus.');
    }
    
    /**
     * Proses laporan kerusakan.
     */
    public function proses(LaporanKerusakan $laporanKerusakan)
    {
        // Simpan tanggal_lapor yang asli
        $tanggalLaporAsli = $laporanKerusakan->tanggal_lapor;
        
        $laporanKerusakan->update([
            'status' => 'diproses',
            'penindak_id' => Auth::id(),
            'tanggal_proses' => now(),
        ]);
        
        // Pastikan tanggal_lapor tidak berubah
        if ($laporanKerusakan->tanggal_lapor != $tanggalLaporAsli) {
            $laporanKerusakan->tanggal_lapor = $tanggalLaporAsli;
            $laporanKerusakan->save();
        }
        
        // Kirim notifikasi ke pelapor
        $this->kirimNotifikasiKePelapor($laporanKerusakan, 'diproses');
        
        return redirect()->route('laporan-kerusakan.show', $laporanKerusakan)
            ->with('success', 'Laporan kerusakan sedang diproses.');
    }
    
    /**
     * Selesaikan laporan kerusakan.
     */
    public function selesai(Request $request, LaporanKerusakan $laporanKerusakan)
    {
        $request->validate([
            'tindakan' => 'required|string',
        ]);
        
        // Simpan tanggal_lapor dan tanggal_proses yang asli
        $tanggalLaporAsli = $laporanKerusakan->tanggal_lapor;
        $tanggalProsesAsli = $laporanKerusakan->tanggal_proses;
        
        $laporanKerusakan->update([
            'status' => 'selesai',
            'tindakan' => $request->tindakan,
            'tanggal_selesai' => now(),
        ]);
        
        // Pastikan tanggal_lapor dan tanggal_proses tidak berubah
        if ($laporanKerusakan->tanggal_lapor != $tanggalLaporAsli || 
            $laporanKerusakan->tanggal_proses != $tanggalProsesAsli) {
            
            $laporanKerusakan->tanggal_lapor = $tanggalLaporAsli;
            $laporanKerusakan->tanggal_proses = $tanggalProsesAsli;
            $laporanKerusakan->save();
        }
        
        // Kirim notifikasi ke pelapor
        $this->kirimNotifikasiKePelapor($laporanKerusakan, 'selesai');
        
        return redirect()->route('laporan-kerusakan.show', $laporanKerusakan)
            ->with('success', 'Laporan kerusakan telah diselesaikan.');
    }
    
    /**
     * Kirim notifikasi ke admin/petugas.
     */
    private function kirimNotifikasiKeAdmin(LaporanKerusakan $laporanKerusakan)
    {
        // Dapatkan semua user dengan permission kelola-laporan-kerusakan
        $admins = \App\Models\User::permission('kelola-laporan-kerusakan')->get();
        
        foreach ($admins as $admin) {
            Notifikasi::create([
                'user_id' => $admin->id,
                'judul' => 'Laporan Kerusakan Baru',
                'pesan' => "Laporan kerusakan baru: {$laporanKerusakan->judul} di {$laporanKerusakan->lokasi}",
                'jenis' => 'warning',
                'link' => route('laporan-kerusakan.show', $laporanKerusakan),
                'dibaca' => false,
            ]);
        }
    }
    
    /**
     * Kirim notifikasi ke pelapor.
     */
    private function kirimNotifikasiKePelapor(LaporanKerusakan $laporanKerusakan, $status)
    {
        $judul = '';
        $pesan = '';
        $jenis = '';
        
        if ($status === 'diproses') {
            $judul = 'Laporan Kerusakan Diproses';
            $pesan = "Laporan kerusakan Anda: {$laporanKerusakan->judul} sedang diproses oleh {$laporanKerusakan->penindak->name}";
            $jenis = 'info';
        } elseif ($status === 'selesai') {
            $judul = 'Laporan Kerusakan Selesai';
            $pesan = "Laporan kerusakan Anda: {$laporanKerusakan->judul} telah selesai ditangani";
            $jenis = 'success';
        }
        
        Notifikasi::create([
            'user_id' => $laporanKerusakan->pelapor_id,
            'judul' => $judul,
            'pesan' => $pesan,
            'jenis' => $jenis,
            'link' => route('laporan-kerusakan.show', $laporanKerusakan),
            'dibaca' => false,
        ]);
    }
}




