<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AdmWaka;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class UpdateAdmWakaUnitIdSeeder extends Seeder
{
    public function run()
    {
        // Get all ADM Waka records without unit_id
        $admWakas = AdmWaka::whereNull('unit_id')->get();
        
        foreach ($admWakas as $adm) {
            // Find the user's unit_id
            $user = User::find($adm->user_id);
            
            if ($user && $user->unit_id) {
                // Update the ADM Waka record with the user's unit_id
                $adm->update(['unit_id' => $user->unit_id]);
            }
        }
        
        $this->command->info('Updated unit_id for ' . count($admWakas) . ' ADM Waka records.');
    }
}