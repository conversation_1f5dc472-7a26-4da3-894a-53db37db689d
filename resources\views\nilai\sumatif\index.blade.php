@extends('adminlte::page')

@section('title', 'Daftar Penilaian Sumatif')

@section('content_header')
    <h1>Daftar Penilaian Sumatif</h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Penilaian Sumatif</h3>
            <div class="card-tools">
                <a href="{{ route('penilaian.sumatif.create') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Tambah Penilaian Sumatif
                </a>
            </div>
        </div>
        <div class="card-body">
            <!-- Pesan Sukses -->
            @if(session('success'))
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h5><i class="icon fas fa-check"></i> Sukses!</h5>
                    {{ session('success') }}
                </div>
            @endif

            <div class="table-responsive">
                <table id="sumatifTable" class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Siswa</th>
                            <th>Kelas</th>
                            <th>Mata Pelajaran</th>
                            <th>Tanggal</th>
                            <th>Nilai</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($penilaianSumatif as $index => $ps)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $ps->siswa->nama ?? 'N/A' }}</td>
                                <td>{{ $ps->kelas->nama ?? 'N/A' }}</td>
                                <td>{{ $ps->mataPelajaran->nama ?? 'N/A' }}</td>
                                <td>{{ $ps->tanggal }}</td>
                                <td>{{ $ps->nilai_angka }} ({{ $ps->nilai_huruf }})</td>
                                <td>
                                    <a href="{{ route('penilaian.sumatif.show', $ps->id) }}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('penilaian.sumatif.edit', $ps->id) }}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('penilaian.sumatif.destroy', $ps->id) }}" method="POST" style="display: inline-block;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Apakah Anda yakin ingin menghapus data ini?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center">Tidak ada data penilaian sumatif</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-3">
                {{ $penilaianSumatif->links() }}
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            // Inisialisasi Select2 untuk dropdown
            $('select').select2({
                theme: 'bootstrap4',
                width: '100%'
            });
            
            // Inisialisasi DataTables
            $('#sumatifTable').DataTable({
                "paging": false, // Nonaktifkan paging karena sudah menggunakan pagination Laravel
                "lengthChange": false,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                }
            });
        });
    </script>
@stop