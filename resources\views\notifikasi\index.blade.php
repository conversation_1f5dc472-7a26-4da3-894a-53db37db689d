@extends('adminlte::page')

@section('title', 'Notifikasi')

@section('content_header')
    <h1>Notifikasi</h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Daftar Notifikasi</h3>
                    <div class="card-tools">
                        <form action="{{ route('notifikasi.mark-all-as-read') }}" method="GET" class="d-inline">
                            <button type="submit" class="btn btn-sm btn-primary">
                                <i class="fas fa-check-double"></i> Tandai Semua Dibaca
                            </button>
                        </form>
                        <form action="{{ route('notifikasi.destroy-all') }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus semua notifikasi?')">
                                <i class="fas fa-trash"></i> Hapus Semua
                            </button>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif
                    
                    @if(session('error'))
                        <div class="alert alert-danger">{{ session('error') }}</div>
                    @endif
                    
                    @if($notifikasi->isEmpty())
                        <div class="text-center py-3">
                            <i class="fas fa-bell-slash fa-4x text-muted mb-3"></i>
                            <p class="text-muted">Tidak ada notifikasi</p>
                        </div>
                    @else
                        <div class="list-group">
                            @foreach($notifikasi as $item)
                                <div class="list-group-item list-group-item-action {{ $item->dibaca ? '' : 'list-group-item-light' }}">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1">
                                            @if($item->jenis == 'warning')
                                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                            @elseif($item->jenis == 'success')
                                                <i class="fas fa-check-circle text-success"></i>
                                            @elseif($item->jenis == 'danger')
                                                <i class="fas fa-times-circle text-danger"></i>
                                            @else
                                                <i class="fas fa-info-circle text-info"></i>
                                            @endif
                                            {{ $item->judul }}
                                        </h5>
                                        <small>{{ $item->created_at->diffForHumans() }}</small>
                                    </div>
                                    <p class="mb-1">{{ $item->pesan }}</p>
                                    <div class="d-flex justify-content-between align-items-center mt-2">
                                        <div>
                                            @if(!$item->dibaca)
                                                <span class="badge badge-primary">Belum dibaca</span>
                                            @endif
                                        </div>
                                        <div>
                                            <a href="{{ route('notifikasi.mark-as-read', $item) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> Lihat
                                            </a>
                                            <form action="{{ route('notifikasi.destroy', $item) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus notifikasi ini?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        
                        <div class="mt-3">
                            {{ $notifikasi->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
<style>
    .list-group-item {
        margin-bottom: 10px;
        border-radius: 5px;
    }
</style>
@stop

@section('js')
<script>
    $(document).ready(function() {
        // Script tambahan jika diperlukan
    });
</script>
@stop