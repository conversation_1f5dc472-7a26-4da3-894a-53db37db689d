<?php

namespace App\Http\Controllers;

use App\Models\KalenderPendidikan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class KalenderPendidikanController extends Controller
{
    /**
     * Menampilkan daftar event dalam format JSON untuk FullCalendar
     */
    public function getEvents()
    {
        $events = KalenderPendidikan::all();
        
        $formattedEvents = $events->map(function($event) {
            return [
                'id' => $event->id,
                'title' => $event->judul,
                'start' => $event->tanggal_mulai->format('Y-m-d'),
                'end' => $event->tanggal_selesai->format('Y-m-d'),
                'description' => $event->deskripsi,
                'backgroundColor' => $event->warna,
                'borderColor' => $event->warna,
                'allDay' => true
            ];
        });
        
        return response()->json($formattedEvents);
    }
    
    /**
     * Menyimpan event baru
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'judul' => 'required|string|max:255',
            'tanggal_mulai' => 'required|date',
            'tanggal_selesai' => 'required|date|after_or_equal:tanggal_mulai',
            'deskripsi' => 'nullable|string',
            'warna' => 'required|string'
        ]);
        
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        
        KalenderPendidikan::create([
            'judul' => $request->judul,
            'tanggal_mulai' => $request->tanggal_mulai,
            'tanggal_selesai' => $request->tanggal_selesai,
            'deskripsi' => $request->deskripsi,
            'warna' => $request->warna,
            'created_by' => Auth::id()
        ]);
        
        return redirect()->back()->with('success', 'Event kalender berhasil ditambahkan');
    }
    
    /**
     * Menampilkan form edit event
     */
    public function edit($id)
    {
        $event = KalenderPendidikan::findOrFail($id);
        return view('kalender.edit', compact('event'));
    }
    
    /**
     * Mengupdate event
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'judul' => 'required|string|max:255',
            'tanggal_mulai' => 'required|date',
            'tanggal_selesai' => 'required|date|after_or_equal:tanggal_mulai',
            'deskripsi' => 'nullable|string',
            'warna' => 'required|string'
        ]);
        
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        
        $event = KalenderPendidikan::findOrFail($id);
        $event->update([
            'judul' => $request->judul,
            'tanggal_mulai' => $request->tanggal_mulai,
            'tanggal_selesai' => $request->tanggal_selesai,
            'deskripsi' => $request->deskripsi,
            'warna' => $request->warna
        ]);
        
        return redirect()->route('admin.dashboard')->with('success', 'Event kalender berhasil diperbarui');
    }
    
    /**
     * Menghapus event
     */
    public function destroy($id)
    {
        $event = KalenderPendidikan::findOrFail($id);
        $event->delete();
        
        return response()->json(['success' => true]);
    }
}