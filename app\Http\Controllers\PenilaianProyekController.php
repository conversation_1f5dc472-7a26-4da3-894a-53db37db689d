<?php

namespace App\Http\Controllers;

use App\Models\PenilaianProyek;
use App\Models\PenilaianProyekSiswa;
use App\Models\PesertaDidik;
use App\Models\Kelas;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PenilaianProyekController extends Controller
{
    /**
     * Menampilkan daftar penilaian proyek
     */
    public function index()
    {
        // Ambil data penilaian proyek dengan relasi kelas
        $penilaianProyek = PenilaianProyek::with('kelas')
            ->latest()
            ->paginate(10);
            
        return view('nilai.proyek.index', compact('penilaianProyek'));
    }

    /**
     * Menampilkan form untuk membuat penilaian proyek baru
     */
    public function create()
    {
        // Ambil data kelas untuk dropdown
        $kelas = Kelas::all();
        
        return view('nilai.proyek.create', compact('kelas'));
    }

    /**
     * Menyimpan penilaian proyek baru ke database
     */
    public function store(Request $request)
    {
        // Validasi input
        $validated = $request->validate([
            'nama_proyek' => 'required|string|max:255',
            'deskripsi_proyek' => 'required|string',
            'kelas_id' => 'required|exists:kelas,id',
            'tanggal_mulai' => 'required|date',
            'tanggal_selesai' => 'required|date|after_or_equal:tanggal_mulai',
            'dimensi_penilaian' => 'required|string',
        ]);
        
        // Tambahkan user yang membuat
        $validated['created_by'] = Auth::id();
        
        // Simpan data
        $penilaianProyek = PenilaianProyek::create($validated);
        
        return redirect()->route('penilaian.proyek.index')
            ->with('success', 'Penilaian proyek berhasil ditambahkan');
    }

    /**
     * Menampilkan detail penilaian proyek
     */
    public function show(PenilaianProyek $penilaianProyek)
    {
        // Ambil data siswa yang sudah dinilai
        $penilaianSiswa = $penilaianProyek->penilaianSiswa()
            ->with('siswa')
            ->get();
            
        return view('nilai.proyek.show', compact('penilaianProyek', 'penilaianSiswa'));
    }

    /**
     * Menampilkan form untuk mengedit penilaian proyek
     */
    public function edit(PenilaianProyek $penilaianProyek)
    {
        // Ambil data kelas untuk dropdown
        $kelas = Kelas::all();
        
        return view('nilai.proyek.edit', compact('penilaianProyek', 'kelas'));
    }

    /**
     * Memperbarui penilaian proyek di database
     */
    public function update(Request $request, PenilaianProyek $penilaianProyek)
    {
        // Validasi input
        $validated = $request->validate([
            'nama_proyek' => 'required|string|max:255',
            'deskripsi_proyek' => 'required|string',
            'kelas_id' => 'required|exists:kelas,id',
            'tanggal_mulai' => 'required|date',
            'tanggal_selesai' => 'required|date|after_or_equal:tanggal_mulai',
            'dimensi_penilaian' => 'required|string',
        ]);
        
        // Perbarui data
        $penilaianProyek->update($validated);
        
        return redirect()->route('penilaian.proyek.index')
            ->with('success', 'Penilaian proyek berhasil diperbarui');
    }

    /**
     * Menghapus penilaian proyek dari database
     */
    public function destroy(PenilaianProyek $penilaianProyek)
    {
        try {
            DB::beginTransaction();
            
            // Hapus semua penilaian siswa terkait
            $penilaianProyek->penilaianSiswa()->delete();
            
            // Hapus penilaian proyek
            $penilaianProyek->delete();
            
            DB::commit();
            
            return redirect()->route('penilaian.proyek.index')
                ->with('success', 'Penilaian proyek berhasil dihapus');
        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->route('penilaian.proyek.index')
                ->with('error', 'Terjadi kesalahan saat menghapus penilaian proyek: ' . $e->getMessage());
        }
    }

    /**
     * Menampilkan form untuk menilai siswa dalam proyek
     */
    public function nilaiSiswa(PenilaianProyek $penilaianProyek)
    {
        // Ambil data siswa dari kelas yang terkait dengan proyek
        $siswa = PesertaDidik::where('kelas_id', $penilaianProyek->kelas_id)
            ->where('is_active', true)
            ->get();
            
        // Ambil data penilaian yang sudah ada
        $penilaianSiswa = $penilaianProyek->penilaianSiswa()
            ->pluck('nilai', 'siswa_id')
            ->toArray();
            
        return view('nilai.proyek.nilai-siswa', compact(
            'penilaianProyek', 
            'siswa', 
            'penilaianSiswa'
        ));
    }

    /**
     * Menyimpan penilaian siswa untuk proyek
     */
    public function simpanNilaiSiswa(Request $request, PenilaianProyek $penilaianProyek)
    {
        $request->validate([
            'nilai.*' => 'required|numeric|min:0|max:100',
            'deskripsi.*' => 'nullable|string',
        ]);
        
        try {
            DB::beginTransaction();
            
            foreach ($request->nilai as $siswaId => $nilai) {
                // Cek apakah penilaian sudah ada
                $penilaian = PenilaianProyekSiswa::updateOrCreate(
                    [
                        'penilaian_proyek_id' => $penilaianProyek->id,
                        'siswa_id' => $siswaId,
                    ],
                    [
                        'nilai' => $nilai,
                        'deskripsi' => $request->deskripsi[$siswaId] ?? null,
                    ]
                );
            }
            
            DB::commit();
            
            return redirect()->route('penilaian.proyek.show', $penilaianProyek->id)
                ->with('success', 'Penilaian siswa berhasil disimpan');
        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan saat menyimpan penilaian: ' . $e->getMessage())
                ->withInput();
        }
    }
}
