<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Guru; // Pastikan model Guru diimport
use App\Models\Unit;
use App\Traits\FiltersByUserUnit; // Import trait untuk filter unit
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\PengembanganDiri;
use App\Models\TugasTambahan;
use App\Models\TahunAjaran;

class TendikController extends Controller
{
    use FiltersByUserUnit; // Gunakan trait untuk filter unit
    
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->user();
        $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
        
        // Query untuk mengambil hanya data dengan jenis_ptk 'Tenaga Kependidikan'
        $query = Guru::with('unit')
                    ->where('jenis_ptk', 'Tenaga Kependidikan')
                    ->where('status', 'Aktif');
        
        // Terapkan filter unit jika user tidak memiliki akses ke semua unit
        if (!$canViewAllUnits && $user->unit_id) {
            $query->where('unit_id', $user->unit_id);
            
            // Log untuk debugging
            Log::info('Filtering tendik by unit', [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'unit_id' => $user->unit_id,
                'roles' => $user->getRoleNames(),
                'query_sql' => $query->toSql(),
                'query_bindings' => $query->getBindings()
            ]);
        }
        
        $tendik = $query->get();
        
        // Debug: log jumlah data yang ditemukan
        Log::info('Tendik data retrieved', [
            'count' => $tendik->count(),
            'user_unit_id' => $user->unit_id,
            'can_view_all_units' => $canViewAllUnits
        ]);
        
        // Ambil daftar unit untuk filter (jika diperlukan)
        $units = Unit::orderBy('nama_unit')->get();
        
        return view('gtk.tendik.index', compact('tendik', 'units'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Ambil data yang diperlukan untuk form
        $user = auth()->user();
        $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
        
        // Jika user dapat melihat semua unit, tampilkan semua unit
        // Jika tidak, hanya tampilkan unit user tersebut
        if ($canViewAllUnits) {
            $units = Unit::all();
        } else {
            $units = Unit::where('id', $user->unit_id)->get();
        }
        
        return view('gtk.tendik.create', compact('units'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Validasi input dengan menambahkan nik sebagai field wajib
        $validated = $request->validate([
            'nama' => 'required|string|max:255',
            'unit_id' => 'required|exists:units,id',
            'nik' => 'required|string|max:16|unique:guru,nik', // Tambahkan validasi NIK
            'jenis_kelamin' => 'required|in:L,P', // Tambahkan validasi jenis kelamin
            'tempat_lahir' => 'nullable|string|max:100',
            'tanggal_lahir' => 'nullable|date',
            'alamat' => 'nullable|string',
            'status' => 'required|in:Aktif,Non-Aktif', // Tambahkan validasi status
            // Tambahkan validasi lainnya sesuai kebutuhan
        ]);
        
        // Set jenis_ptk ke 'Tenaga Kependidikan'
        $validated['jenis_ptk'] = 'Tenaga Kependidikan';
        
        try {
            // Simpan data dengan menggunakan DB transaction
            \DB::beginTransaction();
            
            $tendik = Guru::create($validated);
            
            \DB::commit();
            
            return redirect()->route('gtk.tendik.index')
                             ->with('success', 'Data tenaga kependidikan berhasil ditambahkan');
        } catch (\Exception $e) {
            \DB::rollBack();
            
            // Log error untuk debugging
            \Log::error('Error saat menyimpan tendik: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            
            return redirect()->back()
                             ->withInput()
                             ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $tendik = Guru::with('unit')->findOrFail($id);
        
        // Cek apakah user memiliki akses ke data ini
        $user = auth()->user();
        $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
        
        if (!$canViewAllUnits && $user->unit_id != $tendik->unit_id) {
            return redirect()->route('gtk.tendik.index')
                             ->with('error', 'Anda tidak memiliki akses untuk melihat data ini');
        }
        
        // Ambil data pengembangan diri
        $pengembanganDiri = PengembanganDiri::where('guru_id', $id)->get();
        
        // Ambil data tugas tambahan
        $tugasTambahan = TugasTambahan::with('tahunAjaran')
                        ->where('guru_id', $id)
                        ->get();
        
        // Ambil daftar tahun ajaran untuk dropdown
        $tahunAjaranList = TahunAjaran::orderBy('aktif', 'desc')
                      ->orderBy('tanggal_mulai', 'desc')
                      ->get();
        
        // Dapatkan ID tahun ajaran aktif
        $tahunAjaranAktif = TahunAjaran::where('aktif', 1)->first();
        $tahunAjaranAktifId = $tahunAjaranAktif ? $tahunAjaranAktif->id : null;
        
        return view('gtk.tendik.show', compact(
            'tendik', 
            'pengembanganDiri', 
            'tugasTambahan', 
            'tahunAjaranList',
            'tahunAjaranAktifId'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $tendik = Guru::findOrFail($id);
        
        // Cek apakah user memiliki akses ke data ini
        $user = auth()->user();
        $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
        
        if (!$canViewAllUnits && $user->unit_id != $tendik->unit_id) {
            return redirect()->route('gtk.tendik.index')
                             ->with('error', 'Anda tidak memiliki akses untuk mengedit data ini');
        }
        
        // Ambil data unit untuk dropdown
        if ($canViewAllUnits) {
            $units = Unit::all();
        } else {
            $units = Unit::where('id', $user->unit_id)->get();
        }
        
        return view('gtk.tendik.edit', compact('tendik', 'units'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $tendik = Guru::findOrFail($id);
        
        // Cek apakah user memiliki akses ke data ini
        $user = auth()->user();
        $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
        
        if (!$canViewAllUnits && $user->unit_id != $tendik->unit_id) {
            return redirect()->route('gtk.tendik.index')
                             ->with('error', 'Anda tidak memiliki akses untuk mengupdate data ini');
        }
        
        // Validasi input
        $validated = $request->validate([
            'nama' => 'required|string|max:255',
            'unit_id' => 'required|exists:units,id',
            // Tambahkan validasi lainnya sesuai kebutuhan
        ]);
        
        // Pastikan jenis_ptk tetap 'Tenaga Kependidikan'
        $validated['jenis_ptk'] = 'Tenaga Kependidikan';
        
        // Update data
        $tendik->update($validated);
        
        return redirect()->route('gtk.tendik.index')
                         ->with('success', 'Data tenaga kependidikan berhasil diupdate');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $tendik = Guru::findOrFail($id);
        
        // Cek apakah user memiliki akses ke data ini
        $user = auth()->user();
        $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
        
        if (!$canViewAllUnits && $user->unit_id != $tendik->unit_id) {
            return redirect()->route('gtk.tendik.index')
                             ->with('error', 'Anda tidak memiliki akses untuk menghapus data ini');
        }
        
        // Hapus data
        $tendik->delete();
        
        return redirect()->route('gtk.tendik.index')
                         ->with('success', 'Data tenaga kependidikan berhasil dihapus');
    }

    /**
     * Store a newly created pengembangan diri resource.
     */
    public function storePengembanganDiri(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'jenis_diklat' => 'required|string|max:255',
                'nama' => 'required|string|max:255',
                'penyelenggara' => 'required|string|max:255',
                'tingkat' => 'required|string|max:255',
                'tahun' => 'required|integer|min:1900|max:' . (date('Y') + 1),
                'peran' => 'required|string|max:255',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            // Cek apakah guru/tendik ada
            $tendik = Guru::findOrFail($id);
            
            // Simpan data pengembangan diri
            PengembanganDiri::create([
                'guru_id' => $id,
                'jenis_diklat' => $request->jenis_diklat,
                'nama' => $request->nama,
                'penyelenggara' => $request->penyelenggara,
                'tingkat' => $request->tingkat,
                'tahun' => $request->tahun,
                'peran' => $request->peran,
            ]);

            return redirect()->route('gtk.tendik.show', $id)
                ->with('success', 'Data pengembangan diri berhasil ditambahkan');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Update the specified pengembangan diri resource.
     */
    public function updatePengembanganDiri(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'jenis_diklat' => 'required|string|max:255',
                'nama' => 'required|string|max:255',
                'penyelenggara' => 'required|string|max:255',
                'tingkat' => 'required|string|max:255',
                'tahun' => 'required|integer|min:1900|max:' . (date('Y') + 1),
                'peran' => 'required|string|max:255',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            // Cari data pengembangan diri
            $pengembanganDiri = PengembanganDiri::findOrFail($id);
            
            // Update data
            $pengembanganDiri->update([
                'jenis_diklat' => $request->jenis_diklat,
                'nama' => $request->nama,
                'penyelenggara' => $request->penyelenggara,
                'tingkat' => $request->tingkat,
                'tahun' => $request->tahun,
                'peran' => $request->peran,
            ]);

            return redirect()->route('gtk.tendik.show', $pengembanganDiri->guru_id)
                ->with('success', 'Data pengembangan diri berhasil diperbarui');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified pengembangan diri resource.
     */
    public function destroyPengembanganDiri($id)
    {
        try {
            $pengembanganDiri = PengembanganDiri::findOrFail($id);
            $guru_id = $pengembanganDiri->guru_id;
            
            $pengembanganDiri->delete();
            
            return redirect()->route('gtk.tendik.show', $guru_id)
                ->with('success', 'Data pengembangan diri berhasil dihapus');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Store a newly created tugas tambahan resource.
     */
    public function storeTugasTambahan(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'tugas_tambahan' => 'required|string|max:255',
                'tahun_ajaran_id' => 'required|exists:tahun_ajaran,id',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            // Cek apakah guru/tendik ada
            $tendik = Guru::findOrFail($id);
            
            // Simpan data tugas tambahan
            TugasTambahan::create([
                'guru_id' => $id,
                'tugas_tambahan' => $request->tugas_tambahan,
                'tahun_ajaran_id' => $request->tahun_ajaran_id,
            ]);

            return redirect()->route('gtk.tendik.show', $id)
                ->with('success', 'Tugas tambahan berhasil ditambahkan');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Update the specified tugas tambahan resource.
     */
    public function updateTugasTambahan(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'tugas_tambahan' => 'required|string|max:255',
                'tahun_ajaran_id' => 'required|exists:tahun_ajaran,id',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            // Cari data tugas tambahan
            $tugasTambahan = TugasTambahan::findOrFail($id);
            
            // Update data
            $tugasTambahan->update([
                'tugas_tambahan' => $request->tugas_tambahan,
                'tahun_ajaran_id' => $request->tahun_ajaran_id,
            ]);

            return redirect()->route('gtk.tendik.show', $tugasTambahan->guru_id)
                ->with('success', 'Tugas tambahan berhasil diperbarui');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified tugas tambahan resource.
     */
    public function destroyTugasTambahan($id)
    {
        try {
            $tugasTambahan = TugasTambahan::findOrFail($id);
            $guru_id = $tugasTambahan->guru_id;
            
            $tugasTambahan->delete();
            
            return redirect()->route('gtk.tendik.show', $guru_id)
                ->with('success', 'Tugas tambahan berhasil dihapus');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }
}


