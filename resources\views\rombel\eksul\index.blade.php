@extends('adminlte::page')

@section('title', 'Rombongan Belajar Ekstrakurikuler')

@section('content_header')
    <h1>Rombongan Belajar Ekstrakurikuler</h1>
@stop

@section('content')
<div class="row">
    <div class="col-md-12">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h5><i class="icon fas fa-check"></i> Sukses!</h5>
                {{ session('success') }}
            </div>
        @endif
    </div>
</div>

<div class="row">
    <!-- Form Tambah Rombel Ekstrakurikuler -->
    <div class="col-md-4">
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">Tambah Rombel Ekstrakurikuler</h3>
            </div>
            <form action="{{ route('rombel.eksul.store') }}" method="POST">
                @csrf
                <div class="card-body">
                    <div class="form-group">
                        <label for="nama_rombel">Nama Rombel</label>
                        <input type="text" class="form-control @error('nama_rombel') is-invalid @enderror" 
                               id="nama_rombel" name="nama_rombel" placeholder="Masukkan nama rombel" 
                               value="{{ old('nama_rombel') }}" required>
                        @error('nama_rombel')
                            <span class="invalid-feedback">{{ $message }}</span>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="ekstrakurikuler_id">Ekstrakurikuler</label>
                        <input type="text" class="form-control @error('ekstrakurikuler') is-invalid @enderror" 
                               id="ekstrakurikuler" name="ekstrakurikuler" placeholder="Masukkan nama ekstrakurikuler" 
                               value="{{ old('ekstrakurikuler') }}" required>
                        @error('ekstrakurikuler')
                            <span class="invalid-feedback">{{ $message }}</span>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="tahun_ajaran">Tahun Ajaran</label>
                        <input type="text" class="form-control @error('tahun_ajaran') is-invalid @enderror" 
                               id="tahun_ajaran" name="tahun_ajaran" placeholder="Contoh: 2023/2024" 
                               value="{{ old('tahun_ajaran') }}" required>
                        @error('tahun_ajaran')
                            <span class="invalid-feedback">{{ $message }}</span>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="pembina">Pembina</label>
                        <input type="text" class="form-control @error('pembina') is-invalid @enderror" 
                               id="pembina" name="pembina" placeholder="Nama pembina" 
                               value="{{ old('pembina') }}" required>
                        @error('pembina')
                            <span class="invalid-feedback">{{ $message }}</span>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="siswa_ids">Pilih Siswa</label>
                        <select class="form-control select2 @error('siswa_ids') is-invalid @enderror" 
                                id="siswa_ids" name="siswa_ids[]" multiple required>
                            @foreach($siswa as $s)
                                <option value="{{ $s->id }}">
                                    {{ $s->nama }} ({{ $s->nis ?? 'Belum ada NIS' }})
                                </option>
                            @endforeach
                        </select>
                        @error('siswa_ids')
                            <span class="invalid-feedback">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Daftar Rombel Ekstrakurikuler -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Daftar Rombel Ekstrakurikuler</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Nama Rombel</th>
                            <th>Ekstrakurikuler</th>
                            <th>Pembina</th>
                            <th>Jumlah Anggota</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($rombelEksul as $key => $rombel)
                            <tr>
                                <td>{{ $key + 1 }}</td>
                                <td>{{ $rombel->nama_rombel }}</td>
                                <td>{{ $rombel->ekstrakurikuler }}</td>
                                <td>{{ $rombel->pembina }}</td>
                                <td>{{ $rombel->anggota->count() }} siswa</td>
                                <td>
                                    <a href="{{ route('rombel.eksul.show', $rombel->id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> Detail
                                    </a>
                                    <form action="{{ route('rombel.eksul.destroy', $rombel->id) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus rombel ini?')">
                                            <i class="fas fa-trash"></i> Hapus
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center">Belum ada data rombel ekstrakurikuler</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="{{ asset('css/admin_custom.css') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css" />
@stop

@section('js')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Inisialisasi Select2
            $('.select2').select2({
                theme: 'bootstrap4',
                placeholder: 'Pilih siswa',
                allowClear: true
            });
        });
    </script>
@stop

