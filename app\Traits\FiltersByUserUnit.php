<?php

namespace App\Traits;

trait FiltersByUserUnit
{
    /**
     * Menerapkan filter berdasarkan unit pengguna pada query
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $relationPath Path relasi ke unit_id (contoh: 'kelas')
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function applyUnitFilter($query, $relationPath = null)
    {
        $user = auth()->user();
        
        // Administrator, <PERSON><PERSON><PERSON>, dan <PERSON>gawas dapat melihat semua data
        if (!$user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas'])) {
            if ($relationPath) {
                // Filter melalui relasi
                $query->whereHas($relationPath, function($q) use ($user) {
                    $q->where('unit_id', $user->unit_id);
                });
            } else {
                // Filter langsung pada tabel utama
                $query->where('unit_id', $user->unit_id);
            }
        }
        
        return $query;
    }
}