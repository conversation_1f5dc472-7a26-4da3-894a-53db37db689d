@extends('layouts.website')
@section('title', 'Prestasi')
@section('content')
<div class="container py-5">    <h1 class="mb-4">Prestasi</h1>
        <div class="row">
        @foreach($achievements as $achievement)        <div class="col-md-4 mb-4">
            <div class="card h-100">                @if($achievement->image)
                    <img src="{{ Storage::url($achievement->image) }}" class="card-img-top" alt="{{ $achievement->title }}">                @endif
                <div class="card-body">                    <h5 class="card-title">{{ $achievement->title }}</h5>
                    <p class="text-muted">                        <i class="fas fa-calendar"></i> 
                       <!-- {{ $achievement->tanggal ? $achievement->tanggal->format('d M Y') : '-' }}           -->         </p>
                    <p class="text-muted">                        <i class="fas fa-trophy"></i> 
                        {{ $achievement->level }}                    </p>
                    <p class="text-muted">                        <i class="fas fa-user"></i> 
                        {{ $achievement->participant }}                    </p>
                    <p class="card-text">{{ Str::limit($achievement->description, 100) }}</p>                </div>
            </div>        </div>
        @endforeach    </div>
    <div class="d-flex justify-content-center mt-4">
        {{ $achievements->links() }}    </div>
</div>
@endsection





















@extends('layouts.website')

@section('title', 'Prestasi')

@section('content')
<div class="container py-5">
    <h1 class="mb-4">Prestasi</h1>
    
    <div class="row">
        @foreach($achievements as $achievement)
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                @if($achievement->image)
                    <img src="{{ Storage::url($achievement->image) }}" class="card-img-top" alt="{{ $achievement->title }}">
                @endif
                <div class="card-body">
                    <h5 class="card-title">{{ $achievement->title }}</h5>
                    <p class="text-muted">
                        <i class="fas fa-calendar"></i> 
                        {{ $achievement->tanggal ? $achievement->tanggal->format('d M Y') : '-' }}
                    </p>
                    <p class="text-muted">
                        <i class="fas fa-trophy"></i> 
                        {{ $achievement->level }}
                    </p>
                    <p class="text-muted">
                        <i class="fas fa-user"></i> 
                        {{ $achievement->participant }}
                    </p>
                    <p class="card-text">{{ Str::limit($achievement->description, 100) }}</p>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <div class="d-flex justify-content-center mt-4">
        {{ $achievements->links() }}
    </div>
</div>
@endsection
