<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lihat Dokumen KTSP</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }
        #document-container {
            width: 100%;
            height: 100vh;
            position: relative;
        }
        #document-viewer {
            width: 100%;
            height: 100%;
            border: none;
            position: absolute;
            top: 0;
            left: 0;
        }
        .not-supported {
            padding: 20px;
            text-align: center;
        }
        .download-link {
            display: block;
            margin: 20px auto;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div id="document-container">
        @php
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            $isPdf = strtolower($extension) === 'pdf';
            $isOffice = in_array(strtolower($extension), ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']);
            // Buat URL absolut untuk file
            $absoluteUrl = url(Storage::url('adm_ktsp/' . $filename));
        @endphp

        @if($isPdf)
            <object
                id="document-viewer"
                data="{{ route('adm.ktsp.view', ['filename' => $filename]) }}"
                type="application/pdf"
            >
                <div class="not-supported">
                    <p>Browser Anda tidak mendukung tampilan PDF. Silakan unduh file untuk melihatnya.</p>
                    <a href="{{ route('adm.ktsp.download', ['filename' => $filename]) }}" class="download-link">Unduh File</a>
                </div>
            </object>
        @elseif($isOffice)
            <iframe 
                id="document-viewer"
                src="https://view.officeapps.live.com/op/embed.aspx?src={{ urlencode($absoluteUrl) }}"
                frameborder="0"
            >
                <div class="not-supported">
                    <p>Browser Anda tidak mendukung tampilan dokumen Office. Silakan unduh file untuk melihatnya.</p>
                    <a href="{{ route('adm.ktsp.download', ['filename' => $filename]) }}" class="download-link">Unduh File</a>
                </div>
            </iframe>
        @else
            <div class="not-supported">
                <p>Format file tidak didukung untuk ditampilkan secara langsung. Silakan unduh file untuk melihatnya.</p>
                <a href="{{ route('adm.ktsp.download', ['filename' => $filename]) }}" class="download-link">Unduh File</a>
            </div>
        @endif
    </div>
</body>
</html>
