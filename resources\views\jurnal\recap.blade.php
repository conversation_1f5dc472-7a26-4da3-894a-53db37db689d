@extends('adminlte::page')

@section('title', 'Rekap Jurnal Kegiatan')

@section('content_header')
    <h1>Rekap Jurnal Kegiatan</h1>
@stop

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Filter Rekap Jurnal</h3>
            </div>
            <div class="card-body">
                <form action="{{ route('jurnal.recap') }}" method="GET" class="form-inline">
                    <div class="form-group mr-2">
                        <label for="start_date" class="mr-2"><PERSON><PERSON>:</label>
                        <input type="date" name="start_date" id="start_date" class="form-control" value="{{ $startDate }}">
                    </div>
                    <div class="form-group mr-2">
                        <label for="end_date" class="mr-2"><PERSON><PERSON><PERSON>:</label>
                        <input type="date" name="end_date" id="end_date" class="form-control" value="{{ $endDate }}">
                    </div>
                    <button type="submit" class="btn btn-primary">Terapkan Filter</button>
                </form>
            </div>
        </div>
        
        @forelse($recapData as $userData)
            <div class="card mt-4">
                <div class="card-header bg-primary">
                    <h3 class="card-title">{{ $userData['user']->name }}</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <h4>Total Jam Ekstrakurikuler: {{ $userData['total_eskul_hours'] }} jam</h4>
                            
                            @if(count($userData['eskul_details']) > 0)
                                <div class="mt-4">
                                    <h5>Rincian Jam Ekstrakurikuler:</h5>
                                    <div class="accordion" id="eskulAccordion{{ $userData['user']->id }}">
                                        @foreach($userData['eskul_details'] as $eskulName => $detail)
                                            <div class="card">
                                                <div class="card-header" id="heading{{ $loop->index }}{{ $userData['user']->id }}">
                                                    <h2 class="mb-0">
                                                        <button class="btn btn-link btn-block text-left" type="button" data-toggle="collapse" 
                                                                data-target="#collapse{{ $loop->index }}{{ $userData['user']->id }}" 
                                                                aria-expanded="false" aria-controls="collapse{{ $loop->index }}{{ $userData['user']->id }}">
                                                            {{ $eskulName }} - Total: {{ $detail['total_jam'] }} jam
                                                        </button>
                                                    </h2>
                                                </div>
                                                <div id="collapse{{ $loop->index }}{{ $userData['user']->id }}" class="collapse" 
                                                     aria-labelledby="heading{{ $loop->index }}{{ $userData['user']->id }}" 
                                                     data-parent="#eskulAccordion{{ $userData['user']->id }}">
                                                    <div class="card-body">
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered table-striped">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Tanggal</th>
                                                                        <th>Kelas</th>
                                                                        <th>Jumlah Siswa</th>
                                                                        <th>Kegiatan</th>
                                                                        <th>Jumlah Jam</th>
                                                                        <th>Keterangan</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    @foreach($detail['items'] as $item)
                                                                        <tr>
                                                                            <td>{{ \Carbon\Carbon::parse($item['tanggal'])->format('d/m/Y') }}</td>
                                                                            <td>{{ $item['kelas'] }}</td>
                                                                            <td>{{ $item['jumlah_siswa'] }}</td>
                                                                            <td>{{ $item['kegiatan'] }}</td>
                                                                            <td>{{ $item['jumlah_jam'] }}</td>
                                                                            <td>{{ $item['keterangan'] ?? '-' }}</td>
                                                                        </tr>
                                                                    @endforeach
                                                                </tbody>
                                                                <tfoot>
                                                                    <tr class="bg-light font-weight-bold">
                                                                        <td colspan="4" class="text-right">Total:</td>
                                                                        <td>{{ $detail['total_jam'] }}</td>
                                                                        <td></td>
                                                                    </tr>
                                                                </tfoot>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @else
                                <div class="alert alert-info mt-3">
                                    Tidak ada data ekstrakurikuler untuk periode ini.
                                </div>
                            @endif
                            
                            <!-- Tambahan untuk Jam Pengganti -->
                            <h4 class="mt-4">Total Jam Pengganti: {{ $userData['total_pengganti_hours'] ?? 0 }} jam</h4>
                            
                            @if(isset($userData['pengganti_details']) && count($userData['pengganti_details']) > 0)
                                <div class="mt-3">
                                    <h5>Rincian Jam Pengganti:</h5>
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Tanggal</th>
                                                    <th>Kelas</th>
                                                    <th>Jam Ke</th>
                                                    <th>Guru Diganti</th>
                                                    <th>Jumlah Jam</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($userData['pengganti_details'] as $item)
                                                    <tr>
                                                        <td>{{ \Carbon\Carbon::parse($item['tanggal'])->format('d/m/Y') }}</td>
                                                        <td>{{ $item['kelas'] }}</td>
                                                        <td>{{ $item['jam_ke'] }}</td>
                                                        <td>{{ $item['guru_diganti'] }}</td>
                                                        <td>{{ $item['jumlah_jam'] }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                            <tfoot>
                                                <tr class="bg-light font-weight-bold">
                                                    <td colspan="4" class="text-right">Total:</td>
                                                    <td>{{ $userData['total_pengganti_hours'] ?? 0 }}</td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            @else
                                <div class="alert alert-info mt-3">
                                    Tidak ada data jam pengganti untuk periode ini.
                                </div>
                            @endif
                            
                            <div class="mt-4">
                                <h5>Daftar Jurnal Disetujui:</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>Tanggal</th>
                                                <th>Kegiatan</th>
                                                <th>Keterangan</th>
                                                <th>Disetujui Pada</th>
                                                <th>Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($userData['journals'] as $journal)
                                                <tr>
                                                    <td>{{ \Carbon\Carbon::parse($journal->tanggal)->format('d/m/Y') }}</td>
                                                    <td>{{ $journal->kegiatan }}</td>
                                                    <td>{{ $journal->keterangan ?? '-' }}</td>
                                                    <td>{{ \Carbon\Carbon::parse($journal->approved_at)->format('d/m/Y H:i') }}</td>
                                                    <td>
                                                        <a href="{{ route('jurnal.show', $journal->id) }}" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i> Detail
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="alert alert-info mt-3">
                Tidak ada data jurnal yang disetujui untuk periode yang dipilih.
            </div>
        @endforelse
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="{{ asset('css/admin_custom.css') }}">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            $('.table').DataTable({
                "paging": true,
                "lengthChange": false,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.21/i18n/Indonesian.json"
                }
            });
        });
    </script>
@stop
