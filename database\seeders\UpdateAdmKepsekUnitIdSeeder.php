<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AdmKepsek;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class UpdateAdmKepsekUnitIdSeeder extends Seeder
{
    public function run()
    {
        // Ambil semua record AdmKepsek yang belum memiliki unit_id
        $admKepseks = AdmKepsek::whereNull('unit_id')->get();
        
        foreach ($admKepseks as $adm) {
            // Cari unit_id dari user terkait
            $user = User::find($adm->user_id);
            
            if ($user && $user->unit_id) {
                // Update record AdmKepsek dengan unit_id dari user
                $adm->update(['unit_id' => $user->unit_id]);
            }
        }
        
        $this->command->info('Updated unit_id for ' . count($admKepseks) . ' AdmKepsek records.');
    }
}