<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AdmKtspPermissionSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Daftar permission baru yang akan ditambahkan
        $newPermissions = [
            'view-adm-ktsp',
            'upload-adm-ktsp',
            'manage-adm-ktsp'
        ];

        // Buat permission baru jika belum ada
        foreach ($newPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permission ke role yang sesuai
        $rolePermissions = [
            'Kepala Sekolah' => [
                'view-adm-ktsp',
                'upload-adm-ktsp',
                'manage-adm-ktsp'
            ],
            'Yayasan' => [
                'view-adm-ktsp',
                'upload-adm-ktsp'
            ],
            'Administrator' => [
                'view-adm-ktsp',
                'upload-adm-ktsp',
                'manage-adm-ktsp'
            ],
            'Pengawas' => [
                'view-adm-ktsp',
                'upload-adm-ktsp',
                'manage-adm-ktsp'
            ],
            'Waka Kurikulum' => [
                'view-adm-ktsp',
                'upload-adm-ktsp'
            ],
            'Waka Kesiswaan' => [
                'view-adm-ktsp',
                'upload-adm-ktsp'
            ],
            'Waka Sarpras' => [
                'view-adm-ktsp',
                'upload-adm-ktsp'
            ]
        ];

        // Assign permission ke role tanpa menghapus permission yang sudah ada
        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Role::firstOrCreate(['name' => $roleName]);
            foreach ($permissions as $permission) {
                if (!$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                }
            }
        }
    }
}