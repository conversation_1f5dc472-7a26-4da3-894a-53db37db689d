/* Custom admin styles */
html, body {
    height: 100%;
    margin: 0;
    overflow: hidden;
}

body {
    display: flex;
    flex-direction: column;
}

.wrapper {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

.main-header {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
}

.main-sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    overflow-y: auto;
    height: 100vh;
    z-index: 999;
}

.content-wrapper {
    margin-left: 250px; /* Sesuaikan dengan lebar sidebar */
    margin-top: 57px; /* Sesuaikan dengan tinggi navbar */
    height: calc(100vh - 57px);
    overflow-y: auto;
    z-index: 800;
}

/* Penyesuaian untuk mode collapsed sidebar */
body.sidebar-collapse .content-wrapper {
    margin-left: 4.6rem;
}

/* Penyesuaian untuk mobile */
@media (max-width: 991.98px) {
    .content-wrapper {
        margin-left: 0;
    }
    
    body.sidebar-open .content-wrapper {
        margin-left: 250px;
    }
}

/* Pastikan scrollbar selalu terlihat di Chrome */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
