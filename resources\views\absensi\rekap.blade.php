@extends('adminlte::page')

@section('title', 'Rekap Absensi')

@section('content_header')
    <h1>Rekap Absensi</h1>
@stop

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Filter Rekap Absensi</h3>
    </div>
    <div class="card-body">
        <form id="rekapForm">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="kelas_id">Kelas</label>
                        <select class="form-control" id="kelas_id" name="kelas_id" required>
                            <option value="">-- <PERSON><PERSON><PERSON> --</option>
                            @foreach($kelasOptions as $id => $nama)
                                <option value="{{ $id }}">{{ $nama }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="bulan">Bulan</label>
                        <select class="form-control" id="bulan" name="bulan" required>
                            <option value="1" {{ date('n') == 1 ? 'selected' : '' }}>Januari</option>
                            <option value="2" {{ date('n') == 2 ? 'selected' : '' }}>Februari</option>
                            <option value="3" {{ date('n') == 3 ? 'selected' : '' }}>Maret</option>
                            <option value="4" {{ date('n') == 4 ? 'selected' : '' }}>April</option>
                            <option value="5" {{ date('n') == 5 ? 'selected' : '' }}>Mei</option>
                            <option value="6" {{ date('n') == 6 ? 'selected' : '' }}>Juni</option>
                            <option value="7" {{ date('n') == 7 ? 'selected' : '' }}>Juli</option>
                            <option value="8" {{ date('n') == 8 ? 'selected' : '' }}>Agustus</option>
                            <option value="9" {{ date('n') == 9 ? 'selected' : '' }}>September</option>
                            <option value="10" {{ date('n') == 10 ? 'selected' : '' }}>Oktober</option>
                            <option value="11" {{ date('n') == 11 ? 'selected' : '' }}>November</option>
                            <option value="12" {{ date('n') == 12 ? 'selected' : '' }}>Desember</option>
                        </select>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="tahun">Tahun</label>
                        <select class="form-control" id="tahun" name="tahun" required>
                            @for($i = date('Y'); $i >= date('Y') - 5; $i--)
                                <option value="{{ $i }}" {{ date('Y') == $i ? 'selected' : '' }}>{{ $i }}</option>
                            @endfor
                        </select>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="button" id="btnTampilkanRekap" class="btn btn-primary btn-block">
                            <i class="fas fa-search"></i> Tampilkan Rekap
                        </button>
                    </div>
                </div>
            </div>
        </form>
        
        <div id="rekapContainer" class="mt-4" style="display: none;">
            <div class="text-right mb-3">
                <button type="button" id="btnExportExcel" class="btn btn-success">
                    <i class="fas fa-file-excel"></i> Export Excel
                </button>
                <button type="button" id="btnPrint" class="btn btn-info">
                    <i class="fas fa-print"></i> Cetak
                </button>
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="rekapTable">
                    <thead>
                        <tr>
                            <th rowspan="2" class="text-center align-middle">No</th>
                            <th rowspan="2" class="text-center align-middle">NISN</th>
                            <th rowspan="2" class="text-center align-middle">Nama Siswa</th>
                            <th colspan="{{ $totalDays ?? 31 }}" class="text-center" id="bulanTahunHeader">
                                {{ isset($namaBulan[$bulan]) ? $namaBulan[$bulan] . ' ' . $tahun : 'Tanggal' }}
                            </th>
                            <th colspan="4" class="text-center">Jumlah</th>
                        </tr>
                        <tr id="tanggalHeader">
                            @if(isset($totalDays))
                                @for($i = 1; $i <= $totalDays; $i++)
                                    <th class="text-center">{{ $i }}</th>
                                @endfor
                            @else
                                <!-- Tanggal akan diisi oleh JavaScript jika tidak ada filter -->
                            @endif
                            <th class="text-center">H</th>
                            <th class="text-center">S</th>
                            <th class="text-center">I</th>
                            <th class="text-center">A</th>
                        </tr>
                    </thead>
                    <tbody id="rekapTableBody">
                        @if(count($rekapData) > 0)
                            @foreach($rekapData as $index => $siswa)
                                <tr>
                                    <td class="text-center">{{ $index + 1 }}</td>
                                    <td>{{ $siswa->nisn }}</td>
                                    <td>{{ $siswa->nama }}</td>
                                    @if(isset($totalDays))
                                        @for($i = 1; $i <= $totalDays; $i++)
                                            <td class="text-center">
                                                @if(isset($siswa->absensi[$i]))
                                                    @if($siswa->absensi[$i] == 'hadir')
                                                        <span class="badge bg-success">H</span>
                                                    @elseif($siswa->absensi[$i] == 'sakit')
                                                        <span class="badge bg-warning">S</span>
                                                    @elseif($siswa->absensi[$i] == 'izin')
                                                        <span class="badge bg-info">I</span>
                                                    @elseif($siswa->absensi[$i] == 'alpa')
                                                        <span class="badge bg-danger">A</span>
                                                    @endif
                                                @else
                                                    -
                                                @endif
                                            </td>
                                        @endfor
                                    @endif
                                    <td class="text-center">{{ $siswa->hadir ?? 0 }}</td>
                                    <td class="text-center">{{ $siswa->sakit ?? 0 }}</td>
                                    <td class="text-center">{{ $siswa->izin ?? 0 }}</td>
                                    <td class="text-center">{{ $siswa->alpa ?? 0 }}</td>
                                </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="{{ ($totalDays ?? 31) + 7 }}" class="text-center">
                                    @if($kelasId)
                                        Tidak ada data absensi untuk kelas dan periode yang dipilih
                                    @else
                                        Silakan pilih kelas, bulan, dan tahun untuk melihat rekap absensi
                                    @endif
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@stop

@section('js')
<script>
$(function() {
    // Tampilkan rekap absensi
    $('#btnTampilkanRekap').click(function() {
        const kelasId = $('#kelas_id').val();
        const bulan = $('#bulan').val();
        const tahun = $('#tahun').val();
        
        if (!kelasId) {
            alert('Silakan pilih kelas terlebih dahulu');
            return;
        }
        
        // Set header bulan dan tahun
        const namaBulan = $('#bulan option:selected').text();
        $('#bulanTahunHeader').text(`${namaBulan} ${tahun}`);
        
        // Buat header tanggal
        const jumlahHari = new Date(tahun, bulan, 0).getDate();
        let headerTanggal = '';
        
        for (let i = 1; i <= jumlahHari; i++) {
            headerTanggal += `<th class="text-center">${i}</th>`;
        }
        
        // Tambahkan kolom jumlah
        headerTanggal += `
            <th class="text-center">H</th>
            <th class="text-center">S</th>
            <th class="text-center">I</th>
            <th class="text-center">A</th>
        `;
        
        // Update colspan pada header bulan dan tahun
        $('#bulanTahunHeader').attr('colspan', jumlahHari);
        
        // Update header tanggal
        $('#tanggalHeader').html(headerTanggal);
        
        // Load data rekap
        $.ajax({
            url: "{{ route('absensi.get-rekap') }}",
            type: "GET",
            data: {
                kelas_id: kelasId,
                bulan: bulan,
                tahun: tahun
            },
            dataType: "json",
            beforeSend: function() {
                $('#rekapTableBody').html('<tr><td colspan="' + (jumlahHari + 7) + '" class="text-center">Loading...</td></tr>');
                $('#rekapContainer').show();
            },
            success: function(response) {
                let html = '';
                
                if (response.length === 0) {
                    html = '<tr><td colspan="' + (jumlahHari + 7) + '" class="text-center">Tidak ada data absensi</td></tr>';
                } else {
                    $.each(response, function(index, siswa) {
                        html += `<tr>
                            <td class="text-center">${index + 1}</td>
                            <td>${siswa.nisn}</td>
                            <td>${siswa.nama}</td>`;
                        
                        // Tampilkan status untuk setiap tanggal
                        for (let i = 1; i <= jumlahHari; i++) {
                            const status = siswa.absensi[i] || '';
                            let statusText = '';
                            let statusClass = '';
                            
                            if (status === 'hadir') {
                                statusText = 'H';
                                statusClass = 'bg-success text-white';
                            } else if (status === 'sakit') {
                                statusText = 'S';
                                statusClass = 'bg-warning';
                            } else if (status === 'izin') {
                                statusText = 'I';
                                statusClass = 'bg-info text-white';
                            } else if (status === 'alpa') {
                                statusText = 'A';
                                statusClass = 'bg-danger text-white';
                            }
                            
                            html += `<td class="text-center ${statusClass}">${statusText}</td>`;
                        }
                        
                        // Tampilkan jumlah
                        html += `
                            <td class="text-center">${siswa.hadir || 0}</td>
                            <td class="text-center">${siswa.sakit || 0}</td>
                            <td class="text-center">${siswa.izin || 0}</td>
                            <td class="text-center">${siswa.alpa || 0}</td>
                        </tr>`;
                    });
                }
                
                $('#rekapTableBody').html(html);
            },
            error: function(xhr) {
                alert('Terjadi kesalahan saat memuat data rekap absensi');
                console.error(xhr.responseText);
                $('#rekapTableBody').html('<tr><td colspan="' + (jumlahHari + 7) + '" class="text-center text-danger">Error: Gagal memuat data</td></tr>');
            }
        });
    });
    
    // Export Excel
    $('#btnExportExcel').click(function() {
        const kelasId = $('#kelas_id').val();
        const bulan = $('#bulan').val();
        const tahun = $('#tahun').val();
        
        if (!kelasId) {
            alert('Silakan pilih kelas terlebih dahulu');
            return;
        }
        
        window.location.href = `{{ url('absensi/export-excel') }}?kelas_id=${kelasId}&bulan=${bulan}&tahun=${tahun}`;
    });
    
    // Print
    $('#btnPrint').click(function() {
        window.print();
    });
});
</script>
@stop

@section('css')
<style>
    .table td, .table th {
        vertical-align: middle;
        padding: 0.5rem;
        text-align: center;
    }
    
    @media print {
        .no-print {
            display: none !important;
        }
        
        .card-header, .btn, form {
            display: none !important;
        }
        
        .card {
            border: none !important;
        }
        
        .card-body {
            padding: 0 !important;
        }
    }
</style>
@stop





