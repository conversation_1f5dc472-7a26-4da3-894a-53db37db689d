@extends('layouts.admin')

@section('title', 'Buat Jurnal Kegiatan')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Tambah Jurnal Kegiatan</h3>
    </div>
    <div class="card-body">
        <form action="{{ route('jurnal.store') }}" method="POST">
            @csrf
            
            <!-- Tambahkan informasi unit -->
            <div class="form-group">
                <label>Unit</label>
                <input type="text" class="form-control" value="{{ auth()->user()->unit->nama_unit ?? '-' }}" disabled>
                <small class="form-text text-muted">Unit akan otomatis terisi berdasarkan unit Anda</small>
            </div>
            
            <div class="form-group">
                <label for="tanggal">Tanggal</label>
                <input type="date" class="form-control @error('tanggal') is-invalid @enderror" id="tanggal" name="tanggal" value="{{ old('tanggal', date('Y-m-d')) }}" required>
                @error('tanggal')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label>Kegiatan</label>
                <textarea name="kegiatan" class="form-control @error('kegiatan') is-invalid @enderror" rows="5" required>{{ old('kegiatan') }}</textarea>
                @error('kegiatan')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label>Status Kehadiran</label>
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="ada" name="ada">
                    <label class="custom-control-label" for="ada">Ada</label>
                </div>
                <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" id="tidak" name="tidak">
                    <label class="custom-control-label" for="tidak">Tidak</label>
                </div>
            </div>
            
            <div class="form-group">
                <label>Keterangan</label>
                <textarea name="keterangan" class="form-control" rows="3"></textarea>
            </div>
            
            <button type="submit" class="btn btn-primary">Simpan</button>
        </form>
    </div>
</div>
@endsection

@section('js')
<script>
$(document).ready(function() {
    // Pastikan hanya satu checkbox yang bisa dipilih
    $('#ada, #tidak').change(function() {
        if (this.checked) {
            const otherId = this.id === 'ada' ? '#tidak' : '#ada';
            $(otherId).prop('checked', false);
        }
    });
});
</script>
@endsection

