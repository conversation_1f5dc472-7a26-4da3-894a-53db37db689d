@extends('adminlte::page')

@section('title', 'Approval Jurnal')

@section('content_header')
    <h1>Approval Jurnal Kegiatan</h1>
@stop

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Daftar Jurnal Menunggu Persetujuan</h3>
            </div>
            <div class="card-body">
                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Nama</th>
                            <th>Tanggal</th>
                            <th>Kegiatan</th>
                            <th>Keterangan</th>
                            <th>Diajukan Pada</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($pendingJurnals as $jurnal)
                        <tr>
                            <td>{{ $jurnal->user->name }}</td>
                            <td>{{ \Carbon\Carbon::parse($jurnal->tanggal)->format('d/m/Y') }}</td>
                            <td>{{ $jurnal->kegiatan }}</td>
                            <td>{{ $jurnal->keterangan ?? '-' }}</td>
                            <td>{{ $jurnal->submitted_at ? \Carbon\Carbon::parse($jurnal->submitted_at)->format('d/m/Y H:i') : '-' }}</td>
                            <td>
                                <a href="{{ route('jurnal.show', $jurnal->id) }}" class="btn btn-sm btn-info mb-1" data-toggle="tooltip" title="Lihat Detail">
                                    <i class="fas fa-eye"></i> Detail
                                </a>
                                
                                <form action="{{ route('jurnal.approve.individual', $jurnal->id) }}" method="POST" class="d-inline">
                                    @csrf
                                    <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Yakin ingin menyetujui jurnal ini?')">
                                        <i class="fas fa-check"></i> Setujui
                                    </button>
                                </form>
                                
                                <button type="button" class="btn btn-sm btn-danger" onclick="showRejectModal('{{ route('jurnal.reject.individual', $jurnal->id) }}')">
                                    <i class="fas fa-times"></i> Tolak
                                </button>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center">Tidak ada jurnal yang menunggu persetujuan</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
                
                {{ $pendingJurnals->links() }}
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="{{ asset('css/admin_custom.css') }}">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            $('[data-toggle="tooltip"]').tooltip();
            
            $('table').DataTable({
                "paging": false,
                "lengthChange": false,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
            });
        });
        
        function showRejectModal(url) {
            $('#rejectForm').attr('action', url);
            $('#rejectModal').modal('show');
        }
    </script>
@stop

@include('jurnal.modals.reject')


