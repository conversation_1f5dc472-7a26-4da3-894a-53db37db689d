<?php

/**
 * Mengekstrak ID video dari URL YouTube
 * 
 * @param string $url URL YouTube
 * @return string|null ID video YouTube atau null jika tidak valid
 */
function getYoutubeVideoId($url) {
    if (empty($url)) return null;
    
    $pattern = 
        '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i';
    
    if (preg_match($pattern, $url, $match)) {
        return $match[1];
    }
    
    return null;
}

