<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckRole
{
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        // Nonaktifkan sementara pengecekan role
        return $next($request);
        
        /* <PERSON>de asli
        if (!in_array($request->user()->role, $roles)) {
            abort(403, 'Unauthorized action.');
        }
        */
    }
}
