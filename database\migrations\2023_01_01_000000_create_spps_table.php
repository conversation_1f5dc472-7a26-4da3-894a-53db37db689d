<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('spps', function (Blueprint $table) {
            $table->id();
            $table->string('va_number');
            $table->string('nama');
            $table->date('tanggal_lahir');
            $table->decimal('spp_bulan_ini', 10, 2);
            $table->decimal('tunggakan', 10, 2)->default(0);
            $table->decimal('buku', 10, 2)->default(0);
            $table->decimal('uang_program', 10, 2)->default(0);
            $table->decimal('les', 10, 2)->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('spps');
    }
};