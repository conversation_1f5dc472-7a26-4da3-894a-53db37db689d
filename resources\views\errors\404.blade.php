<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Halaman Tidak <PERSON>n</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .error-container {
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-content {
            text-align: center;
            padding: 2rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            max-width: 500px;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-content">
            <i class="fas fa-exclamation-triangle fa-4x text-warning mb-4"></i>
            <h2 class="mb-4">404 - Halaman Tidak Ditemukan</h2>
            <p class="lead text-muted">{{ $message ?? 'Halaman yang Anda cari tidak ditemukan.' }}</p>
            <div class="mt-4">
                <a href="{{ $back_url ?? url('/') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-left mr-2"></i> Kembali
                </a>
            </div>
            @if(isset($debug_info) && !empty($debug_info))
                <div class="mt-4 text-left">
                    <small class="text-muted">
                        <strong>Debug Info:</strong><br>
                        File: {{ $debug_info['file'] ?? 'Unknown' }}<br>
                        Line: {{ $debug_info['line'] ?? 'Unknown' }}
                    </small>
                </div>
            @endif
        </div>
    </div>
</body>
</html>

