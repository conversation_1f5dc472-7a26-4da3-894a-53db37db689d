<?php

namespace App\Http\Controllers;

use App\Models\Jenjang;
use Illuminate\Http\Request;

class JenjangController extends Controller
{
    public function index()
    {
        $jenjang = Jenjang::all();
        return view('pengaturan.jenjang', compact('jenjang'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'jenjang' => 'required',
            'tingkat' => 'required'
        ]);

        // nama_jenjang akan otomatis terisi melalui model boot method
        Jenjang::create($validated);
        return redirect()->route('jenjang.index')->with('success', 'Data berhasil ditambahkan');
    }

    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'jenjang' => 'required',
            'tingkat' => 'required'
        ]);

        $jenjang = Jenjang::findOrFail($id);
        $jenjang->update($validated);
        return redirect()->route('jenjang.index')->with('success', 'Data berhasil diperbarui');
    }

    public function destroy($id)
    {
        $jenjang = Jenjang::findOrFail($id);
        $jenjang->delete();
        return redirect()->route('jenjang.index')->with('success', 'Data berhasil dihapus');
    }
}
