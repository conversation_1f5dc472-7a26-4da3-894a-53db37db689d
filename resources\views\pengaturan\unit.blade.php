@extends('layouts.admin')

@section('title', 'Data Unit')

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar Unit</h3>
        <div class="card-tools">
            <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#tambahUnit">
                <i class="fas fa-plus"></i> Tambah Unit
            </button>
        </div>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        {{-- Tabel untuk menampilkan data unit --}}
        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>No</th>
                    <th>Jenjang</th>
                    <th>Nama Unit</th>
                    <th>NSS</th>
                    <th>NPSN</th>
                    <th>No Telepon</th>
                    <th>Alamat</th>
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
                @foreach($units as $unit)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $unit->jenjang_id }}</td>
                    <td>{{ $unit->nama_unit }}</td>
                    <td>{{ $unit->nss }}</td>
                    <td>{{ $unit->npsn }}</td>
                    <td>{{ $unit->no_telepon }}</td>
                    <td>{{ $unit->alamat }}</td>
                    <td>
                        <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#editUnit{{ $unit->id }}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <form action="{{ route('unit.destroy', $unit->id) }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Yakin hapus data?')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<!-- Modal Tambah Unit -->
<div class="modal fade" id="tambahUnit" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Unit</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('unit.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    {{-- Form Group Jenjang --}}
                    <div class="form-group">
                        <label>Jenjang</label>
                        <select name="jenjang_id" class="form-control @error('jenjang_id') is-invalid @enderror" required>
                            <option value="">Pilih Jenjang</option>
                            <option value="PG">PG</option>
                            <option value="SD">SD</option>
                            <option value="SMP">SMP</option>
                            <option value="SMA">SMA</option>
                        </select>
                        @error('jenjang_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    {{-- Form Group Nama Unit --}}
                    <div class="form-group">
                        <label>Nama Unit</label>
                        <input type="text" name="nama_unit" class="form-control @error('nama_unit') is-invalid @enderror" required>
                        @error('nama_unit')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    {{-- Form Group NSS --}}
                    <div class="form-group">
                        <label>NSS</label>
                        <input type="text" name="nss" class="form-control @error('nss') is-invalid @enderror" required>
                        @error('nss')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    {{-- Form Group NPSN --}}
                    <div class="form-group">
                        <label>NPSN</label>
                        <input type="text" name="npsn" class="form-control @error('npsn') is-invalid @enderror" required>
                        @error('npsn')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    {{-- Form Group No Telepon --}}
                    <div class="form-group">
                        <label>No Telepon</label>
                        <input type="text" name="no_telepon" class="form-control @error('no_telepon') is-invalid @enderror" required>
                        @error('no_telepon')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    {{-- Form Group Alamat --}}
                    <div class="form-group">
                        <label>Alamat</label>
                        <textarea name="alamat" class="form-control @error('alamat') is-invalid @enderror" rows="3" required></textarea>
                        @error('alamat')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit Unit -->
@foreach($units as $unit)
<div class="modal fade" id="editUnit{{ $unit->id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Unit</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('unit.update', $unit->id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    {{-- Form Group Jenjang --}}
                    <div class="form-group">
                        <label>Jenjang</label>
                        <select name="jenjang_id" class="form-control @error('jenjang_id') is-invalid @enderror" required>
                            <option value="">Pilih Jenjang</option>
                            <option value="PG" {{ $unit->jenjang_id == 'PG' ? 'selected' : '' }}>PG</option>
                            <option value="SD" {{ $unit->jenjang_id == 'SD' ? 'selected' : '' }}>SD</option>
                            <option value="SMP" {{ $unit->jenjang_id == 'SMP' ? 'selected' : '' }}>SMP</option>
                            <option value="SMA" {{ $unit->jenjang_id == 'SMA' ? 'selected' : '' }}>SMA</option>
                        </select>
                        @error('jenjang_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    {{-- Form Group Nama Unit --}}
                    <div class="form-group">
                        <label>Nama Unit</label>
                        <input type="text" name="nama_unit" class="form-control @error('nama_unit') is-invalid @enderror" 
                               value="{{ $unit->nama_unit }}" required>
                        @error('nama_unit')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    {{-- Form Group NSS --}}
                    <div class="form-group">
                        <label>NSS</label>
                        <input type="text" name="nss" class="form-control @error('nss') is-invalid @enderror" 
                               value="{{ $unit->nss }}" required>
                        @error('nss')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    {{-- Form Group NPSN --}}
                    <div class="form-group">
                        <label>NPSN</label>
                        <input type="text" name="npsn" class="form-control @error('npsn') is-invalid @enderror" 
                               value="{{ $unit->npsn }}" required>
                        @error('npsn')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    {{-- Form Group No Telepon --}}
                    <div class="form-group">
                        <label>No Telepon</label>
                        <input type="text" name="no_telepon" class="form-control @error('no_telepon') is-invalid @enderror" 
                               value="{{ $unit->no_telepon }}" required>
                        @error('no_telepon')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    {{-- Form Group Alamat --}}
                    <div class="form-group">
                        <label>Alamat</label>
                        <textarea name="alamat" class="form-control @error('alamat') is-invalid @enderror" 
                                  rows="3" required>{{ $unit->alamat }}</textarea>
                        @error('alamat')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endforeach

@endsection
