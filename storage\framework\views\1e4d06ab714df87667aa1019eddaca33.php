<?php $__env->startSection('title', 'Daftar Fasilitas'); ?>

<?php $__env->startSection('content_header'); ?>
    <h1>Daftar Fasilitas</h1>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <a href="<?php echo e(route('admin.website.facility.create')); ?>" class="btn btn-primary">
            Tambah Fasilitas
        </a>
    </div>
    <div class="card-body">
        <?php if(session('success')): ?>
            <div class="alert alert-success"><?php echo e(session('success')); ?></div>
        <?php endif; ?>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Gambar</th>
                        <th>Judul</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $facilities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $facility): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td><?php echo e($loop->iteration); ?></td>
                        <td>
                            <?php if($facility->image): ?>
                                <img src="<?php echo e(asset('storage/facilities/'.$facility->image)); ?>" 
                                     alt="<?php echo e($facility->title); ?>" 
                                     width="100">
                            <?php endif; ?>
                        </td>
                        <td><?php echo e($facility->title); ?></td>
                        <td>
                            <a href="<?php echo e(route('admin.website.facility.edit', $facility)); ?>" 
                               class="btn btn-sm btn-warning">Edit</a>
                            <form action="<?php echo e(route('admin.website.facility.destroy', $facility)); ?>" 
                                  method="POST" 
                                  class="d-inline">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" 
                                        class="btn btn-sm btn-danger" 
                                        onclick="return confirm('Yakin ingin menghapus?')">Hapus</button>
                            </form>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('adminlte::page', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/admin/website/facility/index.blade.php ENDPATH**/ ?>