<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AnggotaEkstrakurikuler extends Model
{
    use HasFactory;

    // Tentukan nama tabel yang benar
    protected $table = 'anggota_ekstrakurikulers';

    protected $fillable = [
        'rombel_ekstrakurikuler_id',
        'siswa_id',
        'kelas_id',
    ];

    // Relasi ke rombel ekstrakurikuler
    public function rombelEkstrakurikuler()
    {
        return $this->belongsTo(RombelEkstrakurikuler::class, 'rombel_ekstrakurikuler_id');
    }

    // Relasi ke siswa
    public function siswa()
    {
        return $this->belongsTo(PesertaDidik::class, 'siswa_id');
    }

    // Relasi ke kelas
    public function kelas()
    {
        return $this->belongsTo(Kelas::class, 'kelas_id');
    }
}

