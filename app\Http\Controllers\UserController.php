<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Unit; // Tambahkan import model Unit
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules;

class UserController extends Controller
{
    public function index()
    {
        $users = User::with('unit')->get();
        return view('admin.users.index', compact('users'));
    }

    public function create()
    {
        // Mengambil daftar unit dari database
        $units = Unit::select('id', 'nama_unit')->get();
        
        $roles = [
            'Administrator',
            'Yayasan',
            'Pengawas',
            'Kepal<PERSON> Se<PERSON>lah',
            '<PERSON>aka Kurikulum',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            'Guru',
            'Bimbingan Konseling',
            'Tata Usaha',
            'Pustakawan'
        ];
        return view('admin.users.create', compact('units', 'roles'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'phone' => ['required', 'string', 'max:15'],
            'unit_id' => ['required', 'exists:units,id'], // ubah validasi
            'role' => ['required', 'string'],
            'photo' => ['nullable', 'image', 'max:1024'],
        ]);

        $data = [
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'unit_id' => $request->unit_id, // ubah dari unit menjadi unit_id
            'role' => $request->role,
        ];

        if ($request->hasFile('photo')) {
            $photo = $request->file('photo');
            $filename = time() . '.' . $photo->getClientOriginalExtension();
            
            // Simpan gambar menggunakan Storage facade
            $path = $photo->storeAs('photos', $filename, 'public');
            $data['photo'] = $filename;
        }

        $user = User::create($data);
        
        // Assign role ke user
        $user->assignRole($request->role);

        return redirect()->route('users.index')
            ->with('success', 'User berhasil ditambahkan');
    }

    public function edit(User $user)
    {
        // Pastikan variabel $units diambil dari database
        $units = Unit::select('id', 'nama_unit')->get();
        
        $roles = [
            'Administrator',
            'Yayasan',
            'Pengawas',
            'Kepala Sekolah',
            'Waka Kurikulum',
            'Waka Kesiswaan',
            'Waka Sarpras',
            'Guru',
            'Bimbingan Konseling',
            'Tata Usaha',
            'Pustakawan'
        ];
        return view('admin.users.edit', compact('user', 'units', 'roles'));
    }

    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'password' => ['nullable', 'confirmed', Rules\Password::defaults()],
            'phone' => ['required', 'string', 'max:15'],
            'unit_id' => ['required', 'exists:units,id'],
            'role' => ['required', 'string'],
            'photo' => ['nullable', 'image', 'max:1024'],
        ]);

        $data = [
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'unit_id' => $request->unit_id,
            'role' => $request->role,
        ];

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        if ($request->hasFile('photo')) {
            // Hapus foto lama jika ada
            if ($user->photo) {
                Storage::disk('public')->delete('photos/' . $user->photo);
            }
            
            $photo = $request->file('photo');
            $filename = time() . '.' . $photo->getClientOriginalExtension();
            
            // Simpan gambar menggunakan Storage facade
            $path = $photo->storeAs('photos', $filename, 'public');
            $data['photo'] = $filename;
        }

        $user->update($data);
        
        // Sync role user
        $user->syncRoles([$request->role]);

        return redirect()->route('users.index')
            ->with('success', 'User berhasil diperbarui');
    }

    public function destroy(User $user)
    {
        $user->delete();
        return redirect()->route('users.index')
            ->with('success', 'User berhasil dihapus');
    }
}












