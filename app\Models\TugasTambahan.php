<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TugasTambahan extends Model
{
    use HasFactory;
    
    // Tentukan nama tabel
    protected $table = 'guru_tugastambahan';
    
    // Tentukan kolom yang dapat diisi
    protected $fillable = [
        'guru_id',
        'nama_guru',
        'tugas_tambahan',
        'kelas',
        'tahun_ajaran_id'
    ];
    
    // Relasi ke guru
    public function guru()
    {
        return $this->belongsTo(Guru::class);
    }
    
    // <PERSON>lasi ke tahun ajaran
    public function tahunAjaran()
    {
        return $this->belongsTo(TahunAjaran::class);
    }
}

