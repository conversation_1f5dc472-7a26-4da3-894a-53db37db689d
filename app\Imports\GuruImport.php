<?php

namespace App\Imports;

use App\Models\Guru;
use App\Models\Unit;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class GuruImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError
{
    use SkipsErrors;
    
    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        try {
            // Log data untuk debugging
            Log::info('Processing row: ', $row);
            
            // Cari unit berdasarkan nama
            $unit = null;
            if (isset($row['unit']) && !empty($row['unit'])) {
                $unit = Unit::where('nama_unit', $row['unit'])->first();
                if (!$unit) {
                    Log::warning('Unit not found: ' . $row['unit']);
                } else {
                    Log::info('Unit found: ' . $unit->nama_unit . ' (ID: ' . $unit->id . ')');
                }
            } else {
                Log::warning('Unit field is empty or not set');
            }
            
            // Proses tanggal lahir
            $tanggalLahir = $this->transformDate($row['tanggal_lahir'] ?? null);
            Log::info('Transformed tanggal_lahir: ' . ($tanggalLahir ?? 'null') . ' from: ' . ($row['tanggal_lahir'] ?? 'null'));
            
            // Proses tanggal pengangkatan
            $tmtPengangkatan = $this->transformDate($row['tmt_pengangkatan'] ?? null);
            Log::info('Transformed tmt_pengangkatan: ' . ($tmtPengangkatan ?? 'null') . ' from: ' . ($row['tmt_pengangkatan'] ?? 'null'));
            
            // Proses tanggal penugasan
            $tmtPenugasan = $this->transformDate($row['tmt_penugasan'] ?? null);
            Log::info('Transformed tmt_penugasan: ' . ($tmtPenugasan ?? 'null') . ' from: ' . ($row['tmt_penugasan'] ?? 'null'));
            
            // Buat data guru sesuai dengan form create
            $guru = new Guru([
                // IDENTITAS GURU
                'nama' => $row['nama'] ?? null,
                'nik' => $row['nik'] ?? null,
                'jenis_kelamin' => $row['jenis_kelamin'] ?? null,
                'tempat_lahir' => $row['tempat_lahir'] ?? null,
                'tanggal_lahir' => $tanggalLahir,
                'nama_ibu_kandung' => $row['nama_ibu_kandung'] ?? null,
                
                // ALAMAT
                'alamat' => $row['alamat'] ?? null,
                'kelurahan' => $row['kelurahan'] ?? null,
                'kecamatan' => $row['kecamatan'] ?? null,
                'kabupaten' => $row['kabupaten'] ?? null,
                'provinsi' => $row['provinsi'] ?? null,
                
                // DATA PRIBADI
                'agama' => $row['agama'] ?? null,
                'npwp' => $row['npwp'] ?? null,
                'nama_wajib_pajak' => $row['nama_wajib_pajak'] ?? null,
                'kewarganegaraan' => $row['kewarganegaraan'] ?? 'Indonesia',
                'status_kawin' => $row['status_kawin'] ?? null,
                'nama_pasangan' => $row['nama_pasangan'] ?? null,
                'pekerjaan_pasangan' => $row['pekerjaan_pasangan'] ?? null,
                
                // KEPEGAWAIAN
                'status_pegawai' => $row['status_pegawai'] ?? null,
                'niy' => $row['niy'] ?? null,
                'nuptk' => $row['nuptk'] ?? null,
                'jenis_ptk' => $row['jenis_ptk'] ?? null,
                'sk_pengangkatan' => $row['sk_pengangkatan'] ?? null,
                'tmt_pengangkatan' => $tmtPengangkatan,
                'lembaga_pengangkat' => $row['lembaga_pengangkat'] ?? null,
                'pangkat_golongan' => $row['pangkat_golongan'] ?? null,
                
                // PENUGASAN
                'sk_penugasan' => $row['sk_penugasan'] ?? null,
                'tmt_penugasan' => $tmtPenugasan,
                'lembaga_penugasan' => $row['lembaga_penugasan'] ?? null,
                'status' => $row['status'] ?? 'Aktif',
                
                // KONTAK
                'no_telp' => $row['no_telp'] ?? null,
                'email' => $row['email'] ?? null,
                
                // MATA PELAJARAN
                'mata_pelajaran' => $row['mata_pelajaran'] ?? null,
                
                // UNIT
                'unit_id' => $unit ? $unit->id : null,
            ]);
            
            Log::info('Guru model created successfully: ' . $guru->nama);
            return $guru;
        } catch (\Exception $e) {
            Log::error('Error importing guru: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'row' => $row
            ]);
            throw $e; // Re-throw to let SkipsOnError handle it
        }
    }

    /**
     * Transform date value from excel
     *
     * @param mixed $value
     * @return string|null
     */
    private function transformDate($value)
    {
        if (empty($value)) {
            return null;
        }
        
        try {
            // Jika nilai sudah dalam format string YYYY-MM-DD, gunakan langsung
            if (is_string($value) && preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
                return $value;
            }
            
            // Jika nilai adalah angka Excel date, konversi ke PHP date
            if (is_numeric($value)) {
                return Date::excelToDateTimeObject($value)->format('Y-m-d');
            }
            
            // Jika format lain, coba parse dengan strtotime
            $timestamp = strtotime($value);
            if ($timestamp !== false) {
                return date('Y-m-d', $timestamp);
            }
            
            // Jika semua gagal, kembalikan null
            return null;
        } catch (\Exception $e) {
            Log::error('Error transforming date: ' . $e->getMessage(), [
                'value' => $value,
                'type' => gettype($value)
            ]);
            return null;
        }
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'nama' => 'required',
            'jenis_kelamin' => 'nullable|in:L,P',
            'unit' => 'required',
        ];
    }
    
    /**
     * @param \Throwable $e
     */
    public function onError(\Throwable $e)
    {
        Log::error('Error in GuruImport: ' . $e->getMessage(), [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
    }
}



