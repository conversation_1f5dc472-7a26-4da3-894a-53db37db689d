<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class PengaturanPermissionSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles dan permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Daftar permission baru untuk pengaturan
        $newPermissions = [
            'view-pengaturan',       // Untuk melihat menu pengaturan
            'manage-pengaturan',     // Untuk mengelola pengaturan umum
            'view-unit',             // Untuk melihat data unit
            'manage-unit',           // Untuk mengelola unit
            'view-jenjang',          // Untuk melihat data jenjang
            'manage-jenjang',        // Untuk mengelola jenjang
            'view-gedung',           // Untuk melihat data gedung
            'manage-gedung',         // Untuk mengelola gedung
            'view-kelas',            // Untuk melihat data kelas
            'manage-kelas',          // Untuk mengelola kelas
            'view-mapel',            // Untuk melihat data mata pelajaran
            'manage-mapel',          // Untuk mengelola mata pelajaran
        ];

        // Buat permission baru jika belum ada
        foreach ($newPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permission ke role yang sesuai
        $rolePermissions = [
            'Administrator' => [
                'view-pengaturan',
                'manage-pengaturan',
                'view-unit',
                'manage-unit',
                'view-jenjang',
                'manage-jenjang',
                'view-gedung',
                'manage-gedung',
                'view-kelas',
                'manage-kelas',
                'view-mapel',
                'manage-mapel',
            ],
            'Kepala Sekolah' => [
                'view-pengaturan',
                'view-unit',
                'view-jenjang',
                'view-gedung',
                'view-kelas',
                'view-mapel',
            ],
            'Waka Kurikulum' => [
                'view-pengaturan',
                'view-unit',
                'view-jenjang',
                'view-gedung',
                'view-kelas',
                'manage-kelas',
                'view-mapel',
                'manage-mapel',
            ],
            'Tata Usaha' => [
                'view-pengaturan',
                'view-unit',
                'view-jenjang',
                'view-gedung',
                'manage-gedung',
                'view-kelas',
                'manage-kelas',
                'view-mapel',
            ],
        ];

        // Assign permission ke role tanpa menghapus permission yang sudah ada
        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Role::firstOrCreate(['name' => $roleName]);
            foreach ($permissions as $permission) {
                if (!$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                }
            }
        }
    }
}