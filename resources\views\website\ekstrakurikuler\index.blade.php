@extends('layouts.website')

@section('content')
<style>
.title-container {
    position: relative;
    padding: 20px 0;
    margin-bottom: 50px;
}

.facility-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
    padding-bottom: 15px;
}

.facility-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, #3498db, #2ecc71);
    border-radius: 2px;
}

.title-decoration {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 2px;
    background-color: #ecf0f1;
}

.title-container::before,
.title-container::after {
    content: '★';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24px;
    color: #3498db;
}

.title-container::before {
    left: 25%;
}

.title-container::after {
    right: 25%;
}

@media (max-width: 768px) {
    .facility-title {
        font-size: 2rem;
    }
    
    .title-container::before {
        left: 10%;
    }
    
    .title-container::after {
        right: 10%;
    }
}

/* Animasi hover untuk cards */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.card-img-top {
    height: 250px;
    object-fit: cover;
}
</style>
<div class="container py-5">
    <div class="title-container text-center">
        <h1 class="facility-title">Ekstrakurikuler</h1>
        <div class="title-decoration"></div>
    </div>

    <!-- Filter Jenjang -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="list-group list-group-horizontal-md justify-content-center">
                <a href="{{ route('website.ekstrakurikuler') }}" 
                   class="list-group-item list-group-item-action {{ request()->routeIs('website.ekstrakurikuler') ? 'active' : '' }}">
                    Semua
                </a>
                <a href="{{ route('website.ekstrakurikuler.jenjang', 'paud') }}" 
                   class="list-group-item list-group-item-action {{ request()->segment(2) == 'paud' ? 'active' : '' }}">
                    PAUD
                </a>
                <a href="{{ route('website.ekstrakurikuler.jenjang', 'sd') }}" 
                   class="list-group-item list-group-item-action {{ request()->segment(2) == 'sd' ? 'active' : '' }}">
                    SD
                </a>
                <a href="{{ route('website.ekstrakurikuler.jenjang', 'smp') }}" 
                   class="list-group-item list-group-item-action {{ request()->segment(2) == 'smp' ? 'active' : '' }}">
                    SMP
                </a>
                <a href="{{ route('website.ekstrakurikuler.jenjang', 'sma') }}" 
                   class="list-group-item list-group-item-action {{ request()->segment(2) == 'sma' ? 'active' : '' }}">
                    SMA
                </a>
            </div>
        </div>
    </div>

    @foreach($ekstrakurikulers as $unit => $items)
        <div class="mb-5">
            <h2 class="mb-4">{{ $unit }}</h2>
            <div class="row">
                @foreach($items as $ekskul)
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            @if($ekskul->gambar)
                                <img src="{{ Storage::url($ekskul->gambar) }}" 
                                     class="card-img-top" 
                                     alt="{{ $ekskul->nama }}">
                            @endif
                            <div class="card-body">
                                <h5 class="card-title">{{ $ekskul->nama }}</h5>
                                <p class="card-text">{{ $ekskul->deskripsi }}</p>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endforeach
</div>

@endsection








