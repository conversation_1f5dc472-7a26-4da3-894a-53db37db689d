@extends('adminlte::page')

@section('title', 'Penilaian Siswa - Proyek')

@section('content_header')
    <h1>Penilaian Siswa - {{ $penilaianProyek->nama_proyek }}</h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Form Penilaian Siswa</h3>
            <div class="card-tools">
                <a href="{{ route('penilaian.proyek.show', $penilaianProyek->id) }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
        </div>
        <div class="card-body">
            <!-- Informasi Proyek -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <table class="table table-sm">
                        <tr>
                            <th width="30%"><PERSON><PERSON></th>
                            <td>: {{ $penilaianProyek->nama_proyek }}</td>
                        </tr>
                        <tr>
                            <th>Kelas</th>
                            <td>: {{ $penilaianProyek->kelas->nama_kelas }}</td>
                        </tr>
                        <tr>
                            <th>Periode</th>
                            <td>: {{ $penilaianProyek->tanggal_mulai->format('d/m/Y') }} - {{ $penilaianProyek->tanggal_selesai->format('d/m/Y') }}</td>
                        </tr>
                        <tr>
                            <th>Dimensi Penilaian</th>
                            <td>: {{ ucwords(str_replace('_', ' ', $penilaianProyek->dimensi_penilaian)) }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Pesan Error -->
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <!-- Form Penilaian -->
            <form action="{{ route('penilaian.proyek.simpanNilaiSiswa', $penilaianProyek->id) }}" method="POST">
                @csrf
                
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th width="5%">No</th>
                                <th width="15%">NIS</th>
                                <th width="30%">Nama Siswa</th>
                                <th width="15%">Nilai</th>
                                <th width="35%">Deskripsi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($siswa as $index => $s)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>{{ $s->nis }}</td>
                                    <td>{{ $s->nama }}</td>
                                    <td>
                                        <select name="nilai[{{ $s->id }}]" class="form-control @error('nilai.'.$s->id) is-invalid @enderror" required>
                                            <option value="">-- Pilih --</option>
                                            <option value="BP" {{ (old('nilai.'.$s->id, $penilaianSiswa[$s->id] ?? '') == 'BP') ? 'selected' : '' }}>BP (Belum Berkembang)</option>
                                            <option value="MB" {{ (old('nilai.'.$s->id, $penilaianSiswa[$s->id] ?? '') == 'MB') ? 'selected' : '' }}>MB (Mulai Berkembang)</option>
                                            <option value="BSH" {{ (old('nilai.'.$s->id, $penilaianSiswa[$s->id] ?? '') == 'BSH') ? 'selected' : '' }}>BSH (Berkembang Sesuai Harapan)</option>
                                            <option value="SB" {{ (old('nilai.'.$s->id, $penilaianSiswa[$s->id] ?? '') == 'SB') ? 'selected' : '' }}>SB (Sangat Berkembang)</option>
                                        </select>
                                    </td>
                                    <td>
                                        <textarea name="deskripsi[{{ $s->id }}]" class="form-control @error('deskripsi.'.$s->id) is-invalid @enderror" rows="2">{{ old('deskripsi.'.$s->id, isset($penilaianSiswa[$s->id]) ? $penilaianSiswa[$s->id]->deskripsi : '') }}</textarea>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="5" class="text-center">Tidak ada data siswa</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">Simpan Penilaian</button>
                    <a href="{{ route('penilaian.proyek.show', $penilaianProyek->id) }}" class="btn btn-secondary">Batal</a>
                </div>
            </form>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
    <script>
        $(document).ready(function() {
            // Jika diperlukan JavaScript tambahan
        });
    </script>
@stop