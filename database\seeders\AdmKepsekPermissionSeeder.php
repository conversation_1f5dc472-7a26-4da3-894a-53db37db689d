<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AdmKepsekPermissionSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Daftar permission baru yang akan ditambahkan
        $newPermissions = [
            'view-adm-kepsek',
            'upload-adm-kepsek',
            'manage-adm-kepsek',
            'approve-adm-kepsek',
            'reject-adm-kepsek'
        ];

        // Buat permission baru jika belum ada
        foreach ($newPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permission ke role yang sesuai
        $rolePermissions = [
            'Kepala Sekolah' => [
                'view-adm-kepsek',
                'upload-adm-kepsek'
            ],
            'Yayasan' => [
                'view-adm-kepsek'
            ],
            'Administrator' => [
                'view-adm-kepsek',
                'manage-adm-kepsek',
                'approve-adm-kepsek',
                'reject-adm-kepsek'
            ],
            'Pengawas' => [
                'view-adm-kepsek',
                'manage-adm-kepsek',
                'approve-adm-kepsek',
                'reject-adm-kepsek'
            ]
        ];

        // Assign permission ke role tanpa menghapus permission yang sudah ada
        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Role::firstOrCreate(['name' => $roleName]);
            foreach ($permissions as $permission) {
                if (!$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                }
            }
        }
    }
}

