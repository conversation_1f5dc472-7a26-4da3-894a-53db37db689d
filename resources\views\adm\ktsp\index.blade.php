@extends('adminlte::page')

@section('title', 'KTSP')

@section('content_header')
    <h1>KTSP (Kurikulum Tingkat Satu<PERSON>)</h1>
@stop

@section('content')
<div class="container">
    @if(auth()->user()->hasPermissionTo('upload-adm-ktsp'))
        <button type="button" class="btn btn-primary mb-3" data-toggle="modal" data-target="#uploadModal">
            <i class="fas fa-upload"></i> Upload KTSP
        </button>
    @endif

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Daftar KTSP</h3>
        </div>
        <div class="card-body">
            <table class="table table-bordered table-striped datatable">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Judul</th>
                        <th>Keterangan</th>
                        <th>Diupload Oleh</th>
                        <th>Tanggal Upload</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($admList as $index => $adm)
                    <tr>
                        <td>{{ $index + 1 }}</td>
                        <td>{{ $adm->judul }}</td>
                        <td>{{ $adm->keterangan ?? '-' }}</td>
                        <td>{{ $adm->user->name }}</td>
                        <td>{{ $adm->created_at->format('d-m-Y H:i') }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ route('adm.ktsp.view-page', basename($adm->file_path)) }}" class="btn btn-sm btn-info" target="_blank">
                                    <i class="fas fa-eye"></i> Lihat
                                </a>
                                <a href="{{ route('adm.ktsp.download', basename($adm->file_path)) }}" class="btn btn-sm btn-success">
                                    <i class="fas fa-download"></i> Unduh
                                </a>
                                @if(auth()->user()->id == $adm->user_id || auth()->user()->hasRole('Administrator'))
                                    <a href="{{ route('adm.ktsp.delete', $adm->id) }}" class="btn btn-sm btn-danger" 
                                       onclick="return confirm('Apakah Anda yakin ingin menghapus KTSP ini?')">
                                        <i class="fas fa-trash"></i> Hapus
                                    </a>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1" role="dialog" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadModalLabel">Upload KTSP</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('adm.ktsp.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="judul">Judul</label>
                        <input type="text" class="form-control" id="judul" name="judul" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="keterangan">Keterangan</label>
                        <textarea class="form-control" id="keterangan" name="keterangan" rows="3"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="file">File (PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX)</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="file" name="file" required>
                            <label class="custom-file-label" for="file">Pilih file...</label>
                        </div>
                        <small class="form-text text-muted">Maksimal ukuran file: 10MB</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Upload</button>
                </div>
            </form>
        </div>
    </div>
</div>

@stop

@section('css')
<link rel="stylesheet" href="/css/admin_custom.css">
@stop

@section('js')
<script>
    $(function () {
        $('.datatable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Indonesian.json"
            }
        });
        
        $('[data-toggle="tooltip"]').tooltip();
        
        // Menampilkan nama file yang dipilih
        $(".custom-file-input").on("change", function() {
            var namaFile = $(this).val().split("\\").pop();
            $(this).siblings(".custom-file-label").addClass("selected").html(namaFile);
        });
    });
</script>
@stop




