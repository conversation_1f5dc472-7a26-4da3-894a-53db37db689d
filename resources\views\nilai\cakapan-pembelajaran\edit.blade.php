@extends('adminlte::page')

@section('title', 'Edit Cakapan Pembelajaran')

@section('content_header')
    <h1>Edit Cakapan Pembelajaran</h1>
@stop

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Form Edit Cakapan Pembelajaran</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('penilaian.cakapan-pembelajaran.update', $cakapanPembelajaran->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="form-group">
                            <label for="kompetensi_dasar_id">Kompetensi Dasar</label>
                            <select name="kompetensi_dasar_id" id="kompetensi_dasar_id" class="form-control @error('kompetensi_dasar_id') is-invalid @enderror" required>
                                <option value="">-- <PERSON><PERSON><PERSON> --</option>
                                @foreach($kompetensiDasars as $kd)
                                    <option value="{{ $kd->id }}" {{ (old('kompetensi_dasar_id', $cakapanPembelajaran->kompetensi_dasar_id) == $kd->id) ? 'selected' : '' }}>
                                        {{ $kd->kode }} - {{ $kd->deskripsi }}
                                    </option>
                                @endforeach
                            </select>
                            @error('kompetensi_dasar_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="form-group">
                            <label for="deskripsi">Deskripsi Cakapan Pembelajaran</label>
                            <textarea name="deskripsi" id="deskripsi" class="form-control @error('deskripsi') is-invalid @enderror" rows="4" required>{{ old('deskripsi', $cakapanPembelajaran->deskripsi) }}</textarea>
                            @error('deskripsi')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="form-group">
                            <a href="{{ route('penilaian.cakapan-pembelajaran.index') }}" class="btn btn-secondary">Batal</a>
                            <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <link rel="stylesheet" href="/css/admin_custom.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
@stop

@section('js')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#kompetensi_dasar_id').select2({
                theme: 'bootstrap4',
                placeholder: "-- Pilih Kompetensi Dasar --"
            });
        });
    </script>
@stop