@extends('adminlte::page') {{-- Menggunakan template AdminLTE --}}

@section('title', 'Daftar Kelas') {{-- <PERSON><PERSON><PERSON> halaman yang akan ditampilkan di tab browser --}}

@section('content_header')
    <h1>Daftar Kelas - Tahun <PERSON>an {{ $tahunAjaran->nama ?? 'Aktif' }}</h1> {{-- Header konten dengan tahun ajaran aktif --}}
@stop

@section('content')
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Daftar Kelas</h3> {{-- Judul card --}}
        </div>
        <div class="card-body">
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }} {{-- Menampilkan pesan sukses jika ada --}}
                </div>
            @endif

            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="tabel-kelas">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th><PERSON><PERSON></th>
                            <th>Tin<PERSON></th>
                            <th><PERSON><PERSON></th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($kelas as $k) {{-- Loop untuk setiap kelas --}}
                            <tr>
                                <td>{{ $loop->iteration }}</td> {{-- Nomor urut --}}
                                <td>{{ $k->nama }}</td> {{-- Nama kelas --}}
                                <td>{{ $k->tingkat }}</td> {{-- Tingkat kelas --}}
                                <td>{{ $k->wali_kelas ?? 'Belum ditentukan' }}</td> {{-- Nama wali kelas atau 'Belum ditentukan' jika kosong --}}
                                <td>{{ $k->siswa->count() }}</td> {{-- Jumlah siswa di kelas --}}
                                <td>
                                    <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#detailKelas{{ $k->id }}">
                                        <i class="fas fa-eye"></i> Detail {{-- Tombol untuk melihat detail kelas --}}
                                    </button>
                                </td>
                            </tr>
                        @empty {{-- Jika tidak ada data kelas --}}
                            <tr>
                                <td colspan="6" class="text-center">Belum ada data kelas</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@stop

@section('css')
    <link rel="stylesheet" href="{{ asset('css/admin_custom.css') }}"> {{-- CSS kustom --}}
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css"> {{-- CSS DataTables --}}
@stop

@section('js')
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script>
        $(document).ready(function() {
            // Inisialisasi DataTables
            $('#tabel-kelas').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                }
            });
            
            // Pastikan modal tidak terbuka secara otomatis
            $('.modal').modal({
                show: false
            });
        });
    </script>
@stop

<!-- Semua modal di luar tabel -->
@foreach($kelas as $k)
    <div class="modal fade" id="detailKelas{{ $k->id }}" tabindex="-1" role="dialog" aria-labelledby="detailKelasLabel{{ $k->id }}" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detailKelasLabel{{ $k->id }}">Detail Kelas {{ $k->nama }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <h6>Daftar Siswa:</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>NIS</th>
                                    <th>NISN</th>
                                    <th>Nama</th>
                                    <th>Jenis Kelamin</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($k->siswa as $s)
                                    <tr>
                                        <td>{{ $loop->iteration }}</td>
                                        <td>{{ $s->nis }}</td>
                                        <td>{{ $s->nisn }}</td>
                                        <td>{{ $s->nama }}</td>
                                        <td>{{ $s->jenis_kelamin == 'L' ? 'Laki-laki' : 'Perempuan' }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center">Belum ada siswa di kelas ini</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    </div>
@endforeach

