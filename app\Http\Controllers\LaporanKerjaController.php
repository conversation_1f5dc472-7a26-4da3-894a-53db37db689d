<?php

namespace App\Http\Controllers;

use App\Models\LaporanKerja;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Log;

class LaporanKerjaController extends BaseController
{
    public function __construct()
    {
        // Gunakan middleware di route daripada di controller
    }

    public function index()
    {
        $laporanKerja = LaporanKerja::with('user')->latest()->get();
        return view('laporan-kerja.index', compact('laporanKerja'));
    }

    public function create()
    {
        return view('laporan-kerja.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'uraian_kerja' => 'required|string',
            'waktu_mulai' => 'required|string',
            'waktu_selesai' => $request->status === 'selesai' ? 'required|string' : 'nullable|string',
            'status' => 'required|in:selesai,proses,tertunda',
            'keterangan' => 'nullable|string',
        ]);

        // Konversi format waktu mulai dari text ke datetime
        $waktuMulai = \Carbon\Carbon::createFromFormat('Y-m-d H:i', $request->waktu_mulai);
        
        // Inisialisasi data laporan
        $laporanData = [
            'uraian_kerja' => $request->uraian_kerja,
            'waktu_mulai' => $waktuMulai,
            'status' => $request->status,
            'keterangan' => $request->keterangan,
            'user_id' => Auth::id(),
            'waktu_selesai' => null, // Eksplisit set null
        ];

        // Hanya isi waktu_selesai jika status selesai dan field diisi
        if ($request->status === 'selesai' && $request->filled('waktu_selesai')) {
            $waktuSelesai = \Carbon\Carbon::createFromFormat('Y-m-d H:i', $request->waktu_selesai);
            
            // Validasi waktu selesai harus setelah waktu mulai
            if ($waktuSelesai->lessThanOrEqualTo($waktuMulai)) {
                return back()
                    ->withInput()
                    ->withErrors(['waktu_selesai' => 'Waktu selesai harus setelah waktu mulai']);
            }
            
            $laporanData['waktu_selesai'] = $waktuSelesai;
        }

        // Debug untuk memastikan waktu_selesai benar-benar null
        // dd($laporanData);

        Log::info('Data laporan yang akan disimpan:', $laporanData);

        LaporanKerja::create($laporanData);

        Log::info('Data laporan yang tersimpan:', LaporanKerja::latest()->first()->toArray());

        return redirect()->route('laporan-kerja.index')
            ->with('success', 'Laporan kerja berhasil ditambahkan');
    }

    public function show(LaporanKerja $laporanKerja)
    {
        return view('laporan-kerja.show', compact('laporanKerja'));
    }

    public function edit(LaporanKerja $laporanKerja)
    {
        return view('laporan-kerja.edit', compact('laporanKerja'));
    }

    public function update(Request $request, LaporanKerja $laporanKerja)
    {
        $request->validate([
            'uraian_kerja' => 'required|string',
            'waktu_mulai' => 'required|string',
            'waktu_selesai' => 'nullable|string',
            'status' => 'required|in:selesai,proses,tertunda',
            'keterangan' => 'nullable|string',
        ]);

        // Konversi format waktu mulai dari text ke datetime
        $waktuMulai = \Carbon\Carbon::createFromFormat('Y-m-d H:i', $request->waktu_mulai);
        
        // Inisialisasi data laporan
        $laporanData = [
            'uraian_kerja' => $request->uraian_kerja,
            'waktu_mulai' => $waktuMulai,
            'status' => $request->status,
            'keterangan' => $request->keterangan,
            'waktu_selesai' => null, // Default null
        ];

        // Jika status selesai dan waktu selesai diisi, validasi dan gunakan
        if ($request->status === 'selesai' && $request->filled('waktu_selesai')) {
            $waktuSelesai = \Carbon\Carbon::createFromFormat('Y-m-d H:i', $request->waktu_selesai);
            
            // Validasi waktu selesai harus setelah waktu mulai
            if ($waktuSelesai->lessThanOrEqualTo($waktuMulai)) {
                return back()
                    ->withInput()
                    ->withErrors(['waktu_selesai' => 'Waktu selesai harus setelah waktu mulai']);
            }
            
            $laporanData['waktu_selesai'] = $waktuSelesai;
        } elseif ($request->status === 'selesai' && !$request->filled('waktu_selesai')) {
            // Jika status selesai tapi waktu selesai tidak diisi
            return back()
                ->withInput()
                ->withErrors(['waktu_selesai' => 'Waktu selesai harus diisi jika status pekerjaan selesai']);
        }

        Log::info('Data laporan yang akan disimpan:', $laporanData);

        $laporanKerja->update($laporanData);

        Log::info('Data laporan yang tersimpan:', LaporanKerja::latest()->first()->toArray());

        return redirect()->route('laporan-kerja.index')
            ->with('success', 'Laporan kerja berhasil diperbarui');
    }

    public function destroy(LaporanKerja $laporanKerja)
    {
        $laporanKerja->delete();

        return redirect()->route('laporan-kerja.index')
            ->with('success', 'Laporan kerja berhasil dihapus');
    }
}









