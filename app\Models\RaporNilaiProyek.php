<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RaporNilaiProyek extends Model
{
    use HasFactory;

    protected $table = 'rapor_nilai_proyeks';

    protected $fillable = [
        'rapor_siswa_id',
        'penilaian_proyek_id',
        'nilai',
        'deskripsi',
    ];

    public function raporSiswa()
    {
        return $this->belongsTo(RaporSiswa::class);
    }

    public function penilaianProyek()
    {
        return $this->belongsTo(PenilaianProyek::class);
    }
}