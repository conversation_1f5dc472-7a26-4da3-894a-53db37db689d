<?php

namespace App\Http\Controllers;

use App\Models\Unit;
use Illuminate\Http\Request;

class UnitController extends Controller
{
    public function index()
    {
        $units = Unit::all();
        return view('pengaturan.unit', compact('units'));
    }

    public function store(Request $request)
    {
        // Validasi input
        $validated = $request->validate([
            'jenjang_id' => 'required|in:PG,SD,SMP,SMA',
            'nama_unit' => 'required|string|max:255',
            'nss' => 'required|string|max:50|unique:units',
            'npsn' => 'required|string|max:50|unique:units',
            'no_telepon' => 'required|string|max:20',
            'alamat' => 'required|string'
        ]);

        // Simpan data unit
        Unit::create($validated);

        return redirect()->route('pengaturan.unit')
            ->with('success', 'Data unit berhasil ditambahkan');
    }

    public function update(Request $request, Unit $unit)
    {
        // Validasi input
        $validated = $request->validate([
            'jenjang_id' => 'required|in:PG,SD,SMP,SMA',
            'nama_unit' => 'required|string|max:255',
            'nss' => 'required|string|max:50|unique:units,nss,' . $unit->id,
            'npsn' => 'required|string|max:50|unique:units,npsn,' . $unit->id,
            'no_telepon' => 'required|string|max:20',
            'alamat' => 'required|string'
        ]);

        // Update data unit
        $unit->update($validated);

        return redirect()->route('pengaturan.unit')
            ->with('success', 'Data unit berhasil diperbarui');
    }

    public function destroy(Unit $unit)
    {
        $unit->delete();
        return redirect()->route('pengaturan.unit')
            ->with('success', 'Data unit berhasil dihapus');
    }
}
