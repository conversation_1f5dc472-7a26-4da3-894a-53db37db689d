<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lihat Dokumen KTSP</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }
        #document-container {
            width: 100%;
            height: 100vh;
            position: relative;
        }
        #document-viewer {
            width: 100%;
            height: 100%;
            border: none;
            position: absolute;
            top: 0;
            left: 0;
        }
        .not-supported {
            padding: 20px;
            text-align: center;
            max-width: 600px;
            margin: 50px auto;
            background-color: #f8f9fa;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .download-link {
            display: block;
            margin: 20px auto;
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            width: 200px;
            text-align: center;
        }
        .file-info {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div id="document-container">
        @php
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            $isPdf = strtolower($extension) === 'pdf';
            $isOffice = in_array(strtolower($extension), ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']);
        @endphp

        @if($isPdf)
            <object
                id="document-viewer"
                data="{{ route('adm.ktsp.view', ['filename' => $filename]) }}"
                type="application/pdf"
            >
                <div class="not-supported">
                    <p>Browser Anda tidak mendukung tampilan PDF. Silakan unduh file untuk melihatnya.</p>
                    <a href="{{ route('adm.ktsp.download', ['filename' => $filename]) }}" class="download-link">Unduh File</a>
                </div>
            </object>
        @elseif($isOffice)
            <div class="not-supported">
                <h3>Dokumen Office ({{ strtoupper($extension) }})</h3>
                <div class="file-info">
                    <p><strong>Nama File:</strong> {{ $filename }}</p>
                    <p><strong>Tipe:</strong> {{ strtoupper($extension) }} Document</p>
                </div>
                <p>Dokumen Office tidak dapat ditampilkan di lingkungan lokal (localhost).</p>
                <p>Untuk melihat dokumen ini, silakan:</p>
                <ol style="text-align: left;">
                    <li>Unduh file menggunakan tombol di bawah</li>
                    <li>Buka dengan aplikasi Office yang sesuai di komputer Anda</li>
                </ol>
                <p>Atau deploy aplikasi ke server publik untuk menggunakan Microsoft Office Online Viewer.</p>
                <a href="{{ route('adm.ktsp.download', ['filename' => $filename]) }}" class="download-link">Unduh File</a>
                <a href="{{ route('adm.ktsp.index') }}" class="download-link" style="background-color: #6c757d;">Kembali</a>
            </div>
        @else
            <div class="not-supported">
                <h3>Format File Tidak Didukung</h3>
                <div class="file-info">
                    <p><strong>Nama File:</strong> {{ $filename }}</p>
                    <p><strong>Tipe:</strong> {{ strtoupper($extension) }}</p>
                </div>
                <p>Format file tidak didukung untuk ditampilkan secara langsung. Silakan unduh file untuk melihatnya.</p>
                <a href="{{ route('adm.ktsp.download', ['filename' => $filename]) }}" class="download-link">Unduh File</a>
                <a href="{{ route('adm.ktsp.index') }}" class="download-link" style="background-color: #6c757d;">Kembali</a>
            </div>
        @endif
    </div>
</body>
</html>

