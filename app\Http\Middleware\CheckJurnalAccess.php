<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckJurnalAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        
        // Cek approval access
        if ($request->is('*/approval') || $request->is('*/approve/*')) {
            if (!$user->hasPermissionTo('approve-jurnal')) {
                return redirect()->route('dashboard')
                    ->with('error', 'Anda tidak memiliki izin untuk menyetujui jurnal.');
            }
        }
        // Cek general access
        else if (!$user->hasAnyPermission(['view-jurnal', 'manage-jurnal'])) {
            return redirect()->route('dashboard')
                ->with('error', 'Anda tidak memiliki izin untuk mengakses jurnal.');
        }

        return $next($request);
    }
}