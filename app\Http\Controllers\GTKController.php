<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class GTKController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:view-gtk');
        $this->middleware('permission:manage-gtk')->only(['create', 'store', 'edit', 'update', 'destroy']);
    }

    public function guru()
    {
        return view('gtk.guru');
    }

    public function tendik()
    {
        return view('gtk.tendik');
    }

    /**
     * Menampilkan daftar GTK dengan status Non-Aktif
     *
     * @return \Illuminate\View\View
     */
    public function nonAktif()
    {
        // Ambil semua GTK yang berstatus Non-Aktif
        $gtkNonAktif = \App\Models\Guru::where('status', 'Non-Aktif')
                                      ->orderBy('updated_at', 'desc')
                                      ->get();
        
        // Tambahkan variabel jenjang yang dibutuhkan view
        $jenjang = request('jenjang', 'all'); // Default 'all' jika tidak ada parameter jenjang
        
        return view('gtk.non-aktif.index', compact('gtkNonAktif', 'jenjang'));
    }

    /**
     * Mengaktifkan kembali GTK yang berstatus Non-Aktif
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function aktivasi($id)
    {
        try {
            $gtk = \App\Models\Guru::findOrFail($id);
            $gtk->status = 'Aktif';
            $gtk->save();
            
            return redirect()->route('gtk.non-aktif')->with('success', 'GTK berhasil diaktifkan kembali');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Gagal mengaktifkan GTK: ' . $e->getMessage());
        }
    }
}
