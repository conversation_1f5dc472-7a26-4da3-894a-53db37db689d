<?php

namespace App\Http\Controllers;

use App\Models\Sarana;
use App\Models\Unit;
use App\Models\Gedung;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class SaranaController extends Controller implements HasMiddleware
{
    /**
     * Dapatkan middleware yang harus ditetapkan ke controller
     */
    public static function middleware(): array
    {
        return [
            new Middleware('permission:view-sarpras', only: ['index', 'show', 'showlokasi', 'lokasi', 'laporKerusakan']),
            new Middleware('permission:create-sarpras', only: ['create', 'store']),
            new Middleware('permission:edit-sarpras', only: ['edit', 'update']),
            new Middleware('permission:delete-sarpras', only: ['destroy']),
        ];
    }

    // Daftar jenis sarana yang tersedia
    protected $jenisSarana = [
        'Meja',
        'Kursi',
        'Papan Tulis',
        'Le<PERSON><PERSON>',
        'Komputer',
        'Printer',
        'Proyektor',
        'AC',
        'Ki<PERSON> Angin',
        'Alat Peraga',
        'Buku',
        'Peralatan Olahraga',
        'Peralatan Laboratorium',
        'Peralatan Musik',
        'Lainnya'
    ];
    
    /**
     * Menampilkan daftar sarana dan prasarana
     */
    public function index(Request $request)
    {
        // Query dasar
        $query = Sarana::with(['unit', 'gedung']);
        
        // Filter berdasarkan unit
        if ($request->filled('unit_id')) {
            $query->where('unit_id', $request->unit_id);
        }
        
        // Filter berdasarkan gedung (lokasi)
        if ($request->filled('gedung_id')) {
            $query->where('gedung_id', $request->gedung_id);
        }
        
        // Filter berdasarkan kondisi
        if ($request->filled('kondisi')) {
            $query->where('kondisi', $request->kondisi);
        }
        
        // Ambil data sarana
        $sarana = $query->latest()->get();
        
        // Hitung jumlah sarana berdasarkan kondisi untuk statistik
        $statistik = [
            'total' => $sarana->count(),
            'baik' => $sarana->where('kondisi', 'Baik')->count(),
            'rusak_ringan' => $sarana->where('kondisi', 'Rusak Ringan')->count(),
            'rusak_berat' => $sarana->where('kondisi', 'Rusak Berat')->count(),
        ];
        
        // Ambil data unit dan gedung untuk filter
        $units = Unit::all();
        $gedungs = Gedung::with('unit')->get();
        
        return view('sarana.index', compact('sarana', 'statistik', 'units', 'gedungs'));
    }

    /**
     * Menampilkan form untuk membuat sarana baru
     */
    public function create()
    {
        $units = Unit::all();
        $gedungs = Gedung::with('unit')->get();
        $jenisSarana = $this->jenisSarana;
        
        return view('sarana.create', compact('units', 'gedungs', 'jenisSarana'));
    }

    /**
     * Menyimpan data sarana baru
     */
    public function store(Request $request)
    {
        // Tambahkan logging untuk debugging
        \Log::info('Sarana store method dipanggil', $request->all());
        
        // Validasi input
        $validated = $request->validate([
            'nama_sarana' => 'required|string|max:255',
            'no_barang' => 'nullable|string|max:100',
            'jenis' => 'required|string|max:100',
            'jumlah' => 'required|integer|min:1',
            'kondisi' => 'required|in:Baik,Rusak Ringan,Rusak Berat',
            'tahun_pengadaan' => 'nullable|numeric|digits:4',
            'keterangan' => 'nullable|string',
            'unit_id' => 'required|exists:units,id',
            'gedung_id' => 'nullable|exists:gedungs,id',
            'foto' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);
        
        \Log::info('Validasi berhasil', $validated);
        
        try {
            DB::beginTransaction();
            
            // Proses upload foto jika ada
            if ($request->hasFile('foto')) {
                $path = $request->file('foto')->store('sarana', 'public');
                $validated['foto'] = $path;
                \Log::info('Foto berhasil diupload', ['path' => $path]);
            }
            
            // Simpan data sarana
            $sarana = Sarana::create($validated);
            \Log::info('Sarana berhasil dibuat', ['id' => $sarana->id]);
            
            DB::commit();
            return redirect()->route('sarana.index')
                ->with('success', 'Data sarana berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('Error saat menyimpan sarana: ' . $e->getMessage());
            \Log::error($e->getTraceAsString());
            
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Menampilkan detail sarana
     */
    public function show(Sarana $sarana)
    {
        // Load relasi gedung
        $sarana->load(['unit', 'gedung']);
        return view('sarana.show', compact('sarana'));
    }

    
    /**
     * Menampilkan form untuk mengedit sarana
     */
    public function edit(Sarana $sarana)
    {
        $units = Unit::all();
        $gedungs = Gedung::with('unit')->get();
        $jenisSarana = $this->jenisSarana;
        
        return view('sarana.edit', compact('sarana', 'units', 'gedungs', 'jenisSarana'));
    }

    /**
     * Memperbarui data sarana
     */
    public function update(Request $request, Sarana $sarana)
    {
        // Validasi input
        $validated = $request->validate([
            'nama_sarana' => 'required|string|max:255',
            'no_barang' => 'nullable|string|max:100',
            'jenis' => 'required|string|max:100',
            'jumlah' => 'required|integer|min:1',
            'kondisi' => 'required|in:Baik,Rusak Ringan,Rusak Berat',
            'tahun_pengadaan' => 'nullable|numeric|digits:4',
            'keterangan' => 'nullable|string',
            'unit_id' => 'required|exists:units,id',
            'gedung_id' => 'nullable|exists:gedungs,id',
            'foto' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);
        
        try {
            DB::beginTransaction();
            
            // Proses upload foto jika ada
            if ($request->hasFile('foto')) {
                // Hapus foto lama jika ada
                if ($sarana->foto) {
                    Storage::disk('public')->delete($sarana->foto);
                }
                
                $path = $request->file('foto')->store('sarana', 'public');
                $validated['foto'] = $path;
            }
            
            // Update data sarana
            $sarana->update($validated);
            
            DB::commit();
            return redirect()->route('sarana.index')
                ->with('success', 'Data sarana berhasil diperbarui');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Menghapus data sarana
     */
    public function destroy(Sarana $sarana)
    {
        try {
            // Hapus foto jika ada
            if ($sarana->foto) {
                Storage::disk('public')->delete($sarana->foto);
            }
            
            $sarana->delete();
            
            return redirect()->route('sarana.index')
                ->with('success', 'Data sarana berhasil dihapus');
        } catch (\Exception $e) {
            return redirect()->route('sarana.index')
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }
    
    /**
     * Menampilkan sarana berdasarkan lokasi/gedung
     */
    public function byLokasi($gedung_id)
    {
        $gedung = Gedung::findOrFail($gedung_id);
        $sarana = Sarana::where('gedung_id', $gedung_id)->with(['unit', 'gedung'])->get();
        
        return view('sarana.by_lokasi', compact('sarana', 'gedung'));
    }

    /**
     * Menampilkan daftar lokasi beserta jumlah sarana
     */
    public function lokasiIndex()
    {
        $gedungs = Gedung::with('unit')
            ->withCount(['sarana as sarana_count'])
            ->withCount(['sarana as sarana_baik_count' => function($query) {
                $query->where('kondisi', 'Baik');
            }])
            ->withCount(['sarana as sarana_rusak_ringan_count' => function($query) {
                $query->where('kondisi', 'Rusak Ringan');
            }])
            ->withCount(['sarana as sarana_rusak_berat_count' => function($query) {
                $query->where('kondisi', 'Rusak Berat');
            }])
            ->get();
        
        return view('sarana.lokasi', compact('gedungs'));
    }

    /**
     * Menampilkan detail sarana dengan fokus pada lokasi
     */
    public function showLokasi(Sarana $sarana)
    {
        // Load relasi unit dan gedung
        $sarana->load(['unit', 'gedung']);
        
        // Tampilkan view showlokasi dengan data sarana
        return view('sarana.showlokasi', compact('sarana'));
    }
}








