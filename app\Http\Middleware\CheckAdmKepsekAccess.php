<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CheckAdmKepsekAccess
{
    public function handle(Request $request, Closure $next)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        
        // Cek apakah user memiliki role yang sesuai
        if (!$user->hasAnyRole(['Kepala Sekolah', 'Yayasan', 'Administrator', 'Pengawas'])) {
            return redirect()->route('dashboard')
                ->with('error', 'Anda tidak memiliki izin untuk mengakses ADM Kepala Sekolah.');
        }
        
        // Cek khusus untuk approve/reject
        if ($request->is('*/approve') || $request->is('*/reject')) {
            if (!$user->hasPermissionTo('approve-adm-kepsek')) {
                return redirect()->route('dashboard')
                    ->with('error', 'Anda tidak memiliki izin untuk menyetujui/menolak ADM Kepala Sekolah.');
            }
        }

        return $next($request);
    }
}
