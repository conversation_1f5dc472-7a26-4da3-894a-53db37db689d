<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class UpdateUserRolesSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Update Administrator
        User::where('name', 'Administrator')
            ->orWhere('name', 'Administrator 2')
            ->each(function ($user) {
                $user->assignRole('Administrator');
            });

        // Update Waka Kurikulum
        User::where('name', 'Waka Kurikulum')
            ->each(function ($user) {
                $user->assignRole('Waka Kurikulum');
            });

        // Update Guru
        User::where('name', '<PERSON> Matematika')
            ->each(function ($user) {
                $user->assignRole('Guru');
            });

        // Update other users as needed
        User::where('name', 'Dita')
            ->each(function ($user) {
                $user->assignRole('Guru');
            });
    }
}