<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class WebsitePermissionSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles dan permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Daftar permission baru untuk website
        $newPermissions = [
            'view-website',                  // Untuk melihat menu website
            'manage-website',                // Untuk mengelola website secara umum
            'view-website-artikel',          // Untuk melihat artikel
            'manage-website-artikel',        // Untuk mengelola artikel
            'view-website-event',            // Untuk melihat event
            'manage-website-event',          // Untuk mengelola event
            'view-website-prestasi',         // Untuk melihat prestasi
            'manage-website-prestasi',       // Untuk mengelola prestasi
            'view-website-fasilitas',        // Untuk melihat fasilitas
            'manage-website-fasilitas',      // Untuk mengelola fasilitas
            'view-website-ekstrakurikuler',  // Untuk melihat ekstrakurikuler
            'manage-website-ekstrakurikuler',// Untuk mengelola ekstrakurikuler
            'view-website-slide',            // Untuk melihat slide
            'manage-website-slide',          // Untuk mengelola slide
            'view-website-halaman',          // Untuk melihat halaman
            'manage-website-halaman',        // Untuk mengelola halaman
        ];

        // Buat permission baru jika belum ada
        foreach ($newPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permission ke role yang sesuai
        $rolePermissions = [
            'Administrator' => [
                'view-website',
                'manage-website',
                'view-website-artikel',
                'manage-website-artikel',
                'view-website-event',
                'manage-website-event',
                'view-website-prestasi',
                'manage-website-prestasi',
                'view-website-fasilitas',
                'manage-website-fasilitas',
                'view-website-ekstrakurikuler',
                'manage-website-ekstrakurikuler',
                'view-website-slide',
                'manage-website-slide',
                'view-website-halaman',
                'manage-website-halaman',
            ],
            'Kepala Sekolah' => [
                'view-website',
                'view-website-artikel',
                'view-website-event',
                'view-website-prestasi',
                'view-website-fasilitas',
                'view-website-ekstrakurikuler',
                'view-website-slide',
                'view-website-halaman',
            ],
            'Waka Kurikulum' => [
                'view-website',
                'manage-website',
                'view-website-artikel',
                'manage-website-artikel',
                'view-website-event',
                'manage-website-event',
                'view-website-prestasi',
                'manage-website-prestasi',
                'view-website-fasilitas',
                'manage-website-fasilitas',
                'view-website-ekstrakurikuler',
                'manage-website-ekstrakurikuler',
                'view-website-slide',
                'manage-website-slide',
                'view-website-halaman',
                'manage-website-halaman',
            ],
            'Waka Kesiswaan' => [
                'view-website',
                'view-website-prestasi',
                'manage-website-prestasi',
                'view-website-ekstrakurikuler',
                'manage-website-ekstrakurikuler',
            ],
            'Waka Sarpras' => [
                'view-website',
                'view-website-fasilitas',
                'manage-website-fasilitas',
            ],
            'Tata Usaha' => [
                'view-website',
                'view-website-artikel',
                'manage-website-artikel',
                'view-website-event',
                'manage-website-event',
            ],
        ];

        // Assign permission ke role tanpa menghapus permission yang sudah ada
        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Role::firstOrCreate(['name' => $roleName]);
            foreach ($permissions as $permission) {
                if (!$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                }
            }
        }
    }
}