<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Jalankan migrasi.
     */
    public function up(): void
    {
        Schema::create('rombel_ekstrakurikuler', function (Blueprint $table) { // Ubah nama tabel tanpa 's'
            $table->id(); // Kolom ID sebagai primary key
            $table->string('nama_rombel'); // Nama rombongan belajar
            $table->string('ekstrakurikuler'); // Nama ekstrakurikuler (disimpan sebagai teks)
            $table->string('tahun_ajaran'); // Tahun ajaran (contoh: 2023/2024)
            $table->string('pembina'); // Nama pembina ekstrakurikuler
            $table->timestamps(); // created_at dan updated_at
        });
    }

    /**
     * Batalkan migrasi.
     */
    public function down(): void
    {
        Schema::dropIfExists('rombel_ekstrakurikuler'); // Ubah nama tabel tanpa 's'
    }
};
