<?php

namespace App\Http\Controllers;

use App\Models\TahunAjaran;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TahunAjaranController extends Controller
{
    public function index()
    {
        $tahunAjarans = TahunAjaran::orderBy('nama', 'desc')->get();
        return view('pengaturan.tahunajaran', compact('tahunAjarans'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'nama' => 'required|string|unique:tahun_ajaran,nama',
            'tanggal_mulai' => 'required|date',
            'tanggal_selesai' => 'required|date|after:tanggal_mulai',
            'aktif' => 'boolean'
        ]);

        try {
            DB::beginTransaction();
            
            // If new record is set as active, deactivate all others
            if ($request->has('aktif')) {
                TahunAjaran::where('aktif', true)->update(['aktif' => false]);
            }
            
            TahunAjaran::create($validated);
            DB::commit();
            
            return redirect()->route('pengaturan.tahunajaran')
                ->with('success', 'Tahun ajaran berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('pengaturan.tahunajaran')
                ->with('error', 'Terjadi kesalahan saat menambahkan tahun ajaran');
        }
    }

    public function update(Request $request, TahunAjaran $tahunAjaran)
    {
        $validated = $request->validate([
            'nama' => 'required|string|unique:tahun_ajaran,nama,' . $tahunAjaran->id,
            'tanggal_mulai' => 'required|date',
            'tanggal_selesai' => 'required|date|after:tanggal_mulai',
            'aktif' => 'boolean'
        ]);

        try {
            DB::beginTransaction();
            
            // If this record is set as active, deactivate all others
            if ($request->has('aktif') && !$tahunAjaran->aktif) {
                TahunAjaran::where('aktif', true)->update(['aktif' => false]);
            }
            
            $tahunAjaran->update($validated);
            DB::commit();
            
            return redirect()->route('pengaturan.tahunajaran')
                ->with('success', 'Tahun ajaran berhasil diperbarui');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('pengaturan.tahunajaran')
                ->with('error', 'Terjadi kesalahan saat memperbarui tahun ajaran');
        }
    }

    public function destroy(TahunAjaran $tahunAjaran)
    {
        try {
            DB::beginTransaction();
            
            // Check if this tahun ajaran is used in kelas
            if ($tahunAjaran->kelas()->count() > 0) {
                return redirect()->route('pengaturan.tahunajaran')
                    ->with('error', 'Tahun ajaran tidak dapat dihapus karena masih digunakan oleh kelas');
            }
            
            $tahunAjaran->delete();
            DB::commit();
            
            return redirect()->route('pengaturan.tahunajaran')
                ->with('success', 'Tahun ajaran berhasil dihapus');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('pengaturan.tahunajaran')
                ->with('error', 'Terjadi kesalahan saat menghapus tahun ajaran');
        }
    }

    public function setActive(TahunAjaran $tahunAjaran)
    {
        try {
            DB::beginTransaction();
            
            // Deactivate all tahun ajaran
            TahunAjaran::where('aktif', true)->update(['aktif' => false]);
            
            // Activate the selected one
            $tahunAjaran->aktif = true;
            $tahunAjaran->save();
            
            DB::commit();
            
            return redirect()->route('pengaturan.tahunajaran')
                ->with('success', 'Tahun ajaran ' . $tahunAjaran->nama . ' berhasil diaktifkan');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->route('pengaturan.tahunajaran')
                ->with('error', 'Terjadi kesalahan saat mengaktifkan tahun ajaran');
        }
    }
}