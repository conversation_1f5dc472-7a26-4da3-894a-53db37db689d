<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class SyncGuruAdmPermissions extends Command
{
    protected $signature = 'guru:sync-adm-permissions {email?}';
    protected $description = 'Sync ADM permissions for existing guru';

    public function handle()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        $email = $this->argument('email');
        $query = User::role('Guru');

        if ($email) {
            $query->where('email', $email);
        }

        $users = $query->get();

        foreach ($users as $user) {
            // Tambah permission baru tanpa menghapus yang lama
            $user->givePermissionTo([
                'view-adm-guru',
                'upload-adm-guru'
            ]);

            $this->info("Updated permissions for: {$user->name} ({$user->email})");
        }

        $this->info('ADM Guru permissions have been synced successfully!');
    }
}
