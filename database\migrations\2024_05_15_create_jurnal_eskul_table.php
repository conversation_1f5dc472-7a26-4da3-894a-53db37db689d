<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('jurnal_eskul', function (Blueprint $table) {
            $table->id();
            $table->foreignId('jurnal_kegiatan_id')->constrained('jurnal_kegiatan')->onDelete('cascade');
            $table->string('nama_eskul');
            $table->string('kelas');
            $table->integer('jumlah_siswa')->default(1);
            $table->string('kegiatan');
            $table->decimal('jumlah_jam', 5, 2)->default(1);
            $table->text('keterangan')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('jurnal_eskul');
    }
};