<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MataPelajaran extends Model
{
    protected $table = 'mata_pelajaran';
    protected $fillable = [
        'unit_id',
        'nama_mapel',
        'pengajar_id'
    ];

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function pengajar()
    {
        return $this->belongsTo(User::class, 'pengajar_id');
    }

    public function detailJadwal()
    {
        return $this->hasMany(DetailJadwal::class, 'mata_pelajaran_id');
    }
}





