<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class JadwalExport implements WithMultipleSheets
{
    protected $jadwalList;

    public function __construct($jadwalList)
    {
        $this->jadwalList = $jadwalList;
    }

    public function sheets(): array
    {
        $sheets = [];
        foreach ($this->jadwalList as $jadwal) {
            $sheets[] = new JadwalPerKelasSheet($jadwal);
        }
        return $sheets;
    }
}
