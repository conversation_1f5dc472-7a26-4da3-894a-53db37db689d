<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AdmKtsp;
use App\Models\User;

class UpdateAdmKtspUnitIdSeeder extends Seeder
{
    public function run()
    {
        // Get all ADM KTSP records without unit_id
        $admKtsps = AdmKtsp::whereNull('unit_id')->get();
        
        foreach ($admKtsps as $adm) {
            // Find the user's unit_id
            $user = User::find($adm->user_id);
            
            if ($user && $user->unit_id) {
                // Update the ADM KTSP record with the user's unit_id
                $adm->update(['unit_id' => $user->unit_id]);
            }
        }
        
        $this->command->info('Updated unit_id for ' . count($admKtsps) . ' ADM KTSP records.');
    }
}