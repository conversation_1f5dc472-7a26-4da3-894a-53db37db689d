<?php

namespace App\Http\Controllers;

use App\Models\Guru;
use App\Models\PengembanganDiri;
use Illuminate\Http\Request;

class PengembanganDiriController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, $guruId)
    {
        $request->validate([
            'jenis_diklat' => 'required|string|max:255',
            'nama' => 'required|string|max:255',
            'penyelenggara' => 'required|string|max:255',
            'tingkat' => 'nullable|string|max:255',
            'tahun' => 'required|integer|min:1900|max:' . date('Y'),
            'peran' => 'nullable|string|max:255',
        ]);

        $guru = Guru::findOrFail($guruId);

        PengembanganDiri::create([
            'guru_id' => $guru->id,
            'jenis_diklat' => $request->jenis_diklat,
            'nama' => $request->nama,
            'penyelenggara' => $request->penyelenggara,
            'tingkat' => $request->tingkat,
            'tahun' => $request->tahun,
            'peran' => $request->peran,
        ]);

        return redirect()->route('gtk.guru.show', $guru->id)
            ->with('success', 'Data pengembangan diri berhasil dit