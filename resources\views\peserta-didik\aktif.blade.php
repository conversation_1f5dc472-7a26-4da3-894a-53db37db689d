@extends('adminlte::page')

@section('title', 'Data Peserta Didik Aktif')

@section('content_header')
    <h1>Data Peserta Didik Aktif</h1>
@stop

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Data Peserta Didik Aktif</h3>
                        <div class="card-tools">
                            <div class="d-flex">
                                @if(auth()->user()->hasAnyRole(['Administrator', 'Yayasan']))
                                <select id="filter-unit" class="form-control mr-2">
                                    <option value="">Semua Unit</option>
                                    @foreach(\App\Models\Unit::orderBy('nama_unit')->get() as $unit)
                                        <option value="{{ $unit->nama_unit }}">{{ $unit->nama_unit }}</option>
                                    @endforeach
                                </select>
                                @endif
                                <!-- Tombol mutasi keluar -->
                                @can('manage-peserta-didik')
                                <button class="btn btn-warning mr-2" data-toggle="modal" data-target="#mutasiKeluarModal" style="white-space: nowrap;">
                                    <i class="fas fa-exchange-alt"></i> Mutasi Keluar
                                </button>
                                <!-- Tombol import/export -->
                                <div class="btn-group mr-2">
                                    <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-file-excel"></i> Import/Export
                                    </button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" href="#" data-toggle="modal" data-target="#importModal">
                                            <i class="fas fa-file-import"></i> Import Data
                                        </a>
                                        <a class="dropdown-item" href="{{ route('peserta-didik.export-template') }}">
                                            <i class="fas fa-download"></i> Download Template
                                        </a>
                                        <a class="dropdown-item" href="{{ route('peserta-didik.export') }}">
                                            <i class="fas fa-file-export"></i> Export Data
                                        </a>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#tambahSiswa" style="white-space: nowrap;">
                                    <i class="fas fa-plus"></i> Tambah PD Baru
                                </button>
                                @endcan
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        @if(session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                        
                        <table id="tabel-siswa" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>NIS</th>
                                    <th>NISN</th>
                                    <th>Nama</th>
                                    <th>Jenis Kelamin</th>
                                    <th>Unit</th>
                                    <th>Kelas</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($siswa as $s)
                                <tr>
                                    <td>{{ $s->nis }}</td>
                                    <td>{{ $s->nisn }}</td>
                                    <td>{{ $s->nama }}</td>
                                    <td>{{ $s->jenis_kelamin == 'L' ? 'Laki-laki' : 'Perempuan' }}</td>
                                    <td>{{ $s->unit->nama_unit ?? 'Belum ada unit' }}</td>
                                    <td>{{ $s->kelas->nama ?? 'Belum ada kelas' }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" data-toggle="modal" data-target="#detailSiswa{{ $s->id }}" data-toggle="tooltip" title="Detail">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        @can('manage-peserta-didik')
                                        <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#editSiswa{{ $s->id }}" data-toggle="tooltip" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form action="{{ route('peserta-didik.destroy', $s->id) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Yakin ingin menghapus data ini?')" data-toggle="tooltip" title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        <!--Tobol mutasi keluar 
                                        <button class="btn btn-warning" data-toggle="modal" data-target="#mutasiKeluarModal">
                                            <i class="fas fa-exchange-alt"></i> Mutasi Keluar
                                        </button> -->
                                        @endcan
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('peserta-didik.modals.detail')
    @can('manage-peserta-didik')
        @include('peserta-didik.modals.create')
        @include('peserta-didik.modals.edit')
        @include('peserta-didik.modals.import')
    @endcan
    <!-- Include modal mutasi keluar -->
    @include('rombel.modals.mutasi-keluar')
@stop

@section('css')
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
@stop

@section('js')
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script>
        $(document).ready(function() {
            var table = $('#tabel-siswa').DataTable({
                "responsive": true,
                "lengthChange": true,
                "autoWidth": false,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                }
            });

            // Filter berdasarkan unit (hanya jika elemen filter-unit ada)
            if ($('#filter-unit').length > 0) {
                $('#filter-unit').on('change', function() {
                    var unit = $(this).val();
                    table.column(4) // Kolom unit (indeks 4)
                        .search(unit)
                        .draw();
                });
            }
        });
    </script>
@stop
