import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                'vendor/adminlte/dist/css/adminlte.min.css'
            ],
            refresh: true,
        }),
    ],
    //sso
    build: {
        cssMinify: true,
        minify: 'terser',
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['bootstrap']
                }
            }
        }
    }
});

