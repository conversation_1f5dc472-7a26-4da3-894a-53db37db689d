<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Achievement;
use Illuminate\Support\Str;

class GenerateAchievementSlugsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $achievements = Achievement::whereNull('slug')->orWhere('slug', '')->get();
        
        foreach ($achievements as $achievement) {
            $baseSlug = Str::slug($achievement->title);
            $slug = $baseSlug;
            $counter = 1;

            // Pastikan slug unik
            while (Achievement::where('slug', $slug)->where('id', '!=', $achievement->id)->exists()) {
                $slug = $baseSlug . '-' . $counter;
                $counter++;
            }

            $achievement->update(['slug' => $slug]);
            
            $this->command->info("Generated slug for '{$achievement->title}': {$slug}");
        }
        
        $this->command->info('All achievement slugs have been generated successfully!');
    }
}
