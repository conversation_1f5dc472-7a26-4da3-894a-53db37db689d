<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PenilaianProyek extends Model
{
    use HasFactory;

    protected $fillable = [
        'nama_proyek',
        'deskripsi_proyek',
        'kelas_id',
        'tanggal_mulai',
        'tanggal_selesai',
        'dimensi_penilaian',
        'created_by',
    ];

    protected $casts = [
        'tanggal_mulai' => 'date',
        'tanggal_selesai' => 'date',
    ];

    public function kelas()
    {
        return $this->belongsTo(Kelas::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function penilaianSiswa()
    {
        return $this->hasMany(PenilaianProyekSiswa::class);
    }

    public function raporNilaiProyek()
    {
        return $this->hasMany(RaporNilaiProyek::class);
    }
}