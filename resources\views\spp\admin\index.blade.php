@extends('adminlte::page')

@section('title', 'Manajemen SPP')

@section('content_header')
    <h1>Manajemen Data SPP</h1>
@stop

@section('content')
<div class="container">
    @if(session('success'))
        <div class="alert alert-success alert-dismissible">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h5><i class="icon fas fa-check"></i> Sukses!</h5>
            {{ session('success') }}
        </div>
    @endif

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Upload Data SPP</h3>
        </div>
        <div class="card-body">
            <form action="{{ route('spp.upload') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="form-group">
                    <label for="file">File Data SPP (CSV, Excel)</label>
                    <div class="input-group">
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="file" name="file" required>
                            <label class="custom-file-label" for="file">Pilih file</label>
                        </div>
                    </div>
                    <small class="text-muted">Format: VA, Nama, Tanggal Lahir (DD/MM/YYYY), SPP Bulan Ini, Tunggakan, Buku, Uang Program, Les</small>
                    @error('file')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
                <div class="form-group">
                    <label for="keterangan">Keterangan</label>
                    <input type="text" class="form-control" id="keterangan" name="keterangan" placeholder="Keterangan upload (opsional)">
                </div>
                <button type="submit" class="btn btn-primary">Upload</button>
            </form>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h3 class="card-title">Riwayat Upload</h3>
        </div>
        <div class="card-body">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Nama File</th>
                        <th>Keterangan</th>
                        <th>Diupload Oleh</th>
                        <th>Tanggal Upload</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($uploads as $index => $upload)
                        <tr>
                            <td>{{ $index + 1 }}</td>
                            <td>{{ $upload->original_name }}</td>
                            <td>{{ $upload->keterangan ?? '-' }}</td>
                            <td>{{ $upload->uploader->name }}</td>
                            <td>{{ $upload->created_at->format('d/m/Y H:i') }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
@stop

@section('js')
<script>
$(document).ready(function() {
    $('.table').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.19/i18n/Indonesian.json"
        }
    });
    
    // Show filename in custom file input
    $(".custom-file-input").on("change", function() {
        var fileName = $(this).val().split("\\").pop();
        $(this).siblings(".custom-file-label").addClass("selected").html(fileName);
    });
});
</script>
@stop