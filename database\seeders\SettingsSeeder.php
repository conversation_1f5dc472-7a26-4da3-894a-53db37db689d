<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    public function run()
    {
        $settings = [
            [
                'key' => 'sambutan_kepsek',
                'value' => 'Selamat datang di website sekolah kami. Kami berkomitmen untuk memberikan pendidikan terbaik...',
                'type' => 'textarea'
            ],
            [
                'key' => 'profil_sekolah',
                'value' => 'Profil lengkap sekolah kami...',
                'type' => 'richtext'
            ],
            [
                'key' => 'visi_misi',
                'value' => 'Visi dan Misi sekolah kami...',
                'type' => 'richtext'
            ],
            [
                'key' => 'sejarah',
                'value' => 'Sejarah perkembangan sekolah kami...',
                'type' => 'richtext'
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
