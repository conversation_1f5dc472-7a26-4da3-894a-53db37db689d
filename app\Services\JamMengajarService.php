<?php

namespace App\Services;

use App\Models\DetailJadwal;
use App\Models\JurnalKegiatan;
use Carbon\Carbon;

class JamMengajarService
{
    /**
     * Hitung jam mengajar berdasarkan jadwal
     */
    public function hitungJamMengajar($userId, $tanggal, $status = 'hadir')
    {
        try {
            $carbon = Carbon::parse($tanggal);
            $namaHari = $this->getNamaHari($carbon->dayOfWeek);
            $tahunAjaran = $this->getTahunAjaranAktif();
            
            // Log untuk debugging
            \Log::info('Menghitung jam mengajar', [
                'userId' => $userId,
                'tanggal' => $tanggal,
                'namaHari' => $namaHari,
                'dayOfWeek' => $carbon->dayOfWeek,
                'tahunAjaran' => $tahunAjaran
            ]);
            
            // Jika hari Sabtu atau Minggu, return 0
            if ($namaHari == 'Sabtu' || $namaHari == 'Minggu') {
                \Log::info('<PERSON> libur (Sabtu/Minggu)');
                return [
                    'jam_mengajar' => 0,
                    'detail' => []
                ];
            }
            
            // Ambil jadwal mengajar guru pada hari tersebut
            $query = DetailJadwal::with(['jadwalPelajaran', 'jadwalPelajaran.kelas', 'mataPelajaran'])
                ->whereHas('mataPelajaran', function($q) use ($userId) {
                    $q->where('pengajar_id', $userId);
                })
                ->whereHas('jadwalPelajaran', function($q) use ($tahunAjaran) {
                    $q->where('tahun_ajaran', $tahunAjaran);
                })
                ->where('hari', $namaHari);
            
            // Log query SQL untuk debugging
            \Log::info('SQL Query: ' . $query->toSql());
            \Log::info('SQL Bindings: ', $query->getBindings());
            
            $jadwalHarian = $query->orderBy('waktu_mulai')->get();
            
            // Log hasil query
            \Log::info('Hasil query jadwal harian', [
                'count' => $jadwalHarian->count(),
                'data' => $jadwalHarian->toArray()
            ]);
            
            // Jika tidak ada jadwal, return 0
            if ($jadwalHarian->isEmpty()) {
                \Log::info('Tidak ada jadwal untuk hari ' . $namaHari);
                return [
                    'jam_mengajar' => 0,
                    'detail' => []
                ];
            }
            
            // Jika guru tidak hadir, return 0
            if ($status == 'tidak_hadir') {
                return [
                    'jam_mengajar' => 0,
                    'detail' => []
                ];
            }
            
            $totalJam = 0;
            $detailJam = [];
            
            foreach ($jadwalHarian as $jadwal) {
                $mulai = Carbon::parse($jadwal->waktu_mulai);
                $selesai = Carbon::parse($jadwal->waktu_selesai);
                $durasi = $selesai->diffInMinutes($mulai) / 60; // Konversi ke jam
                
                $totalJam += $durasi;
                $detailJam[] = [
                    'kelas' => $jadwal->jadwalPelajaran->kelas->nama ?? 'Kelas tidak diketahui',
                    'mapel' => $jadwal->mataPelajaran->nama_mapel ?? 'Mapel tidak diketahui',
                    'waktu' => substr($jadwal->waktu_mulai, 0, 5) . '-' . substr($jadwal->waktu_selesai, 0, 5),
                    'durasi' => $durasi
                ];
                
                \Log::info('Menambahkan jadwal', [
                    'kelas' => $jadwal->jadwalPelajaran->kelas->nama ?? 'Kelas tidak diketahui',
                    'mapel' => $jadwal->mataPelajaran->nama_mapel ?? 'Mapel tidak diketahui',
                    'waktu' => $jadwal->waktu_mulai . '-' . $jadwal->waktu_selesai,
                    'durasi' => $durasi
                ]);
            }
            
            \Log::info('Total jam mengajar: ' . $totalJam . ', detail: ' . count($detailJam));
            
            return [
                'jam_mengajar' => round($totalJam, 2),
                'detail' => $detailJam
            ];
        } catch (\Exception $e) {
            \Log::error('Error dalam hitungJamMengajar: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
    
    /**
     * Hitung jam mengajar tambahan
     */
    public function hitungJamTambahan($data)
    {
        // $data berisi array detail jam tambahan
        // Format: [['kelas' => '...', 'mapel' => '...', 'waktu' => '...', 'durasi' => '...']]
        
        $totalJam = 0;
        foreach ($data as $item) {
            $totalJam += $item['durasi'];
        }
        
        return [
            'jam_tambahan' => round($totalJam, 2),
            'detail' => $data
        ];
    }
    
    /**
     * Mendapatkan nama hari dari angka hari
     */
    protected function getNamaHari($dayOfWeek)
    {
        $hari = [
            0 => 'Minggu',
            1 => 'Senin',
            2 => 'Selasa',
            3 => 'Rabu',
            4 => 'Kamis',
            5 => 'Jumat',
            6 => 'Sabtu',
        ];
        
        return $hari[$dayOfWeek] ?? 'Tidak diketahui';
    }
    
    /**
     * Mendapatkan tahun ajaran aktif
     */
    protected function getTahunAjaranAktif()
    {
        $tahunAjaran = \App\Models\TahunAjaran::where('aktif', true)->first();
        
        if (!$tahunAjaran) {
            \Log::warning('Tidak ada tahun ajaran aktif, menggunakan fallback');
            return $this->getTahunAjaranFallback();
        }
        
        return $tahunAjaran->nama;
    }

    /**
     * Fallback untuk tahun ajaran jika tidak ada yang aktif
     */
    protected function getTahunAjaranFallback()
    {
        $tahunIni = date('Y');
        $tahunDepan = $tahunIni + 1;
        return $tahunIni . '/' . $tahunDepan;
    }
}


