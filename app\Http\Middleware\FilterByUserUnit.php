<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class FilterByUserUnit
{
    public function handle(Request $request, Closure $next)
    {
        // Tambahkan informasi filter unit ke request
        $user = auth()->user();
        
        // Tentukan apakah user dapat melihat semua unit
        $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawa<PERSON>']);
        
        // Tambahkan ke request agar bisa diakses di controller
        $request->merge([
            'filter_by_unit' => !$canViewAllUnits,
            'user_unit_id' => $user->unit_id,
            'can_view_all_units' => $canViewAllUnits
        ]);
        
        return $next($request);
    }
}
