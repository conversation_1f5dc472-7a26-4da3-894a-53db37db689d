<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

class JadwalPerKelasSheet implements FromCollection, WithHeadings, WithMapping, WithTitle, ShouldAutoSize, WithStyles
{
    protected $jadwal;
    protected $currentHari = null;
    protected $rowCount = 0;
    protected $hariRowSpans = [];

    public function __construct($jadwal)
    {
        $this->jadwal = $jadwal;
    }

    public function collection()
    {
        $detailJadwal = $this->jadwal->detailJadwal
            ->sortBy('hari')
            ->sortBy('waktu_mulai')
            ->groupBy('hari');

        // Hitung rowspan untuk setiap hari
        foreach ($detailJadwal as $hari => $details) {
            $this->hariRowSpans[$hari] = $details->count();
        }

        // Flatten collection kembali
        return $detailJadwal->flatten();
    }

    public function headings(): array
    {
        return [
            ['JADWAL PELAJARAN'],
            ['Kelas: ' . $this->jadwal->nama_kelas_text],
            ['Wali Kelas: ' . $this->jadwal->wali_kelas],
            [''],
            ['Hari', 'Waktu', 'Mata Pelajaran', 'Guru']
        ];
    }

    public function map($detail): array
    {
        $this->rowCount++;
        
        // Jika hari berubah, update currentHari
        if ($this->currentHari !== $detail->hari) {
            $this->currentHari = $detail->hari;
            $hari = $detail->hari;
        } else {
            $hari = '';
        }

        // Handle special events (istirahat, upacara, etc)
        if ($detail->is_istirahat || $detail->keterangan) {
            $mataPelajaran = $detail->keterangan ?? 'Istirahat';
            $guru = '-';
        } else {
            $mataPelajaran = $detail->mataPelajaran->nama_mapel ?? '-';
            $guru = $detail->mataPelajaran->pengajar->name ?? '-';
        }

        return [
            $hari,
            substr($detail->waktu_mulai, 0, 5) . ' - ' . substr($detail->waktu_selesai, 0, 5),
            $mataPelajaran,
            $guru
        ];
    }

    public function title(): string
    {
        return str_replace(['/', '\\', '*', '[', ']', ':', '?'], '-', $this->jadwal->nama_kelas_text);
    }

    public function styles(Worksheet $sheet)
    {
        // Merge cells untuk header
        $sheet->mergeCells('A1:D1');
        $sheet->mergeCells('A2:D2');
        $sheet->mergeCells('A3:D3');
        $sheet->mergeCells('A4:D4');

        // Styling untuk header
        $sheet->getStyle('A1:D1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A2:A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
        
        // Merge cells untuk hari yang sama
        $currentRow = 6; // Mulai dari baris data (setelah header)
        foreach ($this->hariRowSpans as $hari => $rowspan) {
            if ($rowspan > 1) {
                $sheet->mergeCells("A{$currentRow}:A" . ($currentRow + $rowspan - 1));
            }
            $currentRow += $rowspan;
        }

        // Border untuk seluruh tabel
        $tableRange = 'A5:D' . ($currentRow - 1);
        $sheet->getStyle($tableRange)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        // Vertical center alignment untuk kolom hari
        $sheet->getStyle('A6:A' . ($currentRow - 1))->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

        return [
            1 => ['font' => ['bold' => true, 'size' => 14]], // Judul
            2 => ['font' => ['bold' => true]], // Kelas
            3 => ['font' => ['bold' => true]], // Wali Kelas
            5 => ['font' => ['bold' => true]], // Header tabel
            'A' => ['alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]],
            'B' => ['alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]],
            'C' => ['alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]],
            'D' => ['alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]],
        ];
    }
}
