@extends('layouts.admin')

@section('title')
    @if(auth()->user()->role === 'guru')
        Jadwal Mengajar {{ auth()->user()->name }}
    @else
        Jadwal Pelajaran
    @endif
@endsection
 
@section('content')
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="card-title">
                @if(auth()->user()->role === 'guru')
                    Jadwal Mengajar {{ auth()->user()->name }}
                @else
                    Daftar Jadwal Pelajaran
                @endif
            </h3>
            <div class="card-tools">
                <div class="btn-group">
                    <button type="button" class="btn btn-secondary btn-sm dropdown-toggle" data-toggle="dropdown">
                        <i class="fas fa-filter"></i> Tahun Ajaran: {{ $tahunAjaranTerpilih }}
                    </button>
                    <div class="dropdown-menu">
                        @foreach($tahunAjaranList as $ta)
                            <a class="dropdown-item {{ $ta == $tahunAjaranTerpilih ? 'active' : '' }}" 
                               href="{{ route('jadwal.index', ['tahun_ajaran' => $ta]) }}">
                                {{ $ta }}
                            </a>
                        @endforeach
                    </div>
                </div>
                @can('manage-jadwal')
                <div class="btn-group">
                    <button type="button" class="btn btn-success btn-sm dropdown-toggle" data-toggle="dropdown">
                        <i class="fas fa-file-excel"></i> Export Excel
                    </button>
                    <div class="dropdown-menu">
                        <a href="{{ route('jadwal.export', ['tahunAjaran' => str_replace('/', '-', $tahunAjaranTerpilih), 'type' => 'kelas']) }}" 
                           class="dropdown-item">
                            <i class="fas fa-users"></i> Per Kelas
                        </a>
                        <a href="{{ route('jadwal.export', ['tahunAjaran' => str_replace('/', '-', $tahunAjaranTerpilih), 'type' => 'guru']) }}" 
                           class="dropdown-item">
                            <i class="fas fa-chalkboard-teacher"></i> Per Guru
                        </a>
                        <a href="{{ route('jadwal.export', ['tahunAjaran' => str_replace('/', '-', $tahunAjaranTerpilih), 'type' => 'all']) }}" 
                           class="dropdown-item">
                            <i class="fas fa-table"></i> Lengkap (Kelas & Guru)
                        </a>
                    </div>
                </div>
                <a href="{{ route('jadwal.create') }}" class="btn btn-primary btn-sm ml-2">
                    <i class="fas fa-plus"></i> Buat Jadwal Baru
                </a>
                @endcan
            </div>
        </div>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if($jadwalList->isEmpty())
            <div class="alert alert-info">
                @if(auth()->user()->role === 'guru')
                    Tidak ada jadwal mengajar untuk tahun ajaran ini.
                @else
                    Tidak ada jadwal pelajaran untuk tahun ajaran ini.
                @endif
            </div>
        @endif

        @foreach($jadwalList as $jadwal)
            <div class="mb-4">
                <div class="d-flex justify-content-between align-items-center">
                    <h4>{{ $jadwal->nama_kelas_text }}</h4>
                    @can('manage-jadwal')
                    <div>
                        <a href="{{ route('jadwal.edit', $jadwal->id) }}" class="btn btn-info btn-sm">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <form action="{{ route('jadwal.destroy', $jadwal->id) }}" method="POST" class="d-inline" 
                              onsubmit="return confirm('Apakah Anda yakin ingin menghapus jadwal ini?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm">
                                <i class="fas fa-trash"></i> Hapus
                            </button>
                        </form>
                    </div>
                    @endcan
                </div>
                @if(auth()->user()->role !== 'guru')
                    <p>Wali Kelas: {{ $jadwal->wali_kelas }}</p>
                @endif
                
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Hari</th>
                            <th>Jam</th>
                            <th>Mata Pelajaran</th>
                            @if(auth()->user()->role !== 'guru')
                                <th>Guru</th>
                            @endif
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($jadwal->detailJadwal->sortBy(function($detail) {
                            $hariUrutan = [
                                'Senin' => 1,
                                'Selasa' => 2,
                                'Rabu' => 3,
                                'Kamis' => 4,
                                'Jumat' => 5
                            ];
                            return $hariUrutan[$detail->hari];
                        })->groupBy('hari') as $hari => $details)
                            @foreach($details->sortBy('waktu_mulai') as $detail)
                                <tr>
                                    @if($loop->first)
                                        <td rowspan="{{ $details->count() }}">{{ $hari }}</td>
                                    @endif
                                    <td>{{ substr($detail->waktu_mulai, 0, 5) }}-{{ substr($detail->waktu_selesai, 0, 5) }}</td>
                                    <td>
                                        @if($detail->is_istirahat || $detail->keterangan)
                                            {{ $detail->keterangan }}
                                        @else
                                            {{ $detail->mataPelajaran->nama_mapel }}
                                        @endif
                                    </td>
                                    @if(auth()->user()->role !== 'guru')
                                        <td>
                                            @if(!$detail->is_istirahat && !$detail->keterangan)
                                                {{ $detail->mataPelajaran->pengajar->name }}
                                            @endif
                                        </td>
                                    @endif
                                </tr>
                            @endforeach
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endforeach
    </div>
</div>
@endsection

@section('css')
<style>
    .card-tools {
        float: right;
        display: flex;
        align-items: center;
    }
    .input-group {
        margin-right: 10px;
    }
    .table th {
        background-color: #f8f9fa;
        vertical-align: middle;
    }
    .table td {
        vertical-align: middle;
    }
</style>
@endsection

@section('js')
<script>
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Flash message fade out
    setTimeout(function() {
        $('.alert-success').fadeOut('slow');
    }, 3000);
});
</script>
@endsection
