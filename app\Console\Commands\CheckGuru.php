<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class CheckGuru extends Command
{
    protected $signature = 'check:guru {email}';
    protected $description = 'Check and fix guru permissions';

    public function handle()
    {
        $email = $this->argument('email');
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User tidak ditemukan!");
            return;
        }

        $this->info("Checking user: " . $user->name);

        // 1. Cek dan berikan role Guru
        if (!$user->hasRole('Guru')) {
            $user->assignRole('Guru');
            $this->info("Role 'Guru' diberikan");
        } else {
            $this->info("User sudah memiliki role 'Guru'");
        }

        // 2. Cek permission
        if ($user->can('manage-adm-guru')) {
            $this->info("User memiliki permission 'manage-adm-guru'");
        } else {
            $this->error("User TIDAK memiliki permission 'manage-adm-guru'");
        }

        // 3. <PERSON><PERSON><PERSON><PERSON> semua permission
        $this->info("\nSemua permission user:");
        foreach($user->getAllPermissions() as $permission) {
            $this->info("- " . $permission->name);
        }

        // 4. Cache clear
        $this->call('cache:clear');
        $this->info("\nCache cleared");
    }
}

