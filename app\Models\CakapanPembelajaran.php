<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CakapanPembelajaran extends Model
{
    use HasFactory;

    protected $fillable = [
        'kompetensi_dasar_id',
        'deskripsi',
    ];

    public function kompetensiDasar()
    {
        return $this->belongsTo(KompetensiDasar::class, 'kompetensi_dasar_id');
    }

    public function tujuanPembelajarans()
    {
        return $this->hasMany(TujuanPembelajaran::class);
    }
}

