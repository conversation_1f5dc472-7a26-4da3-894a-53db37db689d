<?php

namespace App\Imports;

use App\Models\Spp;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class SppImport implements ToCollection, WithHeadingRow
{
    /**
     * @param Collection $rows
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {
            // Cek apakah VA number ada
            if (!isset($row['va_number']) || empty($row['va_number'])) {
                continue;
            }
            
            // Transformasi tanggal lahir
            $tanggalLahir = $this->transformDate($row['tanggal_lahir'] ?? null);
            
            // Cari data berdasarkan VA number
            $spp = Spp::where('va_number', $row['va_number'])->first();
            
            // Data untuk update atau create
            $data = [
                'nama' => $row['nama'] ?? '',
                'tanggal_lahir' => $tanggalLahir,
                'spp_bulan_ini' => $row['spp_bulan_ini'] ?? 0,
                'tunggakan' => $row['tunggakan'] ?? 0,
                'buku' => $row['buku'] ?? 0,
                'uang_program' => $row['uang_program'] ?? 0,
                'les' => $row['les'] ?? 0,
            ];
            
            // Jika data sudah ada, update
            if ($spp) {
                $spp->update($data);
            } else {
                // Jika belum ada, buat baru
                Spp::create(array_merge(['va_number' => $row['va_number']], $data));
            }
        }
    }
    
    /**
     * Transform date value from Excel
     */
    private function transformDate($value)
    {
        if (!$value) {
            return null;
        }
        
        try {
            // Coba parse sebagai Excel date
            if (is_numeric($value)) {
                return \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($value);
            } else {
                // Jika string, coba parse sebagai format DD/MM/YYYY
                return Carbon::createFromFormat('d/m/Y', $value);
            }
        } catch (\Exception $e) {
            // Jika gagal, coba format lain atau kembalikan null
            try {
                return Carbon::parse($value);
            } catch (\Exception $e2) {
                return null;
            }
        }
    }
}




