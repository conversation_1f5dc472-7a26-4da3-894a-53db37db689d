<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PengaturanController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// API untuk mendapatkan jenjang berdasarkan unit
Route::get('/unit/{unit}/jenjangs', [PengaturanController::class, 'getJenjangsByUnit']);

// API untuk mendapatkan gedung berdasarkan unit
Route::get('/unit/{unit}/gedungs', [PengaturanController::class, 'getGedungsByUnit']);

// Endpoint untuk mengambil siswa berdasarkan kelas
Route::get('/kelas/{id}/siswa', [App\Http\Controllers\RombelController::class, 'getSiswaByKelas']);

// Pastikan route ini terdaftar
Route::get('/kelas/{kelasId}/siswa', 'App\Http\Controllers\ApiController@getSiswaByKelas');







