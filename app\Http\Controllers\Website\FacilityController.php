<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Facility;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FacilityController extends Controller
{
    public function index()
    {
        $facilities = Facility::all();
        return view('admin.website.facility.index', compact('facilities'));
    }

    public function create()
    {
        return view('admin.website.facility.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'required|image|mimes:jpeg,png,jpg|max:2048'
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('storage/facilities'), $filename);
            $data['image'] = $filename;
        }

        Facility::create($data);

        return redirect()->route('admin.website.facility.index')
            ->with('success', 'Fasilitas berhasil ditambahkan');
    }

    public function edit(Facility $facility)
    {
        return view('admin.website.facility.edit', compact('facility'));
    }

    public function update(Request $request, Facility $facility)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048'
        ]);

        $data = $request->all();

        if ($request->hasFile('image')) {
            // Delete old image
            if ($facility->image && file_exists(public_path('storage/facilities/' . $facility->image))) {
                unlink(public_path('storage/facilities/' . $facility->image));
            }

            $image = $request->file('image');
            $filename = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('storage/facilities'), $filename);
            $data['image'] = $filename;
        }

        $facility->update($data);

        return redirect()->route('admin.website.facility.index')
            ->with('success', 'Fasilitas berhasil diperbarui');
    }

    public function destroy(Facility $facility)
    {
        if ($facility->image && file_exists(public_path('storage/facilities/' . $facility->image))) {
            unlink(public_path('storage/facilities/' . $facility->image));
        }

        $facility->delete();

        return redirect()->route('admin.website.facility.index')
            ->with('success', 'Fasilitas berhasil dihapus');
    }

    public function show()
    {
        $facilities = Facility::all();
        return view('website.facility.index', compact('facilities'));
    }
}