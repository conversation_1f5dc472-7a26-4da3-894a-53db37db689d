@extends('layouts.admin')

@section('title', 'Data Mata Pelajaran')

@section('css')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css" rel="stylesheet" />
<style>
    .select2-search__field::placeholder {
        content: "Ketik pengajar disini...";
    }
</style>
@stop

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar Mata Pelajaran</h3>
        <div class="card-tools">
            <form action="{{ route('pengaturan.mapel') }}" method="GET" class="form-inline mr-2 d-inline-block">
                <div class="input-group input-group-sm">
                    <input type="text" name="search" class="form-control" placeholder="Cari mata pelajaran..." value="{{ request('search') }}">
                    <div class="input-group-append">
                        <button type="submit" class="btn btn-default">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
            <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#tambahMapel">
                <i class="fas fa-plus"></i> Tambah Mata Pelajaran
            </button>
        </div>
    </div>
    <div class="card-body">
        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        <table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>No</th>
                    <th>Unit</th>
                    <th>Mata Pelajaran</th>
                    <th>Pengajar</th>
                    <th>Aksi</th>
                </tr>
            </thead>
            <tbody>
                @foreach($mapels as $mapel)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $mapel->unit ? $mapel->unit->nama_unit : 'Tidak ada unit' }}</td>
                    <td>{{ $mapel->nama_mapel }}</td>
                    <td>{{ $mapel->pengajar->name }}</td>
                    <td>
                        <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#editMapel{{ $mapel->id }}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <form action="{{ route('mapel.destroy', $mapel->id) }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Yakin hapus data?')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<!-- Modal Tambah -->
<div class="modal fade" id="tambahMapel" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Mata Pelajaran</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('mapel.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label>Unit</label>
                        <select name="unit_id" class="form-control @error('unit_id') is-invalid @enderror" required>
                            <option value="">Pilih Unit</option>
                            @foreach($units as $unit)
                                <option value="{{ $unit->id }}">
                                    {{ $unit->nama_unit }}
                                </option>
                            @endforeach
                        </select>
                        @error('unit_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label>Nama Mata Pelajaran</label>
                        <input type="text" name="nama_mapel" class="form-control @error('nama_mapel') is-invalid @enderror" required>
                        @error('nama_mapel')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label>Pengajar</label>
                        <select name="pengajar_id" class="form-control select2-pengajar @error('pengajar_id') is-invalid @enderror" required>
                            <option value="">Pilih Pengajar</option>
                            @foreach($pengajars as $pengajar)
                                <option value="{{ $pengajar->id }}">{{ $pengajar->name }}</option>
                            @endforeach
                        </select>
                        @error('pengajar_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit -->
@foreach($mapels as $mapel)
<div class="modal fade" id="editMapel{{ $mapel->id }}" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Mata Pelajaran</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('mapel.update', $mapel->id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="form-group">
                        <label>Unit</label>
                        <select name="unit_id" class="form-control @error('unit_id') is-invalid @enderror" required>
                            <option value="">Pilih Unit</option>
                            @foreach($units as $unit)
                                <option value="{{ $unit->id }}" {{ ($mapel->unit_id == $unit->id) ? 'selected' : '' }}>
                                    {{ $unit->nama_unit }}
                                </option>
                            @endforeach
                        </select>
                        @error('unit_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label>Nama Mata Pelajaran</label>
                        <input type="text" name="nama_mapel" class="form-control @error('nama_mapel') is-invalid @enderror" 
                               value="{{ $mapel->nama_mapel }}" required>
                        @error('nama_mapel')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label>Pengajar</label>
                        <select name="pengajar_id" class="form-control select2-pengajar @error('pengajar_id') is-invalid @enderror" required>
                            <option value="">Pilih Pengajar</option>
                            @foreach($pengajars as $pengajar)
                                <option value="{{ $pengajar->id }}" {{ $mapel->pengajar_id == $pengajar->id ? 'selected' : '' }}>
                                    {{ $pengajar->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('pengajar_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endforeach
@endsection

@section('js')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        // Inisialisasi Select2 untuk dropdown pengajar
        $('.select2-pengajar').select2({
            theme: 'bootstrap4',
            placeholder: "Pilih Pengajar",
            allowClear: true,
            width: '100%',
            language: {
                searching: function() {
                    return "Ketik pengajar disini...";
                }
            }
        });
        
        // Juga terapkan ke modal edit
        $('.modal').on('shown.bs.modal', function () {
            $(this).find('.select2-pengajar').select2({
                theme: 'bootstrap4',
                dropdownParent: $(this),
                placeholder: "Pilih Pengajar",
                allowClear: true,
                width: '100%',
                language: {
                    searching: function() {
                        return "Ketik pengajar disini...";
                    }
                }
            });
        });
    });
</script>
@stop






