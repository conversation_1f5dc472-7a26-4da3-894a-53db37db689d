<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Kelas extends Model
{
    use HasFactory;
    
    protected $table = 'kelas';
    
    protected $fillable = [
        'nama',
        'tingkat',
        'gedung_id',
        'unit_id',
        'tahun_ajaran_id',
        'wali_kelas',
        'jenjang_id',
        'tahun_ajaran',
    ];
    
    public function siswa()
    {
        return $this->hasMany(Siswa::class, 'kelas_id');
    }
    
    public function tahunAjaran()
    {
        return $this->belongsTo(TahunAjaran::class);
    }
    
    public function jenjang()
    {
        return $this->belongsTo(Jenjang::class);
    }

    public function gedung()
    {
        return $this->belongsTo(Gedung::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * Scope untuk mendapatkan kelas pada tahun ajaran aktif
     */
    public function scopeTahunAjaranAktif($query)
    {
        $tahunAjaranAktif = TahunAjaran::where('aktif', true)->first();
        
        if ($tahunAjaranAktif) {
            return $query->where('tahun_ajaran', $tahunAjaranAktif->nama);
        }
        
        return $query;
    }

    /**
     * Scope untuk mendapatkan kelas berdasarkan tahun ajaran tertentu
     */
    public function scopeTahunAjaran($query, $tahunAjaran)
    {
        return $query->where('tahun_ajaran', $tahunAjaran);
    }
}





