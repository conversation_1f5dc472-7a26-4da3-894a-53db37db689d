<?php

namespace App\Http\Controllers;

use App\Models\TujuanPembelajaran;
use App\Models\CakapanPembelajaran;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TujuanPembelajaranController extends Controller
{
    public function index()
    {
        $tujuanPembelajarans = TujuanPembelajaran::with('cakapanPembelajaran')->get();
        return view('nilai.tujuan-pembelajaran.index', compact('tujuanPembelajarans'));
    }

    public function create()
    {
        $cakapanPembelajarans = CakapanPembelajaran::all();
        return view('nilai.tujuan-pembelajaran.create', compact('cakapanPembelajarans'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'cakapan_pembelajaran_id' => 'required|exists:cakapan_pembelajarans,id',
            'deskripsi' => 'required|string',
        ]);

        try {
            DB::beginTransaction();
            
            TujuanPembelajaran::create($validated);
            
            DB::commit();
            
            return redirect()->route('penilaian.tujuan-pembelajaran.index')
                ->with('success', 'Tujuan pembelajaran berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->route('penilaian.tujuan-pembelajaran.index')
                ->with('error', 'Terjadi kesalahan saat menambahkan tujuan pembelajaran: ' . $e->getMessage());
        }
    }

    public function show(TujuanPembelajaran $tujuanPembelajaran)
    {
        return view('nilai.tujuan-pembelajaran.show', compact('tujuanPembelajaran'));
    }

    public function edit(TujuanPembelajaran $tujuanPembelajaran)
    {
        $cakapanPembelajarans = CakapanPembelajaran::all();
        return view('nilai.tujuan-pembelajaran.edit', compact('tujuanPembelajaran', 'cakapanPembelajarans'));
    }

    public function update(Request $request, TujuanPembelajaran $tujuanPembelajaran)
    {
        $validated = $request->validate([
            'cakapan_pembelajaran_id' => 'required|exists:cakapan_pembelajarans,id',
            'deskripsi' => 'required|string',
        ]);

        try {
            DB::beginTransaction();
            
            $tujuanPembelajaran->update($validated);
            
            DB::commit();
            
            return redirect()->route('penilaian.tujuan-pembelajaran.index')
                ->with('success', 'Tujuan pembelajaran berhasil diperbarui');
        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->route('penilaian.tujuan-pembelajaran.index')
                ->with('error', 'Terjadi kesalahan saat memperbarui tujuan pembelajaran: ' . $e->getMessage());
        }
    }

    public function destroy(TujuanPembelajaran $tujuanPembelajaran)
    {
        try {
            DB::beginTransaction();
            
            $tujuanPembelajaran->delete();
            
            DB::commit();
            
            return redirect()->route('penilaian.tujuan-pembelajaran.index')
                ->with('success', 'Tujuan pembelajaran berhasil dihapus');
        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->route('penilaian.tujuan-pembelajaran.index')
                ->with('error', 'Terjadi kesalahan saat menghapus tujuan pembelajaran: ' . $e->getMessage());
        }
    }
}
