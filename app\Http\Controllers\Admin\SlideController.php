<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Slide;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SlideController extends Controller
{
    public function index()
    {
        $slides = Slide::orderBy('order')->get();
        return view('admin.slides.index', compact('slides'));
    }

    public function create()
    {
        return view('admin.slides.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            'order' => 'required|integer|min:1'
        ]);

        $imagePath = $request->file('image')->store('slides', 'public');

        Slide::create([
            'title' => $request->title,
            'description' => $request->description,
            'image' => $imagePath,
            'order' => $request->order,
            'status' => $request->has('status')
        ]);

        return redirect()->route('website.slide')
            ->with('success', 'Slide berhasil ditambahkan');
    }

    public function edit(Slide $slide)
    {
        return view('admin.slides.edit', compact('slide'));
    }

    public function update(Request $request, Slide $slide)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'order' => 'required|integer|min:1'
        ]);

        $data = [
            'title' => $request->title,
            'description' => $request->description,
            'order' => $request->order,
            'status' => $request->has('status')
        ];

        if ($request->hasFile('image')) {
            // Hapus gambar lama
            if ($slide->image) {
                Storage::disk('public')->delete($slide->image);
            }
            // Upload gambar baru
            $data['image'] = $request->file('image')->store('slides', 'public');
        }

        $slide->update($data);

        return redirect()->route('website.slide')
            ->with('success', 'Slide berhasil diperbarui');
    }

    public function destroy(Slide $slide)
    {
        if ($slide->image) {
            Storage::disk('public')->delete($slide->image);
        }
        
        $slide->delete();

        return redirect()->route('website.slide')
            ->with('success', 'Slide berhasil dihapus');
    }
}
