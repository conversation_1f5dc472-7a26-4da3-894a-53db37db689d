<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notifikasi extends Model
{
    use HasFactory;
    
    protected $table = 'notifikasi';
    
    protected $fillable = [
        'user_id',
        'judul',
        'pesan',
        'jenis',
        'link',
        'dibaca',
    ];
    
    protected $casts = [
        'dibaca' => 'boolean',
    ];
    
    /**
     * Get the user that owns the notification.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}

