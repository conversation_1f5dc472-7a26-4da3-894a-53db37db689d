<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\Unit;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class ArtikelController extends Controller
{
    public function index()
    {
        $articles = Article::with('author')->latest()->paginate(10);
        return view('admin.website.artikel.index', compact('articles'));
    }

    public function create()
    {
        // Tambahkan units untuk role Administrator
        $units = [];
        if (auth()->user()->hasRole('Administrator')) {
            $units = Unit::all();
        }
        return view('admin.website.artikel.create', compact('units'));
    }

    public function store(Request $request)
    {
        $rules = [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string',
            'image' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            'status' => 'required|in:draft,published,archived',
            'published_at' => 'nullable|date'
        ];

        // Tambahkan validasi unit_id untuk Administrator
        if (auth()->user()->hasRole('Administrator')) {
            $rules['unit_id'] = 'required|exists:units,id';
        }

        $validated = $request->validate($rules);

        try {
            if ($request->hasFile('image')) {
                $path = $request->file('image')->store('articles', 'public');
                
                $articleData = [
                    'title' => $validated['title'],
                    'slug' => Str::slug($validated['title']),
                    'excerpt' => $validated['excerpt'] ?? Str::limit(strip_tags($validated['content']), 200),
                    'content' => $validated['content'],
                    'image' => $path,
                    'status' => $validated['status'],
                    'author_id' => auth()->id(),
                    'published_at' => $validated['published_at'] ?? ($validated['status'] === 'published' ? now() : null)
                ];

                // Set unit_id berdasarkan role
                if (auth()->user()->hasRole('Administrator')) {
                    $articleData['unit_id'] = $validated['unit_id'];
                } else {
                    $articleData['unit_id'] = auth()->user()->unit_id;
                }

                Article::create($articleData);

                return redirect()->route('admin.website.artikel.index')
                    ->with('success', 'Artikel berhasil ditambahkan');
            }
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function edit(Article $artikel)
    {
        return view('admin.website.artikel.edit', compact('artikel'));
    }

    public function update(Request $request, Article $artikel)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'status' => 'required|in:draft,published,archived',
            'published_at' => 'nullable|date'
        ]);

        try {
            $data = [
                'title' => $validated['title'],
                'slug' => Str::slug($validated['title']),
                'excerpt' => $validated['excerpt'] ?? Str::limit(strip_tags($validated['content']), 200),
                'content' => $validated['content'],
                'status' => $validated['status'],
                'published_at' => $validated['published_at'] ?? ($validated['status'] === 'published' && !$artikel->published_at ? now() : $artikel->published_at)
            ];

            if ($request->hasFile('image')) {
                if ($artikel->image) {
                    Storage::disk('public')->delete($artikel->image);
                }
                $data['image'] = $request->file('image')->store('articles', 'public');
            }

            $artikel->update($data);

            return redirect()->route('admin.website.artikel.index')
                ->with('success', 'Artikel berhasil diperbarui');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function destroy(Article $artikel)
    {
        try {
            if ($artikel->image) {
                Storage::disk('public')->delete($artikel->image);
            }
            
            $artikel->delete();
            
            return redirect()->route('website.artikel.index')
                ->with('success', 'Artikel berhasil dihapus');
        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan saat menghapus artikel');
        }
    }

    /**
     * Display the specified article.
     *
     * @param string $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        $article = Article::where('slug', $slug)
                         ->where('status', 'published') // Hanya artikel yang dipublish
                         ->firstOrFail();
        return view('admin.website.artikel.show', compact('article')); // Ubah ke view publik
    }
}



