<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LaporanKerusakan extends Model
{
    use HasFactory;
    
    protected $table = 'laporan_kerusakan';
    
    protected $fillable = [
        'judul',
        'lokasi',
        'deskripsi',
        'status',
        'tindakan',
        'pelapor_id',
        'penindak_id',
        'tanggal_lapor',
        'tanggal_proses',
        'tanggal_selesai',
        'unit_id',
        'sarana_id',
    ];
    
    protected $casts = [
        'tanggal_lapor' => 'datetime',
        'tanggal_proses' => 'datetime',
        'tanggal_selesai' => 'datetime',
    ];
    
    /**
     * Get the user who reported the issue.
     */
    public function pelapor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'pelapor_id');
    }
    
    /**
     * Get the user who handled the issue.
     */
    public function penindak(): BelongsTo
    {
        return $this->belongsTo(User::class, 'penindak_id');
    }
    
    /**
     * Get the unit associated with this report.
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }
    
    /**
     * Get the sarana (facility) associated with this report.
     */
    public function sarana(): BelongsTo
    {
        return $this->belongsTo(Sarana::class);
    }
    
    /**
     * Get the photos associated with this report.
     */
    public function photos(): HasMany
    {
        return $this->hasMany(LaporanKerusakanPhoto::class);
    }
    
    /**
     * Get the comments associated with this report.
     */
    public function comments(): HasMany
    {
        return $this->hasMany(LaporanKerusakanComment::class);
    }
    
    /**
     * Scope a query to only include reports from a specific unit.
     */
    public function scopeFromUnit($query, $unitId)
    {
        return $query->where('unit_id', $unitId);
    }
    
    /**
     * Scope a query to only include reports with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }
    
    /**
     * Scope a query to only include reports from the current user.
     */
    public function scopeMyReports($query)
    {
        return $query->where('pelapor_id', auth()->id());
    }
    
    /**
     * Scope a query to only include reports assigned to the current user.
     */
    public function scopeAssignedToMe($query)
    {
        return $query->where('penindak_id', auth()->id());
    }
}


