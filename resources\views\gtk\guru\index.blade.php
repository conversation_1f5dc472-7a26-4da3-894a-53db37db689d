@extends('layouts.admin')

@section('title', 'Daftar Guru')

@section('content_header')
    <h1>Daftar Guru</h1>
@stop

@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Daftar Guru</h3>
        <div class="card-tools">
            {{-- <PERSON><PERSON><PERSON>an tombol tambah guru jika user memiliki permission, tanpa @can untuk debugging --}}
            <a href="{{ route('gtk.guru.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> <PERSON><PERSON> Guru
            </a>
            <div class="btn-group ml-2">
                <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-file-excel"></i> Import/Export
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="#" data-toggle="modal" data-target="#importModal">
                        <i class="fas fa-file-import"></i> Import Data
                    </a>
                    <a class="dropdown-item" href="{{ route('export.template.guru') }}">
                        <i class="fas fa-download"></i> Download Template
                    </a>
                </div>
            </div>
            {{-- Tampilkan informasi permission untuk debugging --}}
            <small class="d-none">
                Has create-guru permission: {{ auth()->user()->can('create-guru') ? 'Yes' : 'No' }}
            </small>
        </div>
    </div>
    <div class="card-body">
        @php
            $user = auth()->user();
            $canViewAllUnits = $user->hasAnyRole(['Administrator', 'Yayasan', 'Pengawas']);
        @endphp
        
        {{-- Debug info --}}
        <!--
        <div class="alert alert-info">
            <h5><i class="icon fas fa-info"></i> Informasi User</h5>
            <p>User: {{ $user->name }}</p>
            <p>Unit: {{ $user->unit->nama_unit ?? 'Tidak ada unit' }}</p>
            <p>Role: {{ implode(', ', $user->getRoleNames()->toArray()) }}</p>
            <p>Jumlah data guru: {{ count($guru) }}</p>
        </div> -->
        
        @if($canViewAllUnits)
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="filter-unit">Filter Unit:</label>
                    <select id="filter-unit" class="form-control">
                        <option value="">Semua Unit</option>
                        @foreach($units as $unit)
                            <option value="{{ $unit->nama_unit }}">{{ $unit->nama_unit }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
        @else
        <!--
        <div class="alert alert-info">
            <h5><i class="icon fas fa-info"></i> Informasi Unit</h5>
            <p>Anda melihat data guru dari unit: <strong>{{ $user->unit->nama_unit ?? 'Tidak ada unit' }}</strong></p>
        </div> -->
        @endif
        
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="guru-table">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Nama</th>
                        <th>Unit</th>
                        <th>NIP/NIY</th>
                        <th>NUPTK</th>
                        <th>Jenis Kelamin</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($guru as $index => $g)
                    <tr>
                        <td>{{ $index + 1 }}</td>
                        <td>{{ $g->nama }}</td>
                        <td>{{ $g->unit->nama_unit ?? '-' }}</td>
                        <td>{{ $g->nip ?? $g->niy ?? '-' }}</td>
                        <td>{{ $g->nuptk ?? '-' }}</td>
                        <td>{{ $g->jenis_kelamin == 'L' ? 'Laki-laki' : 'Perempuan' }}</td>
                        <td>
                            @if($g->status == 'Aktif')
                                <span class="badge badge-success">Aktif</span>
                            @else
                                <span class="badge badge-danger">Non-Aktif</span>
                            @endif
                        </td>
                        <td>
                            <a href="{{ route('gtk.guru.show', $g->id) }}" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i> Detail
                            </a>
                            @if(auth()->user()->can('edit-guru'))
                            <a href="{{ route('gtk.guru.edit', $g->id) }}" class="btn btn-sm btn-warning">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            @endif
                            @if(auth()->user()->can('delete-guru'))
                            <form action="{{ route('gtk.guru.destroy', $g->id) }}" method="POST" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Apakah Anda yakin ingin menghapus guru ini?')">
                                    <i class="fas fa-trash"></i> Hapus
                                </button>
                            </form>
                            @endif
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center">Tidak ada data guru</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@stop

<!-- Modal Import Data Guru -->
<div class="modal fade" id="importModal" tabindex="-1" role="dialog" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">Import Data Guru</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('gtk.guru.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="file">Pilih File Excel</label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="file" name="file" required accept=".xlsx, .xls, .csv">
                            <label class="custom-file-label" for="file">Pilih file...</label>
                        </div>
                        <small class="form-text text-muted">
                            Format file: .xlsx, .xls, .csv. Maksimal ukuran: 2MB.
                        </small>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Pastikan format data sesuai dengan template yang disediakan.
                        <a href="{{ route('export.template.guru') }}" class="alert-link">Download template</a> jika belum memilikinya.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-file-import"></i> Import Data
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section('js')
<script>
    $(document).ready(function() {
        // Inisialisasi DataTable
        var table = $('#guru-table').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
            }
        });
        
        // Filter berdasarkan unit (untuk admin, yayasan, pengawas)
        $('#filter-unit').on('change', function() {
            var unitName = $(this).val();
            table.column(2) // Kolom unit (indeks 2)
                .search(unitName)
                .draw();
        });
        
        // Custom file input
        $(".custom-file-input").on("change", function() {
            var fileName = $(this).val().split("\\").pop();
            $(this).siblings(".custom-file-label").addClass("selected").html(fileName);
        });
    });
</script>
@stop






