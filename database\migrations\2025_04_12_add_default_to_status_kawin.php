<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('guru', function (Blueprint $table) {
            $table->string('status_kawin', 50)->default('Belum Kawin')->change();
        });
    }

    public function down()
    {
        Schema::table('guru', function (Blueprint $table) {
            $table->string('status_kawin', 50)->change();
        });
    }
};