@extends('adminlte::page')

@section('title', $article->title)

@section('content_header')
    <h1>Detail Artikel</h1>
@stop

@section('content')
<div class="card">
    <div class="card-body">
        <div class="mb-3">
            <a href="{{ route('admin.website.artikel.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="{{ route('admin.website.artikel.edit', $article->id) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
        </div>

        <div class="article-header mb-4">
            <h2>{{ $article->title }}</h2>
            <div class="text-muted">
                <small>
                Dipublish oleh {{ $article->author->name }} pada {{ $article->created_at->format('d M Y H:i') }}
                </small>
            </div>
        </div>

        @if($article->image)
            <div class="article-image mb-4">
                <img src="{{ asset('storage/' . $article->image) }}" 
                     class="img-fluid rounded" 
                     alt="{{ $article->title }}">
            </div>
        @endif

        <div class="article-content">
            {!! $article->content !!}
        </div>

        <div class="article-meta mt-4">
            <div class="row">
                <div class="col-md-6">
                    <strong>Status:</strong> 
                    <span class="badge badge-{{ $article->status === 'published' ? 'success' : 'warning' }}">
                        {{ ucfirst($article->status) }}
                    </span>
                </div>
                <div class="col-md-6">
                    <strong>Dipublikasikan:</strong> 
                    {{ $article->published_at ? $article->published_at->format('d M Y H:i') : 'Belum dipublikasikan' }}
                </div>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
    <style>
        .article-content {
            line-height: 1.6;
        }
        .article-content img {
            max-width: 100%;
            height: auto;
        }
    </style>
@stop
