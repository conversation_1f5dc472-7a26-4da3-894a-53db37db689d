<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class PesertaDidikTemplateExport implements FromArray, WithHeadings, WithStyles, ShouldAutoSize
{
    /**
     * @return array
     */
    public function array(): array
    {
        // Contoh data untuk template
        return [
            [
                'NIS123', 'NISN123', '1234567890123456', 'Dharma', 'L', 'Duri', '2005-01-01', 'Buddha', 'Tidak', 
                'Jl. Contoh No. 123', '001', '002', '<PERSON>sun A', '<PERSON><PERSON><PERSON><PERSON> B', 'Kecamatan C', '12345',
                '<PERSON><PERSON><PERSON>', 'Sepeda', '021-1234567', '08123456789', '<EMAIL>', '1', 'SMP',
                'Reguler', 'REG001', '12345678', 'SMP Pelopor', 'IJZ123', 'SKHUN123', 'UN123',
                '170', '60', '5', '15', '2', 'KKS123', 'Ya', 'KPS123', 'Ya', 'Ya',
                'Nama Ayah', '1234567890123456', '1975', 'S1', 'PNS', '5-10 Juta', 'Tidak Ada',
                'Nama Ibu', '1234567890123456', '1980', 'S1', 'Guru', '5-10 Juta', 'Tidak Ada',
                'Nama Wali', '1234567890123456', '1970', 'S1', 'Wiraswasta', '5-10 Juta'
            ]
        ];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'nis', 'nisn', 'nik', 'nama', 'jenis_kelamin', 'tempat_lahir', 'tanggal_lahir', 'agama', 'kebutuhan_khusus',
            'alamat', 'rt', 'rw', 'dusun', 'kelurahan', 'kecamatan', 'kode_pos',
            'jenis_tinggal', 'transportasi', 'no_telp', 'no_handphone', 'email',
            'kelas_id', 'tingkat', 'nama_program', 'no_reg', 'npsn', 'sekolah_asal', 'seri_ijasah', 'seri_skhun', 'no_unas',
            'tinggi_badan', 'berat_badan', 'jarak_rumah', 'waktu_tempuh', 'jumlah_saudara',
            'no_kks', 'status_kps', 'no_ksp', 'status_pip', 'usulan_pip',
            'nama_ayah', 'NIK_ayah', 'tahun_lahir_ayah', 'pendidikan_ayah', 'pekerjaan_ayah', 'penghasilan_ayah', 'kebutuhan_khusus_ayah',
            'nama_ibu', 'NIK_ibu', 'tahun_lahir_ibu', 'pendidikan_ibu', 'pekerjaan_ibu', 'penghasilan_ibu', 'kebutuhan_khusus_ibu',
            'nama_wali', 'NIK_wali', 'tahun_lahir_wali', 'pendidikan_wali', 'pekerjaan_wali', 'penghasilan_wali',
            'unit_id'
        ];
    }

    /**
     * @param Worksheet $sheet
     */
    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}

