@extends('layouts.website')

@section('title', 'Pelopor')

@section('content')
<!-- Tambahkan style untuk hero section saja, hapus style navbar -->
<style>
/* Hero Section */
.hero-section {
    position: relative;
    margin-top: 0; /* <PERSON><PERSON> dari -4rem menjadi 0 */
}

/* Style lainnya tetap sama seperti sebelumnya */
.carousel-caption {
    background: rgba(0, 0, 0, 0.6);
    padding: 2rem;
    border-radius: 10px;
    max-width: 800px;
    margin: 0 auto;
}

.animated-caption {
    animation: fadeInUp 1s ease-out;
}

/* Welcome Section */
.welcome-section {
    background: linear-gradient(135deg,rgb(255, 255, 255) 0%, #f8f9fa 100%);
}

.principal-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.principal-image-container:hover {
    transform: scale(1.02);
}

/* Section Styling */
.section-title {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 1rem;
    position: relative;
}

.title-underline {
    height: 4px;
    width: 100px;
    background: linear-gradient(to right, #3498db, #2ecc71);
    margin-bottom: 2rem;
    border-radius: 2px;
}

/* Feature Cards setelah slide */
.feature-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-title {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.feature-text {
    color: #666;
    line-height: 1.6;
}

/* Article Cards */
.article-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.article-card:hover {
    transform: translateY(-5px);
}

.article-image {
    position: relative;
    overflow: hidden;
}

.article-image img {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.article-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.article-card:hover .article-overlay {
    opacity: 1;
}

/* Achievement Cards */
.achievement-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.achievement-card:hover {
    transform: translateY(-5px);
}

.achievement-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #f1c40f;
    font-size: 2rem;
    z-index: 1;
}

.achievement-image {
    height: 200px;
    object-fit: cover;
}

/* Extracurricular Cards */
.extra-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.extra-card:hover {
    transform: translateY(-5px);
}

.extra-icon img {
    width: 64px;
    height: 64px;
    object-fit: contain;
    margin-bottom: 1rem;
}

/* Facilities Section Styling */
.facilities-section {
    background-color: #ffffff;
}

.facility-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    height: 100%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.facility-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: #F0973F;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.facility-card:hover {
    transform: translateY(-5px);
}

.facility-card:hover::before {
    transform: scaleX(1);
}

.facility-icon {
    font-size: 2.5rem;
    color: #F0973F;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease;
}

.facility-card:hover .facility-icon {
    transform: scale(1.1);
}

.facility-content h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.facility-content p {
    color: #666;
    margin: 0;
    font-size: 0.95rem;
    line-height: 1.6;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .carousel-item img {
        height: 400px;
    }

    .carousel-caption {
        padding: 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .facility-card {
        padding: 1.5rem;
    }
    
    .facility-icon {
        font-size: 2rem;
    }
    
    .facility-content h4 {
        font-size: 1.1rem;
    }
    
    .facility-content p {
        font-size: 0.9rem;
    }
}
</style>

<!-- Hero Section -->
<div class="hero-section">
    <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="3000">
        <div class="carousel-inner">
            @foreach($slides as $slide)
            <div class="carousel-item {{ $loop->first ? 'active' : '' }}">
                <img src="{{ Storage::url($slide->image) }}" 
                     class="d-block w-100" 
                     alt="{{ $slide->title }}"
                     style="height: 600px; object-fit: cover;">
                <div class="carousel-caption animated-caption">
                    <h1 class="display-4 fw-bold">{{ $slide->title }}</h1>
                    <p class="lead">{{ $slide->description }}</p>
                </div>
            </div>
            @endforeach
        </div>
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon"></span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon"></span>
        </button>
    </div>
</div>

<!-- Features Section (replacing Sambutan Kepala Sekolah) -->
<section class="welcome-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="feature-card text-center p-4 h-100">
                    <i class="fas fa-chalkboard-teacher mb-3" style="font-size: 3rem; color: #F0973F;"></i>
                    <h3 class="feature-title h4">Guru yang Berpengalaman</h3>
                    <p class="feature-text">Dengan SDM yang berpengalaman kami berdedikasi untuk memberikan yang terbaik kepada para Siswa/i.</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="feature-card text-center p-4 h-100">
                    <i class="fas fa-award mb-3" style="font-size: 3rem; color: #F0973F;"></i>
                    <h3 class="feature-title h4">Terakreditasi</h3>
                    <p class="feature-text">Sekolah kami terakreditasi dan diakui oleh pemerintah sehingga anda tidak perlu ragu dengan kualitas kami.</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="feature-card text-center p-4 h-100">
                    <i class="fas fa-building mb-3" style="font-size: 3rem; color: #F0973F;"></i>
                    <h3 class="feature-title h4">Fasilitas yang Lengkap</h3>
                    <p class="feature-text">Menyediakan berbagai macam fasilitas sekolah yang dapat digunakan oleh para siswa/i untuk mengambangkan bakatnya.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Artikel Terbaru -->
<section class="articles-section bg-light py-5">
    <div class="container">
        <h2 class="section-title text-center">Artikel Terbaru</h2>
        <div class="title-underline mx-auto"></div>
        <div class="row">
            @foreach($articles as $article)
            <div class="col-md-4 mb-4">
                <div class="article-card h-100">
                    <div class="article-image">
                        <img src="{{ Storage::url($article->image) }}" class="card-img-top" alt="{{ $article->title }}">
                        <div class="article-overlay">
                            <a href="{{ route('website.artikel.show', $article->slug) }}" class="btn btn-light">Baca Selengkapnya</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="article-title">{{ $article->title }}</h5>
                        <p class="article-excerpt">{{ Str::limit($article->excerpt, 100) }}</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Prestasi -->
<section class="achievements-section py-5">
    <div class="container">
        <h2 class="section-title text-center">Prestasi Terbaru</h2>
        <div class="title-underline mx-auto"></div>
        <div class="row">
            @foreach($achievements as $achievement)
            <div class="col-md-3 mb-4">
                <div class="achievement-card h-100">
                    <div class="achievement-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <img src="{{ Storage::url('prestasi/' . $achievement->image) }}" class="card-img-top achievement-image" alt="{{ $achievement->title }}">
                    <div class="card-body">
                        <h5 class="achievement-title">{{ $achievement->title }}</h5>
                        <p class="achievement-description">{{ $achievement->description }}</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Ekstrakurikuler -->
<section class="extracurricular-section bg-light py-5">
    <div class="container">
        <h2 class="section-title text-center">Ekstrakurikuler</h2>
        <div class="title-underline mx-auto"></div>
        
        <div id="extraCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="3000">
            <div class="carousel-inner">
                @foreach($ekstrakurikuler as $key => $extra)
                    <div class="carousel-item {{ $key < 4 ? 'active' : '' }}">
                        <div class="row">
                            @for($i = $key; $i < min($key + 4, count($ekstrakurikuler)); $i++)
                                <div class="col-md-3">
                                    <div class="extra-card h-100 text-center">
                                        <div class="extra-icon">
                                            @if($ekstrakurikuler[$i]->gambar)
                                                <img src="{{ Storage::url($ekstrakurikuler[$i]->gambar) }}" 
                                                     class="mx-auto rounded" 
                                                     alt="{{ $ekstrakurikuler[$i]->nama }}"
                                                     style="width: 200px; height: 200px; object-fit: cover;">
                                            @else
                                                <div class="no-image-placeholder">
                                                    <i class="fas fa-image" style="font-size: 64px; color: #ccc;"></i>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="card-body">
                                            <h5 class="extra-title">{{ $ekstrakurikuler[$i]->nama }}</h5>
                                            <p class="card-text">{{ Str::limit($ekstrakurikuler[$i]->deskripsi, 100) }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endfor
                        </div>
                    </div>
                @endforeach
            </div>
            
            <button class="carousel-control-prev" type="button" data-bs-target="#extraCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" style="background-color: #F0973F; border-radius: 50%;"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#extraCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" style="background-color: #F0973F; border-radius: 50%;"></span>
                <span class="visually-hidden">Next</span>
            </button>
        </div>
    </div>
</section>

<!-- Fasilitas Section -->
<section class="facilities-section py-5">
    <div class="container">
        <h2 class="section-title text-center">Fasilitas</h2>
        <div class="title-underline mx-auto"></div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="facility-card">
                    <div class="facility-icon">
                        <i class="fas fa-book-reader"></i>
                    </div>
                    <div class="facility-content">
                        <h4>Perpustakaan</h4>
                        <p>Perpustakaan modern dengan koleksi buku yang lengkap dan area belajar yang nyaman.</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="facility-card">
                    <div class="facility-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div class="facility-content">
                        <h4>Laboratorium</h4>
                        <p>Lab IPA, Komputer, dan Bahasa yang dilengkapi dengan peralatan modern.</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="facility-card">
                    <div class="facility-icon">
                        <i class="fas fa-futbol"></i>
                    </div>
                    <div class="facility-content">
                        <h4>Sarana Olahraga</h4>
                        <p>Lapangan olahraga multifungsi dan fasilitas olahraga lengkap.</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="facility-card">
                    <div class="facility-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <div class="facility-content">
                        <h4>Greenhouse</h4>
                        <p>Fasilitas greenhouse untuk pembelajaran botani dan mata pelajaran terkait lingkungan, pertanian, dan biologi.</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="facility-card">
                    <div class="facility-icon">
                        <i class="fas fa-clinic-medical"></i>
                    </div>
                    <div class="facility-content">
                        <h4>UKS</h4>
                        <p>Unit Kesehatan Sekolah dengan peralatan medis dan tenaga kesehatan.</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="facility-card">
                    <div class="facility-icon">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div class="facility-content">
                        <h4>Wifi Area</h4>
                        <p>Koneksi internet cepat untuk mendukung pembelajaran digital.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@endsection
    
                                        </g>
                                    </svg>
                                </div>


                            </div>
                        </div>
                    </main>


                </div>
            </div>
        </div>
    </body>
</html>





