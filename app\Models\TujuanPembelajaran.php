<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TujuanPembelajaran extends Model
{
    use HasFactory;

    protected $fillable = [
        'cakapan_pembelajaran_id',
        'deskripsi',
    ];

    public function cakapanPembelajaran()
    {
        return $this->belongsTo(CakapanPembelajaran::class);
    }

    public function penilaianFormatifs()
    {
        return $this->hasMany(PenilaianFormatif::class);
    }
}