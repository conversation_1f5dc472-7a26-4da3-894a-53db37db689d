<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Auth;
use App\Models\Jenjang;

class ViewServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Tambahkan variabel unit ke semua view
        View::composer('*', function ($view) {
            if (Auth::check()) {
                $user = Auth::user();
                $unit = $user->unit;
                
                $view->with('unit', $unit);
            }
        });
        
        // Tambahkan variabel lain yang mungkin diperlukan
        View::composer('jurnal.*', function ($view) {
            // Tambahkan variabel khusus untuk view jurnal
            if (!array_key_exists('isAdmin', $view->getData())) {
                $isAdmin = Auth::check() && Auth::user()->hasAnyRole(['Administrator', 'Admin', '<PERSON><PERSON><PERSON>kolah', 'Waka Kurikulum']);
                $view->with('isAdmin', $isAdmin);
            }
        });
    }
}
