<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AbsensiPermissionSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles dan permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Daftar permission baru untuk absensi
        $newPermissions = [
            'view-absensi',         // Untuk melihat data absensi
            'manage-absensi',       // Untuk mengelola absensi
            'create-absensi',       // Untuk membuat absensi baru
            'edit-absensi',         // Untuk mengedit absensi
            'delete-absensi',       // Untuk menghapus absensi
            'view-rekap-absensi',   // Untuk melihat rekap absensi
            'view-rekap-admin',     // Untuk melihat rekap admin (khusus admin/pimpinan)
        ];

        // Buat permission baru jika belum ada
        foreach ($newPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permission ke role yang sesuai
        $rolePermissions = [
            'Administrator' => [
                'view-absensi',
                'manage-absensi',
                'create-absensi',
                'edit-absensi',
                'delete-absensi',
                'view-rekap-absensi',
                'view-rekap-admin',
            ],
            'Kepala Sekolah' => [
                'view-absensi',
                'view-rekap-absensi',
                'view-rekap-admin',
            ],
            'Waka Kurikulum' => [
                'view-absensi',
                'view-rekap-absensi',
                'view-rekap-admin',
            ],
            'Waka Kesiswaan' => [
                'view-absensi',
                'view-rekap-absensi',
                'view-rekap-admin',
            ],
            'Guru' => [
                'view-absensi',
                'create-absensi',
                'edit-absensi',
                'view-rekap-absensi',
            ],
            'Tata Usaha' => [
                'view-absensi',
                'manage-absensi',
                'create-absensi',
                'edit-absensi',
                'view-rekap-absensi',
                'view-rekap-admin',
            ],
            'Wali Kelas' => [
                'view-absensi',
                'create-absensi',
                'edit-absensi',
                'view-rekap-absensi',
            ],
        ];

        // Assign permission ke role tanpa menghapus permission yang sudah ada
        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Role::firstOrCreate(['name' => $roleName]);
            foreach ($permissions as $permission) {
                if (!$role->hasPermissionTo($permission)) {
                    $role->givePermissionTo($permission);
                }
            }
        }
    }
}