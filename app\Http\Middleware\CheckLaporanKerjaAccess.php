<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckLaporanKerjaAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        
        // Cek akses lihat laporan
        if ($request->routeIs('laporan-kerja.index') || $request->routeIs('laporan-kerja.show')) {
            if (!$user->hasPermissionTo('lihat-laporan-kerja')) {
                return redirect()->route('dashboard')
                    ->with('error', 'Anda tidak memiliki izin untuk melihat laporan kerja.');
            }
        }
        
        // Cek akses kelola laporan
        if ($request->routeIs('laporan-kerja.create') || 
            $request->routeIs('laporan-kerja.store') || 
            $request->routeIs('laporan-kerja.edit') || 
            $request->routeIs('laporan-kerja.update') || 
            $request->routeIs('laporan-kerja.destroy')) {
            if (!$user->hasPermissionTo('kelola-laporan-kerja')) {
                return redirect()->route('dashboard')
                    ->with('error', 'Anda tidak memiliki izin untuk mengelola laporan kerja.');
            }
        }

        return $next($request);
    }
}