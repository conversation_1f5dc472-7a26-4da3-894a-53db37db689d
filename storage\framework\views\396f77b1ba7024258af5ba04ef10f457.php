<?php $__env->startSection('content'); ?>
<style>
/* ===== STYLING UNTUK HALAMAN EKSTRAKURIKULER  ===== */

/* Container utama dengan background gradient */
.ekstrakurikuler-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

/* Styling untuk judul halaman */
.page-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* Styling untuk card ekstrakurikuler */
.ekskul-card {
    background: white;
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    overflow: hidden;
    position: relative;
    height: 100%;
}

.ekskul-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
} 

.ekskul-card:hover::before {
    transform: scaleX(1);
}

.ekskul-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Styling untuk gambar card */
.ekskul-image {
    height: 220px;
    object-fit: cover;
    transition: transform 0.4s ease;
    border-radius: 0;
}

.ekskul-card:hover .ekskul-image {
    transform: scale(1.05);
}

/* Container gambar tanpa overlay */
.image-container {
    position: relative;
    overflow: hidden;
    border-radius: 20px 20px 0 0;
}

/* Styling untuk body card */
.ekskul-card-body {
    padding: 1.5rem;
    background: white;
}

.ekskul-title {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.25rem;
    margin-bottom: 1rem;
    transition: color 0.3s ease;
}

.ekskul-card:hover .ekskul-title {
    color: #667eea;
}

.ekskul-description {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Styling untuk placeholder jika tidak ada gambar */
.no-image-placeholder {
    height: 220px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 3rem;
    border-radius: 20px 20px 0 0;
}

/* Styling untuk empty state */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

.empty-state h3 {
    color: #495057;
    margin-bottom: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .ekstrakurikuler-container {
        padding: 1rem 0;
    }

    .ekskul-image,
    .no-image-placeholder {
        height: 180px;
    }

    .ekskul-card-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .page-title {
        font-size: 1.75rem;
    }

    .ekskul-image,
    .no-image-placeholder {
        height: 160px;
    }
}
</style>

<div class="ekstrakurikuler-container">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <?php echo $__env->make('website.partials._sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <h1 class="page-title">Ekstrakurikuler <?php echo e(strtoupper($jenjang)); ?></h1>

                <?php if($ekstrakurikulers->count() > 0): ?>
                    <div class="row">
                        <?php $__currentLoopData = $ekstrakurikulers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ekskul): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-6 col-md-12 mb-4">
                                <div class="card ekskul-card">
                                    <div class="image-container">
                                        <?php if($ekskul->gambar): ?>
                                            <img src="<?php echo e(Storage::url($ekskul->gambar)); ?>"
                                                 class="card-img-top ekskul-image"
                                                 alt="<?php echo e($ekskul->nama); ?>">
                                        <?php else: ?>
                                            <div class="no-image-placeholder">
                                                <i class="fas fa-image"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="card-body ekskul-card-body">
                                        <h5 class="card-title ekskul-title"><?php echo e($ekskul->nama); ?></h5>
                                        <p class="card-text ekskul-description"><?php echo e($ekskul->deskripsi); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-users"></i>
                        <h3>Belum Ada Ekstrakurikuler</h3>
                        <p>Ekstrakurikuler untuk jenjang <?php echo e(strtoupper($jenjang)); ?> belum tersedia.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.website', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/website/ekstrakurikuler/jenjang.blade.php ENDPATH**/ ?>